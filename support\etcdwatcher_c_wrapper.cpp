#include "etcdwatcher.h"
#include "etcdwatcher_c_wrapper.h"
#include "etcdapi.h"

extern "C" {

int etcdwatcher_init_c(void)
{
    return etcdwatcher_init();
}

int etcdwatcher_start_watch_c(const char *key, etcdwatcher_event_callback_c cb)
{
    return etcdwatcher_start_watch(key, (etcdwatcher_event_callback)cb);
}

int etcdwatcher_destroy_c(void)
{
    return etcdwatcher_destroy();
}

int get_db_host_c(char *host, size_t host_size)
{
	return get_db_host(host, host_size);
}

} // extern "C"

