/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright (C) Panasas Inc., 2013
 * Author: <PERSON> j<PERSON>@panasas.com
 *
 * contributeur : <PERSON>   <EMAIL>
 *                Thomas LEIBOVICI  <EMAIL>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

/**
 * @defgroup clntmmt Client management
 * @{
 */

/**
 * @file client_mgr.c
 * <AUTHOR> <<EMAIL>>
 * @brief Protocol client manager
 */

#include "config.h"

#include <time.h>
#include <unistd.h>
#include <sys/socket.h>
#ifdef RPC_VSOCK
#include <linux/vm_sockets.h>
#endif /* VSOCK */
#include <sys/types.h>
#include <sys/param.h>
#include <pthread.h>
#include <assert.h>
#include <arpa/inet.h>
#include "gsh_list.h"
#include "fsal.h"
#include "nfs_core.h"
#include "log.h"
#include "avltree.h"
#include "gsh_types.h"
#ifdef USE_DBUS
#include "gsh_dbus.h"
#endif
#include "tenant_mgr.h"
#include "export_mgr.h"
#include "server_stats_private.h"
#include "abstract_atomic.h"
#include "gsh_intrinsic.h"
#include "server_stats.h"
#include "sal_functions.h"
#include "client_mgr.h"
uint64_t hash_tenant_name(const char *str, size_t length) {
	const uint64_t fnv_prime = 1099511628211ULL;
	const uint64_t fnv_offset_basis = 14695981039346656037ULL;
	uint64_t hash = fnv_offset_basis;
	const unsigned char *p = (const unsigned char *)str;
	const unsigned char *end = p + length;

	while (p != end) {
		hash ^= *p++;
		hash *= fnv_prime;
	}

	return hash;
}
extern struct tenant_by_name tenant_by_name;
static inline int ename_cache_offsetof(struct tenant_by_name *eid, uint64_t k)
{
	return k % eid->cache_sz;
}

int string_name_cmpf(const char *name1, const char *name2, bool ignore_case)
{
	if (ignore_case) {
		/* Case-Insensitive String Comparison */
		return strcasecmp(name1, name2);
	} else {
		/* Case-Sensitive String Comparison */
		return strcmp(name1, name2);
	}
}

/**
 * @brief IP address comparator for AVL tree walk
 *
 * We tell the difference between IPv4 and IPv6 addresses
 * by size (4 vs. 16). IPv4 addresses are "lower", left, sorted
 * first.
 */

static int tenant_client_ip_cmpf(const struct avltree_node *lhs,
			  const struct avltree_node *rhs)
{
	struct tenant_client *lk, *rk;

	lk = avltree_container_of(lhs, struct tenant_client, node_k);
	rk = avltree_container_of(rhs, struct tenant_client, node_k);

	return sockaddr_cmpf(&lk->cl_addrbuf, &rk->cl_addrbuf, true);
}

static int tenant_name_cmpf(const struct avltree_node *lhs,
			  const struct avltree_node *rhs)
{
	struct gsh_tenant *lk, *rk;

	lk = avltree_container_of(lhs, struct gsh_tenant, node_k);
	rk = avltree_container_of(rhs, struct gsh_tenant, node_k);
	return string_name_cmpf(lk->tenant_name, rk->tenant_name, true);
}

struct gsh_tenant *get_gsh_tenant(const char *tenant_name,  bool lookup_only)
{
	struct avltree_node *node = NULL;
	struct gsh_tenant *tenant = NULL;
	struct gsh_tenant v;
	void **cache_slot;
	if(strlen(tenant_name) == 0){
		return NULL;
	}
	memcpy(&v.tenant_name, tenant_name, sizeof(v.tenant_name));
	uint64_t hash = hash_tenant_name(tenant_name, strlen(tenant_name));

	PTHREAD_RWLOCK_rdlock(&tenant_by_name.lock);

	/* check cache */
	cache_slot = (void **)
	    &(tenant_by_name.cache[ename_cache_offsetof(&tenant_by_name, hash)]);
	node = (struct avltree_node *)atomic_fetch_voidptr(cache_slot);
	if (node) {
		if (tenant_name_cmpf(&v.node_k, node) == 0) {
			/* got it in 1 */
			LogDebug(COMPONENT_HASHTABLE_CACHE,
				 "tenant_mgr cache hit slot %d",
				 ename_cache_offsetof(&tenant_by_name, hash));
			tenant = avltree_container_of(node, struct gsh_tenant,
						  node_k);
			goto out;
		}
	}

	/* fall back to AVL */
	node = avltree_lookup(&v.node_k, &tenant_by_name.t);
	if (node) {
		tenant = avltree_container_of(node, struct gsh_tenant, node_k);
		/* update cache */
		atomic_store_voidptr(cache_slot, node);
		goto out;
	} else if (lookup_only) {
		PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);
		return NULL;
	}
	PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);

	/* Create a New Tenant */
	tenant = gsh_calloc(1, sizeof(*tenant));
	if(tenant == NULL ){
		LogEvent(COMPONENT_FSAL, "tenant gsh_calloc error, tenant name :%s", tenant_name);
		return NULL;
	}
	strncpy(tenant->tenant_name, tenant_name, sizeof(tenant->tenant_name) - 1);
	tenant->refcnt = 1;
	(void) tenant_client_pkginit(tenant);
	LogEvent(COMPONENT_FSAL, "add new tenant, tenant_name:%s, tenant %p tenant path :%s, tenant refcnt %ld", 
		tenant_name, tenant, tenant->tenant_name, tenant->refcnt);

	PTHREAD_RWLOCK_wrlock(&tenant_by_name.lock);
	node = avltree_insert(&tenant->node_k, &tenant_by_name.t);
	if (node) {
		gsh_free(tenant);	/* somebody beat us to it */
		tenant = NULL;
		tenant = avltree_container_of(node, struct gsh_tenant, node_k);
	} else {
		PTHREAD_RWLOCK_init(&tenant->lock, NULL);
		/* update cache */
		atomic_store_voidptr(cache_slot, &tenant->node_k);
	}

 out:
	/* we will hold a ref starting out... */
	inc_sys_tenant_refcount(tenant);
	LogDebug(COMPONENT_FSAL, "tenant_name:%s, tenant %p tenant path :%s, tenant refcnt %ld", tenant_name, tenant, tenant->tenant_name, tenant->refcnt);
	PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);
	return tenant;
}

/**
 * @brief Remove a tenant from the AVL and free its resources
 *
 * @param tenant_name [IN]  to remove
 *
 * @retval 0 if removed
 * @retval ENOENT if not found
 * @retval EBUSY if in use
 */

int remove_gsh_tenant(const char *tenant_name)
{
	struct avltree_node *node = NULL;
	struct avltree_node *cnode = NULL;
	struct gsh_tenant *tenant = NULL;
	struct gsh_tenant v;
	int removed = 0;
	void **cache_slot;

	if(strlen(tenant_name) == 0){
		return ENOENT;
	}
	memcpy(&v.tenant_name, tenant_name, sizeof(v.tenant_name));
	uint64_t hash = hash_tenant_name(tenant_name, strlen(tenant_name));

	PTHREAD_RWLOCK_rdlock(&tenant_by_name.lock);

	node = avltree_lookup(&v.node_k, &tenant_by_name.t);
	if (node) {
		tenant = avltree_container_of(node, struct gsh_tenant, node_k);
		if (atomic_fetch_int64_t(&tenant->refcnt) > 1) {
			removed = EBUSY;
			LogEvent(COMPONENT_FSAL, "tenant_name:%s, tenant refcnt %ld",tenant_name, tenant->refcnt);
			goto out;
		}
		cache_slot = (void **)
		    &(tenant_by_name.cache[ename_cache_offsetof(
						&tenant_by_name, hash)]);
		cnode = (struct avltree_node *)atomic_fetch_voidptr(cache_slot);
		if (node == cnode)
			atomic_store_voidptr(cache_slot, NULL);
		avltree_remove(node, &tenant_by_name.t);
	} else {
		removed = ENOENT;
	}
 out:
	PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);
	if (removed == 0) {
		free(tenant);
	}
	return removed;
}

/**
 * @ Walk the tree and do the callback on each node
 *
 * @param cb    [IN] Callback function
 * @param state [IN] param block to pass
 */

int foreach_gsh_tenant(bool(*cb) (struct gsh_tenant *tenant, void *state),
		       void *state)
{
	struct avltree_node *tenant_node = NULL;
	struct gsh_tenant *tenant = NULL;
	int cnt = 0;

	PTHREAD_RWLOCK_rdlock(&tenant_by_name.lock);
	for (tenant_node = avltree_first(&tenant_by_name.t); tenant_node != NULL;
	     tenant_node = avltree_next(tenant_node)) {
		tenant = avltree_container_of(tenant_node, struct gsh_tenant,
					  node_k);
		if (!cb(tenant, state))
			break;
		cnt++;
	}
	PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);
	return cnt;
}

void tenant_pkginit(void)
{
	pthread_rwlockattr_t rwlock_attr;

	pthread_rwlockattr_init(&rwlock_attr);
#ifdef GLIBC
	pthread_rwlockattr_setkind_np(
		&rwlock_attr,
		PTHREAD_RWLOCK_PREFER_WRITER_NONRECURSIVE_NP);
#endif
	PTHREAD_RWLOCK_init(&tenant_by_name.lock, &rwlock_attr);
	avltree_init(&tenant_by_name.t, tenant_name_cmpf, 0);
	tenant_by_name.cache_sz = 32767;
	tenant_by_name.cache =
	    gsh_calloc(tenant_by_name.cache_sz, sizeof(struct avltree_node *));
	pthread_rwlockattr_destroy(&rwlock_attr);
}

void put_gsh_tenant(struct gsh_tenant *tenant)
{
	int64_t new_refcnt;


	new_refcnt = atomic_dec_int64_t(&tenant->refcnt);
	assert(new_refcnt >= 0);
	if(tenant->refcnt == 1){
		int i = remove_gsh_tenant(tenant->tenant_name);
		LogEvent(COMPONENT_FSAL, "remove_gsh_tenant, i:%d", i);
	}

}

void put_tenant_client(struct tenant_client *client)
{
	int64_t new_refcnt = atomic_dec_int64_t(&client->refcnt);
	assert(new_refcnt >= 0);
	if(client->refcnt == 0){
		int i = remove_tenant_client(client->tenant, &client->cl_addrbuf);
		LogEvent(COMPONENT_FSAL, "remove_gsh_tenant, i:%d", i);
	}
}

static inline int eclient_cache_offsetof(struct gsh_tenant *t_tenant, uint64_t k)

{

	return k % t_tenant->client_cache_sz;

}
static inline int64_t inc_tenant_client_refcount(struct tenant_client *client)
{
	return atomic_inc_int64_t(&client->refcnt);
}

struct tenant_client *get_tenant_client(struct gsh_tenant *t_tenant, sockaddr_t *client_ipaddr, bool lookup_only)
{
	struct avltree_node *node = NULL;
	struct tenant_client *client;
	struct tenant_client v;
	void **cache_slot;
	uint64_t hash = hash_sockaddr(client_ipaddr, true);
	
	memcpy(&v.cl_addrbuf, client_ipaddr, sizeof(v.cl_addrbuf));

	PTHREAD_RWLOCK_rdlock(&t_tenant->client_lock);

	/* check cache */
	cache_slot = (void **)
		&(t_tenant->client_cache[eclient_cache_offsetof(t_tenant, hash)]);
	node = (struct avltree_node *)atomic_fetch_voidptr(cache_slot);
	if (node) {
		if (tenant_client_ip_cmpf(&v.node_k, node) == 0) {
		/* got it in 1 */
			LogDebug(COMPONENT_HASHTABLE_CACHE,
				"client_mgr cache hit slot %d",
				eclient_cache_offsetof(t_tenant, hash));
			client = avltree_container_of(node, struct tenant_client, node_k);
			goto out;
		}
	}

	/* fall back to AVL */
	node = avltree_lookup(&v.node_k, &t_tenant->client_tree);
	if (node) {
		client = avltree_container_of(node, struct tenant_client, node_k);
		/* update cache */
		atomic_store_voidptr(cache_slot, node);
		goto out;
	} else if (lookup_only) {
		PTHREAD_RWLOCK_unlock(&t_tenant->client_lock);
		return NULL;
	}
	PTHREAD_RWLOCK_unlock(&t_tenant->client_lock);

	/* Create a New Client */
	client = gsh_calloc(1, sizeof(*client));
	if(client == NULL ) {
		LogEvent(COMPONENT_FSAL, "client gsh_calloc error, client ip");
		PTHREAD_RWLOCK_unlock(&t_tenant->client_lock);
		return NULL;
	}
	
	client->cl_addrbuf = *client_ipaddr;
	client->refcnt = 1;
	memcpy(&client->cl_addrbuf, client_ipaddr, sizeof(client->cl_addrbuf));
	client->tenant = t_tenant;
	if (!sprint_sockip(client_ipaddr, client->hostaddr_str,
				   sizeof(client->hostaddr_str))) {
		(void) strlcpy(client->hostaddr_str, "<unknown>",
			sizeof(client->hostaddr_str));
	}

	LogDebug(COMPONENT_FSAL, "client ip:%s, client %p", client->hostaddr_str, client);

	/* insert into AVL */
	PTHREAD_RWLOCK_wrlock(&t_tenant->client_lock);
	node = avltree_insert(&client->node_k, &t_tenant->client_tree);
	if (node) {
		gsh_free(client);  /* somebody beat us to it */
		client = NULL;
		client = avltree_container_of(node, struct tenant_client, node_k);
	} else {
		PTHREAD_RWLOCK_init(&client->lock, NULL);
		/* update cache */
		atomic_store_voidptr(cache_slot, &client->node_k);
	}

out:
    /* we will hold a ref starting out... */
	inc_tenant_client_refcount(client);
	LogDebug(COMPONENT_FSAL, "client_id:%s, client %p t_tenant->tenant_name :%s, client refcnt %ld",
		client->hostaddr_str, client, t_tenant->tenant_name, client->refcnt);
	PTHREAD_RWLOCK_unlock(&t_tenant->client_lock);
	return client;
}

int remove_tenant_client(struct gsh_tenant *t_tenant, sockaddr_t *client_ipaddr)
{
	struct avltree_node *node = NULL;
	struct avltree_node *cnode = NULL;
	struct tenant_client *client = NULL;
	struct tenant_client v;
	int removed = 0;
	void **cache_slot;
	
	uint64_t hash = hash_sockaddr(client_ipaddr, true);
	/* Copy the search address into the key */
	memcpy(&v.cl_addrbuf, client_ipaddr, sizeof(v.cl_addrbuf));

	PTHREAD_RWLOCK_wrlock(&t_tenant->client_lock);
	node = avltree_lookup(&v.node_k, &t_tenant->client_tree);
	if (node) {
		client = avltree_container_of(node, struct tenant_client, node_k);
		if (atomic_fetch_int64_t(&client->refcnt) > 1) {
			LogEvent(COMPONENT_FSAL, "client_ip:%s, client refcnt %ld", client->hostaddr_str, client->refcnt);
			removed = EBUSY;
			goto out;
		}
		cache_slot = (void **)
			&(t_tenant->client_cache[eclient_cache_offsetof(t_tenant, hash)]);
		cnode = (struct avltree_node *)atomic_fetch_voidptr(cache_slot);
		if (node == cnode)
			atomic_store_voidptr(cache_slot, NULL);
		avltree_remove(node, &t_tenant->client_tree);
	} else {
		removed = ENOENT;
	}
out:
	PTHREAD_RWLOCK_unlock(&t_tenant->client_lock);
	if (removed == 0) {
		free(client);
		client = NULL;
	}
	return removed;
}

int foreach_tenant_client(bool(*cb) (struct tenant_client *client, void *state), void *state, struct gsh_tenant *t_tenant)
{
	struct avltree_node *client_node;
	struct tenant_client *client;
	int cnt = 0;

	PTHREAD_RWLOCK_rdlock(&t_tenant->client_lock);
	for (client_node = avltree_first(&t_tenant->client_tree); client_node != NULL;
		client_node = avltree_next(client_node)) {
		client = avltree_container_of(client_node, struct tenant_client, node_k);
		if (!cb(client, state))
			break;
		cnt++;
	}
	PTHREAD_RWLOCK_unlock(&t_tenant->client_lock);
	return cnt;
}

void tenant_client_pkginit(struct gsh_tenant *t_tenant)
{
	pthread_rwlockattr_t rwlock_attr;

	pthread_rwlockattr_init(&rwlock_attr);
#ifdef GLIBC
	pthread_rwlockattr_setkind_np(
		&rwlock_attr,
		PTHREAD_RWLOCK_PREFER_WRITER_NONRECURSIVE_NP);
#endif
	PTHREAD_RWLOCK_init(&t_tenant->client_lock, &rwlock_attr);
	avltree_init(&t_tenant->client_tree, tenant_client_ip_cmpf, 0);

	t_tenant->client_cache_sz = 32767;
	t_tenant->client_cache = gsh_calloc(t_tenant->client_cache_sz, sizeof(struct avltree_node *));
	pthread_rwlockattr_destroy(&rwlock_attr);
}


