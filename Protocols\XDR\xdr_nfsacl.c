/*
 * The content of this file is a mix of rpcgen-generated
 * and hand-edited program text.  It is not automatically
 * generated by, e.g., build processes.
 *
 * This file is under version control.
 */
#include "nfs23.h"
#include "nfsacl.h"

bool_t
xdr_attr3 (XDR *xdrs, attr3 *objp)
{
	if (!xdr_bool(xdrs, &objp->attributes_follow))
		return FALSE;
	switch (objp->attributes_follow) {
	case TRUE:
		if (!xdr_fattr3(xdrs, &objp->attr3_u.obj_attributes))
			return FALSE;
		break;
	case FALSE:
		break;
	default:
		return FALSE;
	}
	return TRUE;
}

bool_t
xdr_posix_acl_entry (XDR *xdrs, posix_acl_entry *objp)
{
	if (!xdr_nfs3_uint32(xdrs, &objp->e_tag))
		return FALSE;
	if (!xdr_nfs3_uint32(xdrs, &objp->e_id))
		return FALSE;
	if (!xdr_nfs3_uint32(xdrs, &objp->e_perm))
		return FALSE;
	return TRUE;
}

bool_t
xdr_posix_acl (XDR *xdrs, posix_acl *objp)
{
	if (!xdr_nfs3_uint32(xdrs, &objp->count))
		return FALSE;
	if (!xdr_vector(xdrs, (char *)objp->entries, objp->count,
		sizeof(posix_acl_entry), (xdrproc_t) xdr_posix_acl_entry))
		return FALSE;
	return TRUE;
}

bool_t
xdr_getaclargs (XDR *xdrs, getaclargs *objp)
{
	if (!xdr_nfs_fh3(xdrs, &objp->fhandle))
		return FALSE;
	if (!xdr_nfs3_int32(xdrs, &objp->mask))
		return FALSE;
	return TRUE;
}

bool_t
xdr_getaclresok (XDR *xdrs, getaclresok *objp)
{
	if (!xdr_attr3(xdrs, &objp->attr))
		return FALSE;
	if (!xdr_nfs3_int32(xdrs, &objp->mask))
		return FALSE;
	if (!xdr_nfs3_uint32(xdrs, &objp->acl_access_count))
		return FALSE;
	if (objp->acl_access != NULL) {
		if (!xdr_reference(xdrs, (void **)&objp->acl_access,
			sizeof(posix_acl) +
			objp->acl_access_count * sizeof(posix_acl_entry),
			(xdrproc_t) xdr_posix_acl))
			return FALSE;
	} else {
		if (!xdr_pointer(xdrs, (void **)&objp->acl_access,
			sizeof(posix_acl) +
			objp->acl_access_count * sizeof(posix_acl_entry),
			(xdrproc_t) xdr_posix_acl))
			return FALSE;
	}
	if (!xdr_nfs3_uint32(xdrs, &objp->acl_default_count))
		return FALSE;
	if (objp->acl_default != NULL) {
		if (!xdr_reference(xdrs, (void **)&objp->acl_default,
			sizeof(posix_acl) +
			objp->acl_default_count * sizeof(posix_acl_entry),
			(xdrproc_t) xdr_posix_acl))
			return FALSE;
	} else {
		if (!xdr_pointer(xdrs, (void **)&objp->acl_default,
			sizeof(posix_acl) +
			objp->acl_default_count * sizeof(posix_acl_entry),
			(xdrproc_t) xdr_posix_acl))
			return FALSE;
	}
	return TRUE;
}

bool_t
xdr_getaclres (XDR *xdrs, getaclres *objp)
{
	if (!xdr_nfsstat3(xdrs, &objp->status))
		return FALSE;
	switch (objp->status) {
	case NFS3_OK:
		if (!xdr_getaclresok(xdrs, &objp->getaclres_u.resok))
			return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}

bool_t
xdr_setaclargs (XDR *xdrs, setaclargs *objp)
{
	if (!xdr_nfs_fh3(xdrs, &objp->fhandle))
		return FALSE;
	if (!xdr_nfs3_int32(xdrs, &objp->mask))
		return FALSE;
	if (!xdr_nfs3_uint32(xdrs, &objp->acl_access_count))
		return FALSE;
	if (xdrs->x_op == XDR_DECODE) {
		if (!xdr_reference(xdrs, (void **)&objp->acl_access,
			sizeof(posix_acl) +
			objp->acl_access_count * sizeof(posix_acl_entry),
			(xdrproc_t) xdr_posix_acl))
			return FALSE;
	} else {
		if (!xdr_pointer(xdrs, (void **)&objp->acl_access,
			sizeof(posix_acl) +
			objp->acl_access_count * sizeof(posix_acl_entry),
			(xdrproc_t) xdr_posix_acl))
			return FALSE;
	}
	if (!xdr_nfs3_uint32(xdrs, &objp->acl_default_count))
		return FALSE;
	if (xdrs->x_op == XDR_DECODE) {
		if (!xdr_reference(xdrs, (void **)&objp->acl_default,
			sizeof(posix_acl) +
			objp->acl_default_count * sizeof(posix_acl_entry),
			(xdrproc_t) xdr_posix_acl))
			return FALSE;
	} else {
		if (!xdr_pointer(xdrs, (void **)&objp->acl_default,
			sizeof(posix_acl) +
			objp->acl_default_count * sizeof(posix_acl_entry),
			(xdrproc_t) xdr_posix_acl))
			return FALSE;
	}
	return TRUE;
}

bool_t
xdr_setaclresok (XDR *xdrs, setaclresok *objp)
{
	if (!xdr_attr3(xdrs, &objp->attr))
		return FALSE;
	return TRUE;
}

bool_t
xdr_setaclres (XDR *xdrs, setaclres *objp)
{
	if (!xdr_nfsstat3(xdrs, &objp->status))
		return FALSE;
	switch (objp->status) {
	case NFS3_OK:
		if (!xdr_setaclresok(xdrs, &objp->setaclres_u.resok))
			return FALSE;
		break;
	default:
		break;
	}
	return TRUE;
}

