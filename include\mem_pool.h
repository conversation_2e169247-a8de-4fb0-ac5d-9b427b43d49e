#ifndef MEM_POOL_H
#define MEM_POOL_H
#include <stdint.h>


#define PAGE_SIZE 4096

#define	MEM_UNIT_SIZE 	(1 << 20) + PAGE_SIZE
#define MEM_UNIT_SIZE_MIN  (32*1024)

/*add by z<PERSON><PERSON> for mem stat*/
extern unsigned int g_unitcount;
extern unsigned int g_allocnum;

struct stat_mem_data {
	uint64_t alloc_gsh_num;/*alloc nums from gsh */
	uint64_t alloc_gsh_size;/*alloc size from gsh */
	pthread_mutex_t stat_gsh_lock;
};

extern struct stat_mem_data stat_mem;

void mem_stat_init();
/*add by z<PERSON><PERSON> for mem stat*/


struct mem_unit
{
	char *current;
	struct mem_unit *next;
};

struct mem_list
{
	int 	unit_num;
	int		unit_size ;
	char 	*start_addr;
	char 	*free_addr;
	struct mem_unit *head;
	pthread_mutex_t mem_mutex;
};


int mem_pool_init(uint64_t mem_unit_num);

int mem_pool_create(uint64_t mem_unit_count ,int mem_unit_size);

void mem_pool_finalize();

char *mem_unit_alloc();

int mem_unit_free(char *buf);

int get_alloc_count();

int is_mempool_empty() ;

int is_in_mempool(char *buf);
void *gsh_pool_alloc__(size_t size, const char *file, int line, const char *function);
void gsh_pool_free__(void *p, size_t size);

#define gsh_pool_alloc(size) gsh_pool_alloc__((size), \
			__FILE__, __LINE__, __func__)

#define gsh_pool_free(p,size) gsh_pool_free__((p),(size))
#endif
