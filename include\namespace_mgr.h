
#ifndef NAMESAPCE_MGR_H
#define NAMESAPCE_MGR_H

#include <pthread.h>
#include <sys/types.h>

#include "avltree.h"

#define TENANT_NAME_MAX 1024
#define NAMESPACE_NAME_MAX 1024
#define ETCD_VAL_OP_MAX 1024

#define AUDIT_OP_CREATE			(1 << 0)
#define AUDIT_OP_DELETE			(1 << 1)
#define AUDIT_OP_OPEN			(1 << 2)
#define AUDIT_OP_CLOSE			(1 << 3)
#define AUDIT_OP_WRITE			(1 << 4)
#define AUDIT_OP_READ			(1 << 5)
#define AUDIT_OP_SETATTR		(1 << 6)
#define AUDIT_OP_GETATTR		(1 << 7)
#define AUDIT_OP_RENAME			(1 << 8)
#define AUDIT_OP_LIST			(1 << 9)
#define AUDIT_OP_SETSECURITY	(1 << 10)
#define AUDIT_OP_GETSECURITY	(1 << 11)

void ns_audit_pkginit(void);
struct ns_audit *get_ns_audit(const char *tenant, const char *namespace, bool lookup_only);
void put_ns_audit(struct ns_audit *au);
int remove_ns_audit(const char *tenant, const char *namespace);

struct ns_audit_by_tn{
	struct avltree t;
	pthread_rwlock_t lock;
	struct avltree_node **cache;
	uint32_t cache_sz;
};

struct ns_audit {
	struct avltree_node node_k;
	pthread_rwlock_t lock;
	int64_t refcnt;
	char tenant_name[TENANT_NAME_MAX];
	char namespace_name[NAMESPACE_NAME_MAX];
	uint64_t audit_flags;
};

#endif