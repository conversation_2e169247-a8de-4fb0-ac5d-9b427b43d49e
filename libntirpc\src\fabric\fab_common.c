#include "fab_common.h"

void get_current_time(struct timespec *cur_time)
{
	clock_gettime(CLOCK_MONOTONIC, cur_time);
}

uint64_t get_time_diff(const struct timespec start_time, const struct timespec end_time)
{
	uint64_t time_diff = (end_time.tv_sec - start_time.tv_sec) * 1000000000L + (end_time.tv_nsec - start_time.tv_nsec);
	return time_diff;
}

double updata_avg_time_diff(double avg_time, uint64_t time_diff, uint64_t times)
{
	return avg_time + (time_diff - avg_time) / times;
}
