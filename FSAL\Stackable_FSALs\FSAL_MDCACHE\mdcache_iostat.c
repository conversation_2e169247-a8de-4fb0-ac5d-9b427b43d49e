/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright 2015-2019 Red Hat, Inc. and/or its affiliates.
 * Author: <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 */

/* handle.c
 */

#include "config.h"

#include "fsal.h"
#include <libgen.h>		/* used for 'dirname' */
#include <pthread.h>
#include <string.h>
#include <sys/types.h>
#include "gsh_list.h"
#include "fsal_convert.h"
#include "FSAL/fsal_commonlib.h"
#include "nfs4_acls.h"
#include "nfs_exports.h"
#include "sal_functions.h"
#include <os/subr.h>

#include "mdcache_lru.h"
#include "mdcache_hash.h"
#include "mdcache_avl.h"
#include "gsh_config.h"
#include "mdcache_iostat.h"
#include "server_stats_private.h"
#ifdef USE_DBUS
#include "gsh_dbus.h"
#endif

/*
 * handle methods
 */


file_io_stat g_fileio_stat;

const char *io_stat_desc[] = {
	[IO_STATA_RANGE_0_4K] =    "[0k,4k)",
	[IO_STATA_RANGE_4K_8K] =      "[4k,8k)",
	[IO_STATA_RANGE_8K_16K] =  "[8k,16k)",
	[IO_STATA_RANGE_16K_32K] =     "[16k,32k)",
	[IO_STATA_RANGE_32K_64K] = "[32k,64k)",
	[IO_STATA_RANGE_64K_128K] =     "[64k,128k)",
	[IO_STATA_RANGE_128K_MAX] =  "[128k,~)",
};
const char *files_stat_desc[] = {
	[FILE_SIZE_RANGE_0_1K] =    "<1k",
	[FILE_SIZE_RANGE_1K] =      "1k",
	[FILE_SIZE_RANGE_1K_32K] =  "1k-32k",
	[FILE_SIZE_RANGE_32K] =     "32k",
	[FILE_SIZE_RANGE_32K_64K] = "32-64k",
	[FILE_SIZE_RANGE_64K] =     "64k",
	[FILE_SIZE_RANGE_64K_1M] =  "64k-1M",
	[FILE_SIZE_RANGE_1M] =      "1M",
	[FILE_SIZE_RANGE_1M_50M] =  "1M-50M",
	[FILE_SIZE_RANGE_50M] =     "50M",
	[FILE_SIZE_RANGE_50M_100M] =      "50M-100M",
	[FILE_SIZE_RANGE_100M] =          "100M",
	[FILE_SIZE_RANGE_100M_200M] =      "100M-200M",
	[FILE_SIZE_RANGE_200M] =           "200M",
	[FILE_SIZE_RANGE_200M_500M] =      "200M_500M",
	[FILE_SIZE_RANGE_500M] =           "500M",
	[FILE_SIZE_RANGE_500M_1G] =        "500M_1G",
	[FILE_SIZE_RANGE_1G] =             "1G",
	[FILE_SIZE_RANGE_1G_MAX] =         ">1G",
};
static io_stat_range io_stat_convert(uint32_t length){
	io_stat_range io_range;
	if((length >= 0) && (length < IO_BLOCK_SIZE_4K))
		io_range = IO_STATA_RANGE_0_4K;
	else if ((length >= IO_BLOCK_SIZE_4K) && (length < IO_BLOCK_SIZE_8K))
		io_range = IO_STATA_RANGE_4K_8K;
	else if ((length >= IO_BLOCK_SIZE_8K) && (length < IO_BLOCK_SIZE_16K))
		io_range = IO_STATA_RANGE_8K_16K;
	else if ((length >= IO_BLOCK_SIZE_16K) && (length < IO_BLOCK_SIZE_32K))
		io_range = IO_STATA_RANGE_16K_32K;
	else if ((length >= IO_BLOCK_SIZE_32K) && (length < IO_BLOCK_SIZE_64K))
		io_range = IO_STATA_RANGE_32K_64K;
	else if ((length >= IO_BLOCK_SIZE_64K) && (length < IO_BLOCK_SIZE_128K))
		io_range = IO_STATA_RANGE_64K_128K;
	else
		io_range = IO_STATA_RANGE_128K_MAX;
	return io_range;
}

static file_size_range files_stat_convert(uint32_t size){
	file_size_range range;
	if ((size >= 0) && (size < STAT_SIZE_1K))
		range = FILE_SIZE_RANGE_0_1K;
	else if (size == STAT_SIZE_1K)
		range = FILE_SIZE_RANGE_1K;
	else if ((size > STAT_SIZE_1K) && (size < STAT_SIZE_32K))
		range = FILE_SIZE_RANGE_1K_32K;
	else if (size == STAT_SIZE_32K)
		range = FILE_SIZE_RANGE_32K;
	else if ((size > STAT_SIZE_32K) && (size < STAT_SIZE_64K))
		range = FILE_SIZE_RANGE_32K_64K;
	else if (size == STAT_SIZE_64K)
		range = FILE_SIZE_RANGE_64K;
	else if ((size > STAT_SIZE_64K) && (size < STAT_SIZE_1M))
		range = FILE_SIZE_RANGE_64K_1M;
	else if (size == STAT_SIZE_1M)
		range = FILE_SIZE_RANGE_1M;
	else if ((size > STAT_SIZE_1M) && (size < STAT_SIZE_50M))
		range = FILE_SIZE_RANGE_1M_50M;
	else if (size == STAT_SIZE_50M)
		range = FILE_SIZE_RANGE_50M;
	else if ((size > STAT_SIZE_50M) && (size < STAT_SIZE_100M))
		range = FILE_SIZE_RANGE_50M_100M;
	else if (size == STAT_SIZE_100M)
		range = FILE_SIZE_RANGE_100M;
	else if ((size > STAT_SIZE_100M) && (size < STAT_SIZE_200M))
		range = FILE_SIZE_RANGE_100M_200M;
	else if (size == STAT_SIZE_200M)
		range = FILE_SIZE_RANGE_200M;
	else if ((size > STAT_SIZE_200M) && (size < STAT_SIZE_500M))
		range = FILE_SIZE_RANGE_200M_500M;
	else if (size == STAT_SIZE_500M)
		range = FILE_SIZE_RANGE_500M;
	else if ((size > STAT_SIZE_500M) && (size < STAT_SIZE_1G))
		range = FILE_SIZE_RANGE_500M_1G;
	else if (size == STAT_SIZE_1G)
		range = FILE_SIZE_RANGE_1G;
	else
		range = FILE_SIZE_RANGE_1G_MAX;
	return range;
}

void
io_block_stat_init(void){
	now(&g_fileio_stat.first_record);
	(void) pthread_spin_init(&g_fileio_stat.spin,
					 PTHREAD_PROCESS_PRIVATE);
	return;
}
void
remove_io_block_stat(void){
	(void) pthread_spin_destroy(&g_fileio_stat.spin);
	return;
}

bool mdcache_io_stat(struct fsal_obj_handle *obj_hdl,
								uint64_t offset,
								uint32_t length,
								fsal_io_direction_t io_direct)
{
	mdcache_entry_t *entry =
		container_of(obj_hdl, mdcache_entry_t, obj_handle);
	bool result = true;
	io_stat_range io_range = io_stat_convert(length);

	io_stat *io_stat = NULL;
	files_stat *files_stat = NULL;
	uint64_t *entry_io_cnt = NULL;
	fsal_status_t status = {0, 0};
	struct fsal_attrlist attrs;
	uint64_t cur_filesize = 0;
	memset(&attrs, 0, sizeof(struct fsal_attrlist));

	if((entry->record_time.tv_sec != 0) &&
		(gsh_time_cmp(&g_fileio_stat.first_record, &entry->record_time) > -1)){
		entry->write_io_cnt = 0;
		entry->read_io_cnt = 0;
		entry->record_filesize = 0;
	}

	if (io_direct == FSAL_IO_READ){
		io_stat = &g_fileio_stat.read_io_stat;
		files_stat = &g_fileio_stat.read_files_stat;
		entry_io_cnt = &entry->read_io_cnt;
	}else{
		io_stat = &g_fileio_stat.write_io_stat;
		files_stat = &g_fileio_stat.write_files_stat;
		entry_io_cnt = &entry->write_io_cnt;
	}
	(void)atomic_inc_uint64_t(&io_stat->io_total);
	(void)atomic_inc_uint64_t(&io_stat->io[io_range]);
	(void)atomic_inc_uint64_t(entry_io_cnt);

	//usleep(1000);
	status = obj_hdl->obj_ops->getattrs(obj_hdl, &attrs);
	if (FSAL_IS_ERROR(status)){
		return false;
	}
	cur_filesize = ((offset + (uint64_t)length) > entry->attrs.filesize) ? (offset + (uint64_t)length) : entry->attrs.filesize;
	#if 0
	printf("JERRY-in [%s] entry sizes %lu. sizes %lu. cur_filesize %lu,  direct %d. read-io %lu, write-io %lu\n",
		__func__, entry->attrs.filesize, attrs.filesize, cur_filesize, io_direct,
			entry->read_io_cnt, entry->write_io_cnt);
	#endif

	pthread_spin_lock(&g_fileio_stat.spin);
	file_size_range filesize = files_stat_convert(cur_filesize);
	file_size_range last_filesize = files_stat_convert(entry->record_filesize);

	//if(entry->record_filesize < cur_filesize) {
	if (entry->record_filesize > 0){
		files_stat->file_range_counts[last_filesize].file_count --;
			files_stat->file_range_counts[last_filesize].io_count -= (*entry_io_cnt - 1);
	}
	//else if (entry->record_filesize == 0){
	if (*entry_io_cnt == 1){
		(void)atomic_inc_uint64_t(&(files_stat->file_total));
	}
	files_stat->file_range_counts[filesize].file_count ++;
	files_stat->file_range_counts[filesize].io_count += *entry_io_cnt;
	entry->record_filesize = cur_filesize;
	now(&entry->record_time);
	//}
	pthread_spin_unlock(&g_fileio_stat.spin);
	#if 0
	int j=0;
	for(j=0; j < FILE_SIZE_RANGE_MAX; j++){
		printf("JERRY-in [%s],file %10s,file count %lu io count %lu.\n",__func__,
			files_stat_desc[j],
			files_stat->file_range_counts[j].file_count,
			files_stat->file_range_counts[j].io_count);
	}
	#endif 
	return result;
}

bool export_io_stat(struct fsal_obj_handle *obj_hdl,
			uint64_t offset,
			uint32_t length,
			fsal_io_direction_t io_direct)
{
	bool result = true;
	io_stat_range io_range = io_stat_convert(length);

	io_stat *io_stat = NULL;
	struct export_stats *exp_st;
	if (op_ctx->ctx_export == NULL)
		return FALSE;
	exp_st = container_of(op_ctx->ctx_export, struct export_stats, export);
	if(exp_st->io_stat_time.tv_sec == 0){
		now(&exp_st->io_stat_time);
	}
	if (io_direct == FSAL_IO_READ){
		io_stat = &exp_st->read_io_stat;
	}else{
		io_stat = &exp_st->write_io_stat;
	}
	(void)atomic_inc_uint64_t(&io_stat->io_total);
	(void)atomic_inc_uint64_t(&io_stat->io[io_range]);

	return result;
}

#ifdef USE_DBUS

/**
 * @brief NFSv3 io block stats reporting
 */
void dbus_io_stats(void *dbus_iter)
{
	DBusMessageIter *iter = (DBusMessageIter *)dbus_iter;

	DBusMessageIter array, struct_iter;

	char *message = NULL;

	struct timespec timestamp;
	io_stat *write_io_stats =  &g_fileio_stat.write_io_stat;
	io_stat *read_io_stats =  &g_fileio_stat.read_io_stat;
	files_stat *write_files_stats =  &g_fileio_stat.write_files_stat;
	files_stat *read_files_stats =  &g_fileio_stat.read_files_stat;

	int i = 0;
	unsigned int range_max = IO_STATA_RANGE_MAX + FILE_SIZE_RANGE_MAX;
	unsigned int io_percent = 0;
	uint64_t basic_cnt = 0;

    /* name|writetotal|..|range|percnet|..|readtotal|..|range|percnet|..| */
	message = "all files";
	dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &message);

	dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT64, &g_fileio_stat.write_files_stat.file_total);
	dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT64, &write_io_stats->io_total);

	if (write_io_stats->io_total > 0){
		range_max = IO_STATA_RANGE_MAX;
		dbus_message_iter_append_basic(iter,
					DBUS_TYPE_UINT32, &range_max);

		dbus_message_iter_open_container(iter,
						 DBUS_TYPE_ARRAY,
						 "(su)", &array);

		for (i = 0; i < IO_STATA_RANGE_MAX; i++){
			io_percent = (write_io_stats->io[i]*100)/write_io_stats->io_total;
			/*start struct container*/
			dbus_message_iter_open_container(&array,
						 DBUS_TYPE_STRUCT, NULL, &struct_iter);

			dbus_message_iter_append_basic(&struct_iter, DBUS_TYPE_STRING, &io_stat_desc[i]);
			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT32, &io_percent);

			dbus_message_iter_close_container(&array, &struct_iter);
			/*end struct container*/

		}
		dbus_message_iter_close_container(iter,
						  &array);


		range_max = FILE_SIZE_RANGE_MAX;
		dbus_message_iter_append_basic(iter,
					DBUS_TYPE_UINT32, &range_max);
		dbus_message_iter_open_container(iter,
						 DBUS_TYPE_ARRAY,
						 "(stt)", &array);

		for (i = 0; i < FILE_SIZE_RANGE_MAX; i++){
			/*start struct container*/
			dbus_message_iter_open_container(&array,
						 DBUS_TYPE_STRUCT, NULL, &struct_iter);

			dbus_message_iter_append_basic(&struct_iter, DBUS_TYPE_STRING, &files_stat_desc[i]);
			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT64, &write_files_stats->file_range_counts[i].file_count);
			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT64, &write_files_stats->file_range_counts[i].io_count);

			dbus_message_iter_close_container(&array, &struct_iter);
			/*end struct container*/
		}

		dbus_message_iter_close_container(iter,
						  &array);

	}else{
		io_percent = 0;
		range_max = IO_STATA_RANGE_MAX;
		dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT32, &range_max);
		dbus_message_iter_open_container(iter,
						 DBUS_TYPE_ARRAY,
						 "(su)", &array);
		for (i=0; i<IO_STATA_RANGE_MAX; i++){
			dbus_message_iter_open_container(&array,
						 DBUS_TYPE_STRUCT, NULL, &struct_iter);

			dbus_message_iter_append_basic(&struct_iter, DBUS_TYPE_STRING, &io_stat_desc[i]);
			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT32, &io_percent);

			dbus_message_iter_close_container(&array, &struct_iter);
		}

		dbus_message_iter_close_container(iter,
						  &array);

		range_max = FILE_SIZE_RANGE_MAX;
		dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT32, &range_max);
		dbus_message_iter_open_container(iter,
						 DBUS_TYPE_ARRAY,
						 "(stt)", &array);

		for (i=0; i<FILE_SIZE_RANGE_MAX; i++){
			dbus_message_iter_open_container(&array,
						 DBUS_TYPE_STRUCT, NULL, &struct_iter);

			dbus_message_iter_append_basic(&struct_iter, DBUS_TYPE_STRING, &files_stat_desc[i]);
			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT64, &basic_cnt);
			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT64, &basic_cnt);

			dbus_message_iter_close_container(&array, &struct_iter);
		}
		dbus_message_iter_close_container(iter,
						  &array);

	}

	dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT64, &g_fileio_stat.read_files_stat.file_total);
	dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT64, &read_io_stats->io_total);

	if (read_io_stats->io_total > 0){
		range_max = IO_STATA_RANGE_MAX;
		dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT32, &range_max);

		dbus_message_iter_open_container(iter,
						 DBUS_TYPE_ARRAY,
						 "(su)", &array);

		for (i=0; i < IO_STATA_RANGE_MAX; i++){
			io_percent = (read_io_stats->io[i]*100)/read_io_stats->io_total;
			/*start struct container*/
			dbus_message_iter_open_container(&array,
						 DBUS_TYPE_STRUCT, NULL, &struct_iter);

			dbus_message_iter_append_basic(&struct_iter, DBUS_TYPE_STRING, &io_stat_desc[i]);


			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT32, &io_percent);

			dbus_message_iter_close_container(&array, &struct_iter);
			/*end struct container*/

		}
		dbus_message_iter_close_container(iter,
						  &array);

		range_max = FILE_SIZE_RANGE_MAX;
		dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT32, &range_max);

		dbus_message_iter_open_container(iter,
						 DBUS_TYPE_ARRAY,
						 "(stt)", &array);

		for (i=0; i < FILE_SIZE_RANGE_MAX; i++){
			/*start struct container*/
			dbus_message_iter_open_container(&array,
						 DBUS_TYPE_STRUCT, NULL, &struct_iter);

			dbus_message_iter_append_basic(&struct_iter, DBUS_TYPE_STRING, &files_stat_desc[i]);

			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT64, &read_files_stats->file_range_counts[i].file_count);
			dbus_message_iter_append_basic(&struct_iter,
						DBUS_TYPE_UINT64, &read_files_stats->file_range_counts[i].io_count);

			dbus_message_iter_close_container(&array, &struct_iter);
			/*end struct container*/

		}

		dbus_message_iter_close_container(iter,
						  &array);
	}else{
		io_percent = 0;
		range_max = IO_STATA_RANGE_MAX;
		dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT32, &range_max);
		dbus_message_iter_open_container(iter,
						 DBUS_TYPE_ARRAY,
						 "(su)", &array);
		for (i=0; i < IO_STATA_RANGE_MAX; i++){
			dbus_message_iter_open_container(&array,
						 DBUS_TYPE_STRUCT, NULL, &struct_iter);

			dbus_message_iter_append_basic(&struct_iter, DBUS_TYPE_STRING, &io_stat_desc[i]);
			dbus_message_iter_append_basic(&struct_iter,
						 DBUS_TYPE_UINT32, &io_percent);

			dbus_message_iter_close_container(&array, &struct_iter);
		}
		dbus_message_iter_close_container(iter,
						  &array);

		range_max = FILE_SIZE_RANGE_MAX;
		dbus_message_iter_append_basic(iter,
				DBUS_TYPE_UINT32, &range_max);
		dbus_message_iter_open_container(iter,
						 DBUS_TYPE_ARRAY,
						 "(stt)", &array);

		for (i=0; i < FILE_SIZE_RANGE_MAX; i++){
			dbus_message_iter_open_container(&array,
						 DBUS_TYPE_STRUCT, NULL, &struct_iter);

			dbus_message_iter_append_basic(&struct_iter, DBUS_TYPE_STRING, &files_stat_desc[i]);
			dbus_message_iter_append_basic(&struct_iter,
						 DBUS_TYPE_UINT64, &basic_cnt);
			dbus_message_iter_append_basic(&struct_iter,
						 DBUS_TYPE_UINT64, &basic_cnt);

			dbus_message_iter_close_container(&array, &struct_iter);
		}

		dbus_message_iter_close_container(iter,
						  &array);

	}

    now(&timestamp);
	gsh_dbus_append_timestamp(iter, &g_fileio_stat.first_record);
	gsh_dbus_append_timestamp(iter, &timestamp);

	message = "OK";
	dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &message);

	return;
}

void dbus_reset_io_stats(void)
{
	memset(&g_fileio_stat.write_files_stat, 0, sizeof(files_stat));
	memset(&g_fileio_stat.write_io_stat, 0, sizeof(io_stat));
	memset(&g_fileio_stat.read_files_stat, 0, sizeof(files_stat));
	memset(&g_fileio_stat.read_io_stat, 0, sizeof(io_stat));

	now(&g_fileio_stat.first_record);
}




#endif
