/*
 * Copyright (c) 2009, Sun Microsystems, Inc.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 * - Redistributions of source code must retain the above copyright notice,
 *   this list of conditions and the following disclaimer.
 * - Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 * - Neither the name of Sun Microsystems, Inc. nor the names of its
 *   contributors may be used to endorse or promote products derived
 *   from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON> DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

/*
 * svc_auth_unix.c
 * Handles UNIX flavor authentication parameters on the service side of rpc.
 * There are two svc auth implementations here: AUTH_UNIX and AUTH_SHORT.
 * _svcauth_unix does full blown unix style uid,gid+gids auth,
 * _svcauth_short uses a shorthand auth to index into a cache of longhand auths.
 * Note: the shorthand has been gutted for efficiency.
 *
 * Copyright (C) 1984, Sun Microsystems, Inc.
 */
#include "config.h"
#include <pthread.h>
#include <assert.h>
#include <stdio.h>
#include <string.h>

#include <rpc/rpc.h>
#include <rpc/svc.h>
#include <rpc/svc_auth.h>

extern SVCAUTH svc_auth_none;

/*
 * Unix longhand authenticator
 */
enum auth_stat
_svcauth_unix(struct svc_req *req)
{
	enum auth_stat stat;
	XDR xdrs;
	struct authunix_parms *aup;
	int32_t *buf;
	struct area {
		struct authunix_parms area_aup;
		char area_machname[MAX_MACHINE_NAME + 1];
		gid_t area_gids[NGRPS];
	} *area;
	u_int auth_len;
	size_t str_len, gid_len;
	u_int i;

	assert(req != NULL);

	req->rq_auth = &svc_auth_none;

	area = (struct area *)req->rq_msg.rq_cred_body;
	aup = &area->area_aup;
	aup->aup_machname = area->area_machname;
	aup->aup_gids = area->area_gids;
	auth_len = (u_int) req->rq_msg.cb_cred.oa_length;
	xdrmem_create(&xdrs, req->rq_msg.cb_cred.oa_body, auth_len,
		      XDR_DECODE);
	buf = xdr_inline_decode(&xdrs, auth_len);
	if (buf != NULL) {
		aup->aup_time = IXDR_GET_INT32(buf);
		str_len = (size_t) IXDR_GET_U_INT32(buf);
		if (str_len > MAX_MACHINE_NAME) {
			stat = AUTH_BADCRED;
			goto done;
		}
		memmove(aup->aup_machname, buf, str_len);
		aup->aup_machname[str_len] = 0;
		str_len = RNDUP(str_len);
		buf += str_len / sizeof(int32_t);
		aup->aup_uid = (int)IXDR_GET_INT32(buf);
		aup->aup_gid = (int)IXDR_GET_INT32(buf);
		gid_len = (size_t) IXDR_GET_U_INT32(buf);
		if (gid_len > NGRPS || aup->aup_uid == (uid_t)-1 ||
				aup->aup_gid == (gid_t)-1) {
			stat = AUTH_BADCRED;
			goto done;
		}
		aup->aup_len = gid_len;
		for (i = 0; i < gid_len; i++) {
			/* suppress block warning */
			aup->aup_gids[i] = (int)IXDR_GET_INT32(buf);
			if (aup->aup_gids[i] == (gid_t)-1) {
				stat = AUTH_BADCRED;
				goto done;
			}
		}
		/*
		 * five is the smallest unix credentials structure -
		 * timestamp, hostname len (0), uid, gid, and gids len (0).
		 */
		if ((5 + gid_len) * BYTES_PER_XDR_UNIT + str_len > auth_len) {
			__warnx(TIRPC_DEBUG_FLAG_AUTH,
				"bad auth_len gid %ld str %ld auth %u\n",
				(long)gid_len, (long)str_len, auth_len);
			stat = AUTH_BADCRED;
			goto done;
		}
	} else if (!xdr_authunix_parms(&xdrs, aup)) {
		xdrs.x_op = XDR_FREE;
		(void)xdr_authunix_parms(&xdrs, aup);
		stat = AUTH_BADCRED;
		goto done;
	}

	/* get the verifier */
	req->rq_msg.RPCM_ack.ar_verf = req->rq_msg.cb_verf;
	stat = AUTH_OK;
 done:
	XDR_DESTROY(&xdrs);

	return (stat);
}

/*
 * Shorthand unix authenticator
 * Looks up longhand in a cache.
 */
 /*ARGSUSED*/
enum auth_stat
_svcauth_short(struct svc_req *req)
{
	req->rq_auth = &svc_auth_none;
	return (AUTH_REJECTEDCRED);
}
