/*
 * Copyright © 2012-2014, CohortFS, LLC.
 * Author: <PERSON> <<EMAIL>>
 *
 * contributeur : <PERSON> <<EMAIL>>
 *		  <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

/**
 * @file FSAL_IDFS/export.c
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @date Wed Oct 22 13:24:33 2014
 *
 * @brief Implementation of FSAL export functions for Idfs
 *
 * This file implements the Idfs specific functionality for the FSAL
 * export handle.
 */
#include "config.h"
#include <limits.h>
#include <stdint.h>
#include <sys/statvfs.h>
#include <idfsfs/libidfsfs.h>
#include <idfsfs/idfs_ll_client.h>
#include "abstract_mem.h"
#include "fsal.h"
#include "fsal_types.h"
#include "fsal_api.h"
#include "FSAL/fsal_commonlib.h"
#include "FSAL/fsal_config.h"
#include "internal.h"
#include "statx_compat.h"
#include "sal_functions.h"
#include "nfs_core.h"
#ifdef IDFSFS_POSIX_ACL
#include "nfs_exports.h"
#endif				/* IDFSFS_POSIX_ACL */
#ifdef USE_LTTNG
#include "gsh_lttng/fsal_idfs.h"
#endif
#include "../../include/tenant_mgr.h"
#include "../../include/server_stats_private.h"
#define QOS_UNIT_SIZE 1024

extern struct idfs_export root_export;

enum QosOpType
{
  QOS_READ = 1,
  QOS_WRITE = 2,
};

struct QosOp
{
  enum QosOpType type;	// read/write
  inodeno_t ino;	// inode no
  uint64_t len;	// read/write length
  int uid;	// who read/write
  int gid;	// who read/write
  char* client_ip;	// client ipZZ
  char* tenant_name;
  void *reqdata;
  void (*callback)(void *);
};


/**
 * @brief Clean up an export
 *
 * This function cleans up an export after the last reference is
 * released.
 *
 * @param[in,out] export The export to be released
 */

static void release(struct fsal_export *export_pub)
{
	/* The private, expanded export */
	struct idfs_export *export =
			container_of(export_pub, struct idfs_export, export);
	LogEvent(COMPONENT_FSAL, "release idfs export, exportid=%u", export->export.export_id);
	
	struct gsh_tenant *tenant = NULL;
	tenant = get_gsh_tenant(export_pub->owning_export->tenant, true);
	if(tenant != NULL){
		LogEvent(COMPONENT_FSAL, "tenant:%s, ref:%lu ", tenant->tenant_name, tenant->refcnt);
		put_gsh_tenant(tenant);
		put_gsh_tenant(tenant);
	}

	if (nfs_param.core_param.enable_root_export == false)
	{
		deconstruct_handle(export->root);
		export->root = 0;
#if USE_FSAL_IDFS_ABORT_CONN
		if (admin_shutdown && nfs_grace_is_member() && admin_abort_conn){
			idfs_abort_conn(export->cmount);
		}
#endif

		fsal_detach_export(export->export.fsal, &export->export.exports);
		free_export_ops(&export->export);

		idfs_shutdown(export->cmount);
		export->cmount = NULL;
		gsh_free(export);
		export = NULL;

		return;
	}
	else{
#if USE_FSAL_IDFS_ABORT_CONN
		if (admin_shutdown && nfs_grace_is_member() && admin_abort_conn){
			idfs_abort_conn(export->cmount);
		}
#endif
		export->root = NULL;
		if(export->export.fsal != NULL){
			fsal_detach_export(export->export.fsal, &export->export.exports);
		}
		free_export_ops(&export->export);
		export->cmount = NULL;

	}
}

/**
 * @brief Return a handle corresponding to a path
 *
 * This function looks up the given path and supplies an FSAL object
 * handle.  Because the root path specified for the export is a Idfs
 * style root as supplied to mount -t idfs of idfs-fuse (of the form
 * host:/path), we check to see if the path begins with / and, if not,
 * skip until we find one.
 *
 * @param[in]  export_pub The export in which to look up the file
 * @param[in]  path       The path to look up
 * @param[out] pub_handle The created public FSAL handle
 *
 * @return FSAL status.
 */

static fsal_status_t lookup_path(struct fsal_export *export_pub,
				 const char *path,
				 struct fsal_obj_handle **pub_handle,
				 struct fsal_attrlist *attrs_out)
{
	/* The 'private' full export handle */
	struct idfs_export *export =
			container_of(export_pub, struct idfs_export, export);
	/* The 'private' full object handle */
	struct idfs_handle *handle = NULL;
	/* IdfsInode pointer */
	struct IdfsInode *i = NULL;
	/* FSAL status structure */
	fsal_status_t status = { ERR_FSAL_NO_ERROR, 0 };
	/* The buffer in which to store stat info */
	struct idfs_statx stx;
	/* Return code from Idfs */
	int rc;
	/* Find the actual path in the supplied path */
	const char *realpath;

	if (*path != '/') {
		realpath = strchr(path, ':');
		if (realpath == NULL) {
			status.major = ERR_FSAL_INVAL;
			return status;
		}
		if (*(++realpath) != '/') {
			status.major = ERR_FSAL_INVAL;
			return status;
		}
	} else {
		realpath = path;
	}

	*pub_handle = NULL;

	/*
	 * sanity check: ensure that this is the right export. realpath
	 * must be a superset of the export fullpath, or the string
	 * handling will be broken.
	 */
	if (strstr(realpath, CTX_FULLPATH(op_ctx)) != realpath) {
		status.major = ERR_FSAL_SERVERFAULT;
		return status;
	}

	/* Advance past the export's fullpath */
	if(nfs_param.core_param.enable_root_export == false)
	{
		realpath += strlen(CTX_FULLPATH(op_ctx));
	}

	/* special case the root */
	if (strcmp(realpath, "/") == 0) {
		assert(export->root);
		*pub_handle = &export->root->handle;
		return status;
	}

	rc = fsal_idfs_ll_walk(export->cmount, realpath, &i, &stx,
				!!attrs_out, &op_ctx->creds);
	if (rc < 0)
		return idfs2fsal_error(rc);

	construct_handle(&stx, i, export, &handle);

	if (attrs_out != NULL)
		idfs2fsal_attributes(&stx, attrs_out);

	*pub_handle = &handle->handle;
#ifdef USE_LTTNG
	tracepoint(fsalidfs, idfs_create_handle, __func__, __LINE__,
		   &handle->handle);
#endif
	return status;
}

/**
 * @brief Decode a digested handle
 *
 * This function decodes a previously digested handle.
 *
 * @param[in]  exp_handle  Handle of the relevant fs export
 * @param[in]  in_type  The type of digest being decoded
 * @param[out] fh_desc  Address and length of key
 */
static fsal_status_t wire_to_host(struct fsal_export *exp_hdl,
				  fsal_digesttype_t in_type,
				  struct gsh_buffdesc *fh_desc,
				  int flags)
{
	struct idfs_host_handle *hhdl = fh_desc->addr;

	switch (in_type) {
		/* Digested Handles */
	case FSAL_DIGEST_NFSV3:
	case FSAL_DIGEST_NFSV4:
		/*
		 * Gnfs automatically mixes the export_id in with the
		 * filehandle and strips that out before calling this
		 * function.
		 *
		 * Note that we use a LE values in the filehandle. Earlier
		 * versions treated those values as opaque, so this allows us to
		 * maintain compatibility with legacy deployments (most of which
		 * were on LE arch).
		 */
		hhdl->chk_ino = le64toh(hhdl->chk_ino);
		hhdl->chk_snap = le64toh(hhdl->chk_snap);
		hhdl->chk_fscid = le64toh(hhdl->chk_fscid);
		hhdl->chk_vision = le64toh(hhdl->chk_vision);
		fh_desc->len = sizeof(*hhdl);
		break;
	default:
		return fsalstat(ERR_FSAL_SERVERFAULT, 0);
	}

	return fsalstat(ERR_FSAL_NO_ERROR, 0);
}

/**
 * @brief extract "key" from a host handle
 *
 * This function extracts a "key" from a host handle.  That is, when
 * given a handle that is extracted from wire_to_host() above, this
 * method will extract the unique bits used to index the inode cache.
 *
 * @param[in]     exp_hdl Export handle
 * @param[in,out] fh_desc Buffer descriptor.  The address of the
 *                        buffer is given in @c fh_desc->buf and must
 *                        not be changed.  @c fh_desc->len is the length
 *                        of the data contained in the buffer, @c
 *                        fh_desc->len must be updated to the correct
 *                        size. In other words, the key has to be placed
 *                        at the beginning of the buffer!
 */
fsal_status_t host_to_key(struct fsal_export *exp_hdl,
			  struct gsh_buffdesc *fh_desc)
{
	struct idfs_handle_key *key = fh_desc->addr;

	/*
	 * Gnfs automatically mixes the export_id in with the actual wire
	 * filehandle and strips that out before transforming it to a host
	 * handle. This method is called on a host handle which doesn't have
	 * the export_id.
	 *
	 * Most FSALs don't factor in the export_id with the handle_key,
	 * but we want to do that for FSAL_IDFS, primarily because we
	 * want to do accesses via different exports via different idfsx
	 * creds. Mix the export_id back in here.
	 */
	if (nfs_param.core_param.enable_root_export == true) {
		key->export_id = root_export.export.export_id;
	} else {
		key->export_id = op_ctx->ctx_export->export_id;
	}
	
	fh_desc->len = sizeof(*key);

	return fsalstat(ERR_FSAL_NO_ERROR, 0);
}

#ifndef USE_FSAL_IDFS_LOOKUP_VINO
static int idfs_ll_lookup_vino(struct idfs_mount_info *cmount, vinodeno_t vino,
			       IdfsInode **inode)
{
	uint32_t idfs_ops = IDFS_GET_INODE;
	int ret = -1;
	if (nfs_param.core_param.enable_FSALSTATS){
		idfs_ops = IDFS_GET_INODE;	
	/* Check the cache first */
		stat_idfs_begin(idfs_ops);
		*inode = idfs_ll_get_inode(cmount, vino);
		stat_idfs_end(idfs_ops);
	}else{
		*inode = idfs_ll_get_inode(cmount, vino);
	}
	if (*inode)
		return 0;

	/*
	 * We can't look up snap inodes w/o idfs_ll_lookup_vino. Just
	 * return -ESTALE if we get one that's not already in cache.
	 */
	if (vino.snapid.val != IDFS_NOSNAP)
		return -ESTALE;

	if (nfs_param.core_param.enable_FSALSTATS){
		idfs_ops = IDFS_LOOKUP_INODE;
		stat_idfs_begin(idfs_ops);
		ret = idfs_ll_lookup_inode(cmount, vino.ino, inode);
		stat_idfs_end(idfs_ops);
	}else{
		ret = idfs_ll_lookup_inode(cmount, vino.ino, inode);
	}

	return ret;
}
#endif /* USE_FSAL_IDFS_LOOKUP_VINO */

/**
 * @brief Create a handle object from a wire handle
 *
 * The wire handle is given in a buffer outlined by desc, which it
 * looks like we shouldn't modify.
 *
 * @param[in]  export_pub Public export
 * @param[in]  desc       Handle buffer descriptor (host handle)
 * @param[out] pub_handle The created handle
 *
 * @return FSAL status.
 */
static fsal_status_t create_handle(struct fsal_export *export_pub,
				   struct gsh_buffdesc *desc,
				   struct fsal_obj_handle **pub_handle,
				   struct fsal_attrlist *attrs_out)
{
	/* Full 'private' export structure */
	struct idfs_export *export =
			container_of(export_pub, struct idfs_export, export);
	/* FSAL status to return */
	fsal_status_t status = { ERR_FSAL_NO_ERROR, 0 };
	/* The FSAL specific portion of the handle received by the client */
	struct idfs_host_handle *hhdl = desc->addr;
	/* Idfs return code */
	int rc = 0;
	/* Stat buffer */
	struct idfs_statx stx;
	/* Handle to be created */
	struct idfs_handle *handle = NULL;
	/* IdfsInode pointer */
	struct IdfsInode *i = NULL;
	vinodeno_t vi;

	*pub_handle = NULL;
	uint64_t tmptime1 = 0;

	if (desc->len != sizeof(*hhdl)) {
		status.major = ERR_FSAL_INVAL;
		return status;
	}

	vi.ino.val = hhdl->chk_ino;
	vi.snapid.val = hhdl->chk_snap;

	rc = idfs_ll_lookup_vino(export->cmount, vi, &i);
	if (rc)
		return idfs2fsal_error(rc);
	if (nfs_param.core_param.enable_FSALSTATS) {
		uint32_t idfs_ops = IDFS_GETATTR;
		stat_idfs_begin(idfs_ops);
		rc = fsal_idfs_ll_getattr(export->cmount, i, &stx,
			attrs_out ? IDFS_STATX_ATTR_MASK : IDFS_STATX_HANDLE_MASK,
			&op_ctx->creds);
		tmptime1 = stat_idfs_end(idfs_ops);
		LogEvent_TIMEOUT(COMPONENT_FSAL, tmptime1, "fsal_idfs_ll_getattr: export_id:%d, obj_ino=%lu, rc=%d, time_consumed=%ld s",
			export_pub->export_id, rc < 0 ? 0:stx.stx_ino, rc, tmptime1);
	}else{
		rc = fsal_idfs_ll_getattr(export->cmount, i, &stx,
			attrs_out ? IDFS_STATX_ATTR_MASK : IDFS_STATX_HANDLE_MASK,
			&op_ctx->creds);
	}
	if (rc < 0)
		return idfs2fsal_error(rc);

	construct_handle(&stx, i, export, &handle);

	if (attrs_out != NULL)
		idfs2fsal_attributes(&stx, attrs_out);

	*pub_handle = &handle->handle;
#ifdef USE_LTTNG
	tracepoint(fsalidfs, idfs_create_handle, __func__, __LINE__,
		   &handle->handle);
#endif
	return status;
}

/**
 * @brief Get dynamic filesystem info
 *
 * This function returns dynamic filesystem information for the given
 * export.
 *
 * @param[in]  export_pub The public export handle
 * @param[out] info       The dynamic FS information
 *
 * @return FSAL status.
 */

static fsal_status_t get_fs_dynamic_info(struct fsal_export *export_pub,
					 struct fsal_obj_handle *obj_hdl,
					 fsal_dynamidfsinfo_t *info)
{
	/* Full 'private' export */
	struct idfs_export *export =
			container_of(export_pub, struct idfs_export, export);
	struct idfs_handle *handle =
		container_of(obj_hdl, struct idfs_handle, handle);

	/* Return value from Idfs calls */
	int rc = 0;
	uint32_t idfs_ops = IDFS_STATE_FS;
	/* Filesystem stat */
	struct statvfs vfs_st;
	uint64_t tmptime1 = 0;
	UserPerm *perms = user_cred2idfs(&op_ctx->creds);
	LogFullDebug(COMPONENT_FSAL, "op_ctx->creds.caller_uid:%u",op_ctx->creds.caller_uid);
	if (!perms){
		LogWarn(COMPONENT_FSAL, "user_cred 2 idfs is error, op_ctx->creds.caller_uid:%u",op_ctx->creds.caller_uid);
		return idfs2fsal_error(-ENOMEM);
	}

	if (nfs_param.core_param.enable_FSALSTATS) {
		stat_idfs_begin(idfs_ops);
		rc = idfs_ll_statfs(export->cmount, handle->i, &vfs_st, perms);
		tmptime1 = stat_idfs_end(idfs_ops);
		LogEvent_TIMEOUT(COMPONENT_FSAL, tmptime1, "idfs_ll_statfs: export_id:%d, rc=%d, time_consumed=%ld s",
                        export_pub->export_id, rc, tmptime1);

	}else{
		rc = idfs_ll_statfs(export->cmount, handle->i, &vfs_st, perms);
	}
	idfs_userperm_destroy(perms);
	if (rc < 0)
		return idfs2fsal_error(rc);

	memset(info, 0, sizeof(fsal_dynamidfsinfo_t));
	info->total_bytes = vfs_st.f_frsize * vfs_st.f_blocks;
	info->free_bytes = vfs_st.f_frsize * vfs_st.f_bfree;
	info->avail_bytes = vfs_st.f_frsize * vfs_st.f_bavail;
	info->total_files = vfs_st.f_files;
	info->free_files = vfs_st.f_ffree;
	info->avail_files = vfs_st.f_favail;
	info->time_delta.tv_sec = 0;
	info->time_delta.tv_nsec = FSAL_DEFAULT_TIME_DELTA_NSEC;

	return fsalstat(ERR_FSAL_NO_ERROR, 0);
}

#ifdef IDFSFS_POSIX_ACL
static fsal_aclsupp_t fs_acl_support(struct fsal_export *exp_hdl)
{
	if (!op_ctx_export_has_option(EXPORT_OPTION_DISABLE_ACL))
		return fsal_acl_support(&exp_hdl->fsal->fs_info);
	else
		return 0;
}
#endif				/* IDFSFS_POSIX_ACL */

void idfs_prepare_unexport(struct fsal_export *export_pub)
{
	struct idfs_export *export =
			container_of(export_pub, struct idfs_export, export);

	/* Flush all buffers */
	idfs_sync_fs(export->cmount);
#if 0
#if USE_FSAL_IDFS_ABORT_CONN
	/*
	 * If we're shutting down and are still a member of the cluster, do a
	 * hard abort on the connection to ensure that state is left intact on
	 * the DMS when we return. If we're not shutting down or aren't a
	 * member any longer then cleanly tear down the export.
	 */
	if (admin_shutdown && nfs_grace_is_member())
		idfs_abort_conn(export->cmount);
#endif
#endif
}

int do_check_qos(struct fsal_export *export_pub, uint64_t fileid, char* opType, uint64_t len, 
					void *qosreq, void (*callback)(void *)){
	int status;
	struct QosOp op;
	const char* type_name = "idfs.checkqos";
	int flag = 1;  //0同步，1异步

	if (strcmp(opType, "read") == 0){
		op.type = QOS_READ;
	}else if(strcmp(opType, "write") == 0){
		op.type = QOS_WRITE;
	}else{
		return 0;
	}
	struct idfs_export *export =
		container_of(export_pub, struct idfs_export, export);

	op.ino.val = fileid;
	op.len = len;
	op.uid = op_ctx->creds.caller_uid;
	op.gid = op_ctx->creds.caller_gid;
	op.tenant_name = op_ctx->ctx_export->tenant;
	op.reqdata = qosreq;
	op.callback = callback;

	if ((op_ctx->caller_addr->ss_family == AF_INET6) && !strncmp(op_ctx->client->hostaddr_str, "::ffff:", 7)) {
		op.client_ip = op_ctx->client->hostaddr_str+7;
		LogFullDebug(COMPONENT_FSAL, "export->cmount = %p || handle->i->ino = %ld || opType = %s || len = %ld || uid = %d || gid = %d || client = %s",
			export->cmount, op.ino.val, opType, len, op.uid, op.gid, op.client_ip);
	} else {
		op.client_ip = op_ctx->client->hostaddr_str;
		LogFullDebug(COMPONENT_FSAL, "export->cmount = %p || handle->i->ino = %ld || opType = %s || len = %ld || uid = %d || gid = %d || client = %s",
			export->cmount, op.ino.val, opType, len, op.uid, op.gid, op.client_ip);
	}
	if (atomic_fetch_uint64_t(&nfs_param.core_param.qos_suspend_count) >= nfs_param.core_param.qos_suspend_count_max){
		flag = 0;
	}
	status = idfs_vas_set(export->cmount, NULL, type_name, &op, sizeof(op), flag); 
	LogFullDebug(COMPONENT_FSAL, "idfs_vas_set return status=%d", status);
	if (status > 0){
		atomic_inc_uint64_t(&nfs_param.core_param.qos_suspend_count);
	}
	return status;
}

static int check_qos(struct fsal_export *export_pub, void *qosreq, void (*callback)(void *))
{
	struct svc_req *req = (struct svc_req *)qosreq;
	nfs_request_t *reqdata = container_of(req, nfs_request_t, svc);
	//nfs_request_t *reqdata = (nfs_request_t *)nfsreqdata;
	//struct svc_req *req = &reqdata->svc;
	nfs_arg_t *arg_nfs = &reqdata->arg_nfs;
	uint64_t size = 0;
	int i = 0;
	char* type = NULL;
	nfs_fh4 *fh4 = NULL;
	uint64_t fileid = 0;
	
	if (req->rq_msg.cb_prog== NFS_PROGRAM && req->rq_msg.cb_vers == NFS_V3){		
		if (reqdata->lookahead.flags & NFS_LOOKAHEAD_WRITE){
			type = "write";
			size = reqdata->arg_nfs.arg_write3.count;
			if (size > atomic_fetch_uint64_t(&op_ctx->ctx_export->MaxWrite)) {
				LogEvent(COMPONENT_DISPATCH, "NFS_V3 NFS_LOOKAHEAD_WRITE size > MaxWrite , size %"PRIu64, size);
				size = atomic_fetch_uint64_t(&op_ctx->ctx_export->MaxWrite);
			}
			fileid =  *((uint64_t *) ((char *)arg_nfs->arg_write3.file.data.data_val + offsetof(file_handle_v3_t, fsopaque)));
		}else{
			type = "read";
			size = reqdata->arg_nfs.arg_read3.count;
			if (size > atomic_fetch_uint64_t(&op_ctx->ctx_export->MaxRead)) {
				LogEvent(COMPONENT_DISPATCH, "NFS_V3 NFS_LOOKAHEAD_READ size > MaxRead , size %"PRIu64, size);
				size = atomic_fetch_uint64_t(&op_ctx->ctx_export->MaxRead);
			}
			fileid =  *((uint64_t *) ((char *)arg_nfs->arg_read3.file.data.data_val + offsetof(file_handle_v3_t, fsopaque)));
		}
	}
	if (req->rq_msg.cb_prog== NFS_PROGRAM && req->rq_msg.cb_vers == NFS_V4){
		u_int array_len = reqdata->arg_nfs.arg_compound4.argarray.argarray_len;
		nfs_argop4 *argarray_val = reqdata->arg_nfs.arg_compound4.argarray.argarray_val;
		
		if (reqdata->lookahead.flags & NFS_LOOKAHEAD_READ) {
			type = "read";
			for( i=0;i < array_len;i++){
				size += argarray_val[i].nfs_argop4_u.opread.count;
				if (argarray_val[i].argop == NFS4_OP_PUTFH){
					fh4 = &argarray_val[i].nfs_argop4_u.opputfh.object;
					fileid =  *((uint64_t *) ((char *)fh4->nfs_fh4_val + offsetof(file_handle_v4_t, fsopaque)));
				}
			}
			if (size > atomic_fetch_uint64_t(&op_ctx->ctx_export->MaxRead)) {
				LogEvent(COMPONENT_DISPATCH, "NFS_V4 NFS_LOOKAHEAD_READ size > MaxRead , size %"PRIu64, size);
				size = atomic_fetch_uint64_t(&op_ctx->ctx_export->MaxRead);
			}
		} else{
			type = "write";
			for( i = 0;i < array_len;i++){
				size += argarray_val[i].nfs_argop4_u.opwrite.data.data_len;
				if (argarray_val[i].argop == NFS4_OP_PUTFH){
					fh4 = &argarray_val[i].nfs_argop4_u.opputfh.object;
					fileid =  *((uint64_t *) ((char *)fh4->nfs_fh4_val + offsetof(file_handle_v4_t, fsopaque)));
				}
			}
			if (size > atomic_fetch_uint64_t(&op_ctx->ctx_export->MaxWrite)) {
				LogEvent(COMPONENT_DISPATCH, "NFS_V4 NFS_LOOKAHEAD_WRITE size > MaxWrite , size %"PRIu64, size);
				size = atomic_fetch_uint64_t(&op_ctx->ctx_export->MaxWrite);
			}
		}
	}
		
	return do_check_qos(export_pub, fileid, type, size, qosreq, callback);
}

static fsal_status_t check_conn(struct fsal_export *export_pub){
	fsal_status_t status = { ERR_FSAL_NO_ERROR, 0 };
	struct QosOp op;
	const char* type_name = "idfs.checkconn";
	op.ino.val = 0;
	op.len = 0;
	op.uid = 0;
	op.gid = 0;
	op.tenant_name = op_ctx->ctx_export->tenant;
	op.reqdata = NULL;
	op.callback = NULL;
	struct idfs_export *export =
		container_of(op_ctx->fsal_export, struct idfs_export, export);

	if ((op_ctx->caller_addr->ss_family == AF_INET6) && !strncmp(op_ctx->client->hostaddr_str, "::ffff:", 7)) {
		op.client_ip = op_ctx->client->hostaddr_str+7;
		LogFullDebug(COMPONENT_FSAL, "export->cmount = %p || client = %s", export->cmount, op.client_ip);
	} else {
		op.client_ip = op_ctx->client->hostaddr_str;
		LogFullDebug(COMPONENT_FSAL, "export->cmount = %p || client = %s", export->cmount, op.client_ip);
	}
	idfs_vas_set(export->cmount, NULL, type_name, &op, sizeof(op), 0);
	return status;
}

static fsal_status_t revert_conn(struct fsal_export *export_pub){
	fsal_status_t status = { ERR_FSAL_NO_ERROR, 0 };
	struct QosOp op;
	const char* type_name = "idfs.revertconn";
	op.ino.val = 0;
	op.len = 0;
	op.uid = 0;
	op.gid = 0;
	op.tenant_name = op_ctx->ctx_export->tenant;
	op.reqdata = NULL;
	op.callback = NULL;
	struct idfs_export *export =
		container_of(op_ctx->fsal_export, struct idfs_export, export);

	if ((op_ctx->caller_addr->ss_family == AF_INET6) && !strncmp(op_ctx->client->hostaddr_str, "::ffff:", 7)) {
		op.client_ip = op_ctx->client->hostaddr_str+7;
		LogFullDebug(COMPONENT_FSAL, "export->cmount = %p || client = %s", export->cmount, op.client_ip);
	} else {
		op.client_ip = op_ctx->client->hostaddr_str;
		LogFullDebug(COMPONENT_FSAL, "export->cmount = %p || client = %s", export->cmount, op.client_ip);
	}
	idfs_vas_set(export->cmount, NULL, type_name, &op, sizeof(op), 0);
	return status;
}


/**
 * @brief Set operations for exports
 *
 * This function overrides operations that we've implemented, leaving
 * the rest for the default.
 *
 * @param[in,out] ops Operations vector
 */

void export_ops_init(struct export_ops *ops)
{
	ops->prepare_unexport = idfs_prepare_unexport,
	ops->release = release;
	ops->lookup_path = lookup_path;
	ops->host_to_key = host_to_key;
	ops->wire_to_host = wire_to_host;
	ops->create_handle = create_handle;
	ops->get_fs_dynamic_info = get_fs_dynamic_info;
	ops->alloc_state = idfs_alloc_state;
	ops->free_state = idfs_free_state;
	ops->check_qos = check_qos;
	ops->check_conn = check_conn;
	ops->revert_conn = revert_conn;
#ifdef IDFSFS_POSIX_ACL
	ops->fs_acl_support = fs_acl_support;
#endif				/* IDFSFS_POSIX_ACL */
#ifdef IDFS_PNFS
	export_ops_pnfs(ops);
#endif				/* IDFS_PNFS */
}
