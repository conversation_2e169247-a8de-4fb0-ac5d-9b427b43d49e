#ifndef ETCDWATCHER_C_WRAPPER_H
#define ETCDWATCHER_C_WRAPPER_H

#ifdef __cplusplus
extern "C" {
#endif

typedef void (*etcdwatcher_event_callback_c)(const char *key, const char *val);

int etcdwatcher_init_c(void);

int etcdwatcher_start_watch_c(const char *key, etcdwatcher_event_callback_c cb);

int etcdwatcher_destroy_c(void);

int get_db_host_c(char *host, size_t host_size);

#ifdef __cplusplus
}
#endif

#endif /* ETCDWATCHER_C_WRAPPER_H */

