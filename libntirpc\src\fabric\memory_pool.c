#include "config.h"
#include "fabric.h"
#include "fab_common.h"
//#include "memory_pool.h"

#include "../rdma/rdma.h"

int rdma_mem_affinity = -1;

memory_pool* mpool_init(struct mempool_attr* pool_attr)
{
    memory_pool *mpool = (memory_pool*)malloc(sizeof(memory_pool));
    mpool->count = pool_attr->count;
    mpool->chunk_size = pool_attr->chunk_size;
    mpool->total_size = pool_attr->count * pool_attr->chunk_size;
    mpool->attr = pool_attr;
    char *ptr = NULL;
    char *real_ptr = NULL;
    if(pool_attr->use_hugepage){
        //申请大页内存
        size_t real_size = ALIGN_TO_PAGE_2MB(mpool->total_size) + HUGE_PAGE_SIZE_2MB;

        ptr = (char *)mmap(NULL, real_size, PROT_READ | PROT_WRITE, MAP_PRIVATE | MAP_ANONYMOUS | MAP_POPULATE | MAP_HUGETLB, -1, 0);

        if (ptr == MAP_FAILED) {
            assert(0);
            //ptr = (char *)malloc(real_size);
            //if (ptr == NULL) return NULL;
            //real_size = 0;
        }
        *((size_t *)ptr) = real_size;
        real_ptr = ptr + HUGE_PAGE_SIZE_2MB;
    }
    else {
        size_t page_size = pool_attr->mem_align;
        ptr = mem_aligned(page_size, mpool->total_size);
        real_ptr = ptr;
    }
    //内存绑定到cq线程所在的numa
    if (cq_cpu >= 0) {
        unsigned long nodemask;
        //nodemask = 1UL << rdma_mem_affinity;
	int numanode = numa_node_of_cpu(cq_cpu);
	assert(numanode >= 0);
	nodemask = 1UL << numanode;
        int ret = mbind(ptr, mpool->total_size, MPOL_BIND, &nodemask, sizeof(nodemask), MPOL_MF_MOVE);
        assert(ret == 0);
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC mpool mbind numanode  %d .", __func__, numanode);
    }
    mpool->p = real_ptr;
    //申请chunk结构体内存
    mpool->ptr = (chunk*)malloc(sizeof(chunk) * pool_attr->count);
    for(int i = 1; i < pool_attr->count - 1; ++i)
    {
        mpool->ptr[i].prev = &mpool->ptr[i-1];
        mpool->ptr[i].next = &mpool->ptr[i+1];
        mpool->ptr[i].use = 0;
	mpool->ptr[i].buffer = real_ptr + i * mpool->chunk_size;
	probe_count_inc(PROBE_COUNT_FAB_CHUNK_COUNT);
    }

    mpool->ptr[0].prev = NULL;
    mpool->ptr[0].next = &mpool->ptr[1];
    mpool->ptr[0].use = 0;
    mpool->ptr[0].buffer = real_ptr;
  
    mpool->ptr[pool_attr->count - 1].prev = &mpool->ptr[pool_attr->count - 2];
    mpool->ptr[pool_attr->count - 1].next = NULL;
    mpool->ptr[pool_attr->count - 1].use = 0;
    mpool->ptr[pool_attr->count - 1].buffer = real_ptr + (pool_attr->count - 1) * mpool->chunk_size;

    pthread_mutex_init(&(mpool->lock), NULL);
    return mpool;
}

#define  __MEM_CHUNK_LOCK 1
void* get_chunk(memory_pool* mpool, size_t size)
{
#if __MEM_CHUNK_LOCK
    pthread_mutex_lock(&(mpool->lock));
#endif
    if(0 == mpool->count || size > mpool->chunk_size) {
#if __MEM_CHUNK_LOCK
        pthread_mutex_unlock(&(mpool->lock));
#endif
        return NULL;  //内存池无可用内存
    }
 
    for(int i = 0; i < mpool->attr->count; ++i)
    {
        if(mpool->ptr[i].use == 0)
        {
            --mpool->count;
	    probe_count_dec(PROBE_COUNT_FAB_CHUNK_COUNT);
            mpool->ptr[i].use = 1;
#if __MEM_CHUNK_LOCK
            pthread_mutex_unlock(&(mpool->lock));
#endif
            return &mpool->ptr[i];
        }
    }

#if __MEM_CHUNK_LOCK
    pthread_mutex_unlock(&(mpool->lock));
#endif
    return NULL;
}

//释放内存，将该指针所属的链表放回内存池，并修改该链表的use为可用即可。
//其中，首先根据ptr指针，获得原链表的首地址,用到一开始定义的一个宏函数
void free_chunk(memory_pool *mpool, char *ptr)
{
    if (!ptr) {
	assert(0);
        return;
    }

   // assert(ptr >= (char*)mpool->ptr && ptr <= ((char*)mpool->ptr + (mpool->attr->count * sizeof(chunk))));
#if __MEM_CHUNK_LOCK
    pthread_mutex_lock(&(mpool->lock));
#endif

    chunk *c = (chunk*)ptr;
   // assert(c->buffer >= mpool->p && c->buffer <= mpool->p + mpool->total_size);
    c->use = 0;
    ++mpool->count;
    probe_count_inc(PROBE_COUNT_FAB_CHUNK_COUNT);
#if __MEM_CHUNK_LOCK
    pthread_mutex_unlock(&(mpool->lock));
#endif
}

void mpool_free(memory_pool *mpool)
{
    unsetenv("RDMAV_HUGEPAGES_SAFE");
    if (mpool == NULL) {
        return; 
    }
    if (mpool->ptr == NULL) {
	free(mpool);
        return;
    }

    free(mpool->ptr);
#if 0
    void *real_ptr = (char *)mpool->p - HUGE_PAGE_SIZE_2MB;
    size_t real_size = *((size_t *)real_ptr);
    assert(real_size % HUGE_PAGE_SIZE_2MB == 0);
    if (real_size != 0) {
        munmap(real_ptr, real_size);
    } else {
        free(real_ptr);
    }
#else
    mem_free(mpool->p, mpool->total_size);
#endif
    free(mpool);
}
