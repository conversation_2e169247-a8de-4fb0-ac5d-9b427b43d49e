/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright 2016 Red Hat, Inc. and/or its affiliates.
 * Author: <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 */

#ifndef _FSAL_IDFS_STATX_COMPAT_H
#define _FSAL_IDFS_STATX_COMPAT_H

#include "fsal_types.h"
#include "nfs23.h"
#include "nfsv41.h"
#include "gsh_config.h"
#define SYSTEM_IDFS_keyname					"system.acl"
/*
 * Depending on what we'll be doing with the resulting statx structure, we
 * either set the mask for the minimum that construct_handle requires, or a
 * full set of attributes.
 *
 * Note that even though construct_handle accesses the stx_mode field, we
 * don't need to request IDFS_STATX_MODE here, as the type bits are always
 * accessible.
 */
#define IDFS_STATX_HANDLE_MASK	(IDFS_STATX_INO)
#define IDFS_STATX_ATTR_MASK	(IDFS_STATX_BASIC_STATS	|	\
				 IDFS_STATX_BTIME	|	\
				 IDFS_STATX_VERSION)

#ifdef USE_FSAL_IDFS_STATX
static inline UserPerm *
user_cred2idfs(const struct user_cred *cred)
{
	return idfs_userperm_new(cred->caller_uid, cred->caller_gid,
				 cred->caller_glen, cred->caller_garray);
}

static inline int
fsal_idfs_ll_walk(struct idfs_mount_info *cmount, const char *name,
			IdfsInode **i, struct idfs_statx *stx, bool full,
			const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_walk(cmount, name, i, stx,
		full ? IDFS_STATX_ATTR_MASK : IDFS_STATX_HANDLE_MASK, 0, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_getattr(struct idfs_mount_info *cmount, struct IdfsInode *in,
			struct idfs_statx *stx, unsigned int want,
			const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_getattr(cmount, in, stx, want, 0, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_lookup(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, IdfsInode **out, struct idfs_statx *stx,
			bool full, const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_lookup(cmount, parent, name, out, stx,
			full ? IDFS_STATX_ATTR_MASK : IDFS_STATX_HANDLE_MASK,
			0, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_mkdir(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, mode_t mode, IdfsInode **out,
			struct idfs_statx *stx, bool full,
			const struct user_cred *creds, const void* params)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_mkdir(cmount, parent, name, mode, out, stx,
			full ? IDFS_STATX_ATTR_MASK : IDFS_STATX_HANDLE_MASK,
			0, perms, params);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_mknod(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, mode_t mode, dev_t rdev,
			IdfsInode **out, struct idfs_statx *stx, bool full,
			const struct user_cred *creds, const void* params)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_mknod(cmount, parent, name, mode, rdev, out, stx,
			full ? IDFS_STATX_ATTR_MASK : IDFS_STATX_HANDLE_MASK,
			0, perms, params);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_symlink(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, const char *link_path,
			IdfsInode **out, struct idfs_statx *stx, bool full,
			const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_symlink(cmount, parent, name, link_path, out, stx,
			full ? IDFS_STATX_ATTR_MASK : IDFS_STATX_HANDLE_MASK,
			0, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_readlink(struct idfs_mount_info *cmount, IdfsInode *in, char *buf,
		      size_t bufsize, const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_readlink(cmount, in, buf, bufsize, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_create(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, mode_t mode, int oflags,
			IdfsInode **outp, Fh **fhp, struct idfs_statx *stx,
			bool full, const struct user_cred *creds, const void* params)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_create(cmount, parent, name, mode, oflags, outp,
			fhp, stx,
			full ? IDFS_STATX_ATTR_MASK : IDFS_STATX_HANDLE_MASK,
			0, perms, params);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_setattr(struct idfs_mount_info *cmount, IdfsInode *i,
			  struct idfs_statx *stx, unsigned int mask,
			  const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_setattr(cmount, i, stx, mask, perms);
#ifdef USE_FSAL_IDFS_LL_SYNC_INODE
	if (!ret)
		ret = idfs_ll_sync_inode(cmount, i, 0);
#endif /* USE_FSAL_IDFS_LL_SYNC_INODE */
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_open(struct idfs_mount_info *cmount, IdfsInode *i,
		  int flags, Fh **fh, const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_open(cmount, i, flags, fh, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_opendir(struct idfs_mount_info *cmount, struct IdfsInode *in,
		     struct idfs_dir_result **dirpp,
		     const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_opendir(cmount, in, dirpp, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_link(struct idfs_mount_info *cmount, IdfsInode *i, IdfsInode *newparent,
		  const char *name, const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_link(cmount, i, newparent, name, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_unlink(struct idfs_mount_info *cmount, struct IdfsInode *in,
		    const char *name, const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_unlink(cmount, in, name, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_rename(struct idfs_mount_info *cmount, struct IdfsInode *parent,
		    const char *name, struct IdfsInode *newparent,
		    const char *newname, const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_rename(cmount, parent, name, newparent, newname, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_rmdir(struct idfs_mount_info *cmount, struct IdfsInode *in,
		   const char *name, const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_rmdir(cmount, in, name, perms);
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_readdirplus(struct idfs_mount_info *cmount,
		      struct idfs_dir_result *dirp, IdfsInode *dir,
		      struct dirent *de, struct idfs_statx *stx,
		      unsigned int want, unsigned int flags, IdfsInode **out,
		      struct user_cred *cred)
{
	if(op_ctx->nfs_vers == NFS_V3)
		return idfs_readdirplus_r_with_entries(cmount, dirp, de, stx, want, flags, out, nfs_param.core_param.readdir_count_v3);
	else if(op_ctx->nfs_vers == NFS_V4)
		return idfs_readdirplus_r_with_entries(cmount, dirp, de, stx, want, flags, out, nfs_param.core_param.readdir_count_v4);
	else
		return idfs_readdirplus_r_with_entries(cmount, dirp, de, stx, want, flags, out, 0);
}

static inline int
fsal_idfs_ll_getxattr(struct idfs_mount_info *cmount, struct IdfsInode *in,
			const char *name, char *val, size_t size,
			const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_getxattr(cmount, in, name, val, size, perms);
	if (ret < 0) {
		LogDebug(COMPONENT_IDFS_ACL, "failed to idfs_ll_getxattr, ret: %d", ret);
	}
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_setxattr(struct idfs_mount_info *cmount, struct IdfsInode *in,
			const char *name, char *val, size_t size, int flags,
			const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_setxattr(cmount, in, name, val, size, flags, perms);
        if (ret < 0) {
                LogDebug(COMPONENT_IDFS_ACL, "failed to idfs_ll_setxattr, ret: %d", ret);
        }
	idfs_userperm_destroy(perms);
	return ret;
}

static inline int
fsal_idfs_ll_removexattr(struct idfs_mount_info *cmount, struct IdfsInode *in,
			 const char *name, const struct user_cred *creds)
{
	int ret;
	UserPerm *perms = user_cred2idfs(creds);

	if (!perms)
		return -ENOMEM;

	ret = idfs_ll_removexattr(cmount, in, name, perms);
	idfs_userperm_destroy(perms);
	return ret;
}
#else /* USE_FSAL_IDFS_STATX */

#ifndef AT_NO_ATTR_SYNC
#define AT_NO_ATTR_SYNC		0x4000
#endif /* AT_NO_ATTR_SYNC */

struct idfs_statx {
	uint32_t	stx_mask;
	uint32_t	stx_blksize;
	uint32_t	stx_nlink;
	uint32_t	stx_uid;
	uint32_t	stx_gid;
	uint16_t	stx_mode;
	uint64_t	stx_ino;
	uint64_t	stx_size;
	uint64_t	stx_blocks;
	dev_t		stx_dev;
	dev_t		stx_rdev;
	struct timespec	stx_atime;
	struct timespec	stx_ctime;
	struct timespec	stx_mtime;
	struct timespec	stx_btime;
	uint64_t	stx_version;
};

#define IDFS_STATX_MODE		0x00000001U     /* Want/got stx_mode */
#define IDFS_STATX_NLINK	0x00000002U     /* Want/got stx_nlink */
#define IDFS_STATX_UID		0x00000004U     /* Want/got stx_uid */
#define IDFS_STATX_GID		0x00000008U     /* Want/got stx_gid */
#define IDFS_STATX_RDEV		0x00000010U     /* Want/got stx_rdev */
#define IDFS_STATX_ATIME	0x00000020U     /* Want/got stx_atime */
#define IDFS_STATX_MTIME	0x00000040U     /* Want/got stx_mtime */
#define IDFS_STATX_CTIME	0x00000080U     /* Want/got stx_ctime */
#define IDFS_STATX_INO		0x00000100U     /* Want/got stx_ino */
#define IDFS_STATX_SIZE		0x00000200U     /* Want/got stx_size */
#define IDFS_STATX_BLOCKS	0x00000400U     /* Want/got stx_blocks */
#define IDFS_STATX_BASIC_STATS	0x000007ffU     /* posix stat struct fields */
#define IDFS_STATX_BTIME	0x00000800U     /* Want/got stx_btime */
#define IDFS_STATX_VERSION	0x00001000U     /* Want/got stx_version */
#define IDFS_STATX_ALL_STATS	0x00001fffU     /* All supported stats */

int fsal_idfs_ll_walk(struct idfs_mount_info *cmount, const char *name,
			IdfsInode **i, struct idfs_statx *stx, bool full,
			const struct user_cred *cred);
int fsal_idfs_ll_getattr(struct idfs_mount_info *cmount, struct IdfsInode *in,
			struct idfs_statx *stx, unsigned int want,
			const struct user_cred *cred);
int fsal_idfs_ll_lookup(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, IdfsInode **out, struct idfs_statx *stx,
			bool full, const struct user_cred *cred);
int fsal_idfs_ll_mkdir(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, mode_t mode, IdfsInode **out,
			struct idfs_statx *stx, bool full,
			const struct user_cred *cred);
#ifdef USE_FSAL_IDFS_MKNOD
int fsal_idfs_ll_mknod(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, mode_t mode, dev_t rdev,
			IdfsInode **out, struct idfs_statx *stx, bool full,
			const struct user_cred *cred);
#endif /* USE_FSAL_IDFS_MKNOD */
int fsal_idfs_ll_symlink(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, const char *link_path,
			IdfsInode **out, struct idfs_statx *stx, bool full,
			const struct user_cred *cred);
int fsal_idfs_ll_create(struct idfs_mount_info *cmount, IdfsInode *parent,
			const char *name, mode_t mode, int oflags,
			IdfsInode **outp, Fh **fhp, struct idfs_statx *stx,
			bool full, const struct user_cred *cred);
int fsal_idfs_ll_setattr(struct idfs_mount_info *cmount, IdfsInode *i,
			 struct idfs_statx *stx, unsigned int mask,
			 const struct user_cred *cred);
int fsal_idfs_readdirplus(struct idfs_mount_info *cmount,
			  struct idfs_dir_result *dirp, IdfsInode *dir,
			  struct dirent *de, struct idfs_statx *stx,
			  unsigned int want, unsigned int flags, IdfsInode **out,
			  struct user_cred *cred);

static inline int
fsal_idfs_ll_readlink(struct idfs_mount_info *cmount, IdfsInode *in, char *buf,
		      size_t bufsize, const struct user_cred *creds)
{
	return idfs_ll_readlink(cmount, in, buf, bufsize, creds->caller_uid,
				creds->caller_gid);
}

static inline int
fsal_idfs_ll_open(struct idfs_mount_info *cmount, IdfsInode *i,
		  int flags, Fh **fh, const struct user_cred *cred)
{
	return idfs_ll_open(cmount, i, flags, fh, cred->caller_uid,
				cred->caller_gid);
}

static inline int
fsal_idfs_ll_opendir(struct idfs_mount_info *cmount, struct IdfsInode *in,
		     struct idfs_dir_result **dirpp,
		     const struct user_cred *cred)
{
	return idfs_ll_opendir(cmount, in, dirpp, cred->caller_uid,
				cred->caller_gid);
}

static inline int
fsal_idfs_ll_link(struct idfs_mount_info *cmount, IdfsInode *i, IdfsInode *newparent,
		  const char *name, const struct user_cred *cred)
{
	struct stat	st;

	return idfs_ll_link(cmount, i, newparent, name, &st, cred->caller_uid,
			    cred->caller_gid);
}

static inline int
fsal_idfs_ll_unlink(struct idfs_mount_info *cmount, struct IdfsInode *in,
		    const char *name, const struct user_cred *cred)
{
	return idfs_ll_unlink(cmount, in, name, cred->caller_uid,
			      cred->caller_gid);
}

static inline int
fsal_idfs_ll_rename(struct idfs_mount_info *cmount, struct IdfsInode *parent,
		    const char *name, struct IdfsInode *newparent,
		    const char *newname, const struct user_cred *cred)
{
	return idfs_ll_rename(cmount, parent, name, newparent, newname,
				cred->caller_uid, cred->caller_gid);
}

static inline int
fsal_idfs_ll_rmdir(struct idfs_mount_info *cmount, struct IdfsInode *in,
		   const char *name, const struct user_cred *cred)
{
	return idfs_ll_rmdir(cmount, in, name, cred->caller_uid,
			     cred->caller_gid);
}

static inline int
fsal_idfs_ll_getxattr(struct idfs_mount_info *cmount, struct IdfsInode *in,
		      const char *name, char *val, size_t size,
		      const struct user_cred *cred)
{
	return idfs_ll_getxattr(cmount, in, name, val, size,
				cred->caller_uid, cred->caller_gid);
}

static inline int
fsal_idfs_ll_setxattr(struct idfs_mount_info *cmount, struct IdfsInode *in,
		      const char *name, char *val, size_t size, int flags,
		      const struct user_cred *cred)
{
	return idfs_ll_setxattr(cmount, in, name, val, size, flags,
				cred->caller_uid, cred->caller_gid);
}

static inline int
fsal_idfs_ll_removexattr(struct idfs_mount_info *cmount, struct IdfsInode *in,
			 const char *name, const struct user_cred *cred)
{
	return idfs_ll_removexattr(cmount, in, name, cred->caller_uid,
				   cred->caller_gid);
}
#endif /* USE_FSAL_IDFS_STATX */
#endif /* _FSAL_IDFS_STATX_COMPAT_H */
