#ifndef LIBNTIRPC_COMMON_UTILS_H
#define LIBNTIRPC_COMMON_UTILS_H
#include <time.h>
#include <assert.h>
#include <pthread.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>
#include <netdb.h>
typedef uint64_t nsecs_elapsed_t;

#define NS_PER_USEC ((nsecs_elapsed_t) 1000)
#define NS_PER_MSEC ((nsecs_elapsed_t) 1000000)
#define NS_PER_SEC  ((nsecs_elapsed_t) 1000000000)

static inline void now(struct timespec *ts)
{
	int rc;
	rc = clock_gettime(CLOCK_REALTIME, ts);
	if (rc != 0) {
		assert(0);	/* if this is broken, we are toast so die */
	}
}
/**
 * @brief Get the abs difference between two timespecs in nsecs
 *
 * useful for cheap time calculation. Works with Dr. Who...
 *
 * @param[in] start timespec of before end
 * @param[in] end   timespec after start time
 *
 * @return Elapsed time in nsecs
 */

static inline nsecs_elapsed_t
timespec_diff(const struct timespec *start,
	      const struct timespec *end)
{
	if ((end->tv_sec > start->tv_sec)
	    || (end->tv_sec == start->tv_sec
		&& end->tv_nsec >= start->tv_nsec)) {
		return (end->tv_sec - start->tv_sec) * NS_PER_SEC +
		    (end->tv_nsec - start->tv_nsec);
	} else {
		return (start->tv_sec - end->tv_sec) * NS_PER_SEC +
		    (start->tv_nsec - end->tv_nsec);
	}
}

void server_stats_rpc_done(nsecs_elapsed_t reply_time,nsecs_elapsed_t request_time, void *xdrs);
#endif
