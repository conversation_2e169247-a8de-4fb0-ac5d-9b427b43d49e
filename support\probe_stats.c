/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright (C) Panasas Inc., 2013
 * Author: <PERSON> j<PERSON>@panasas.com
 *
 * contributeur : <PERSON>   <EMAIL>
 *                Thomas LEIBOVICI  <EMAIL>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

/**
 * @defgroup Server statistics management
 * @{
 */

/**
 * @file server_stats.c
 * <AUTHOR> <<EMAIL>>
 * @brief FSAL module manager
 */

#include "config.h"

#include <time.h>
#include <unistd.h>
#include <sys/types.h>
#include <stdint.h>
#include <sys/param.h>
#include <pthread.h>
#include <assert.h>
#include <arpa/inet.h>
#include "fsal.h"
#include "nfs_core.h"
#include "log.h"
#include "avltree.h"
#include "gsh_types.h"
#ifdef USE_DBUS
#include "gsh_dbus.h"
#endif
#include "client_mgr.h"
#include "export_mgr.h"
#include "server_stats.h"
#include <abstract_atomic.h>
#include "mem_pool.h"
#include "nfs_proto_functions.h"

#include "server_stats_private.h"
#include "probe_stats.h"



/* probe_stats_delay->probe_op_delay_stats */
struct probe_stats_delay g_probe_delay_stats = {
	.total_ops = PROBE_DELAY_MAX,
};

/* probe_stats_delay->probe_op_delay_stats */
struct probe_stats_count g_probe_count_stats = {
	.total_ops = PROBE_COUNT_MAX,
};

//__thread static struct timespec start_time;
//__thread static struct timespec stop_time;


#ifdef USE_DBUS
/** @fn fsal_gpfs_extract_stats(struct fsal_module *fsal_hdl, void *iter)
 *  *  @brief Extract the FSAL specific performance counters
 *   */
void print_probe_delay_stats(void *iter)
{
	DBusMessageIter struct_iter;
	DBusMessageIter *iter1 = (DBusMessageIter *)iter;
	char *message;
	uint64_t receive_total_ops, total_ops, total_resp, min_resp, max_resp, op_counter = 0;
	double res = 0.0;
	int i;
	struct probe_stats_delay *probe_stats = &g_probe_delay_stats;
	
	probe_ops_delay_init(&g_probe_delay_stats);
	message = "PROBE";
	dbus_message_iter_append_basic(iter1, DBUS_TYPE_STRING, &message);

	dbus_message_iter_open_container(iter1, DBUS_TYPE_STRUCT, NULL,
					 &struct_iter);
	for (i = 0; i < PROBE_DELAY_MAX; i++) {
		
		receive_total_ops = atomic_fetch_uint64_t(
				&probe_stats->op_stats[i].receive_num_ops);

		total_ops = atomic_fetch_uint64_t(
				&probe_stats->op_stats[i].num_ops);
		if (receive_total_ops == 0)
			continue;

		total_resp = atomic_fetch_uint64_t(
				&probe_stats->op_stats[i].resp_time);
		min_resp = atomic_fetch_uint64_t(
				&probe_stats->op_stats[i].resp_time_min);
		max_resp = atomic_fetch_uint64_t(
				&probe_stats->op_stats[i].resp_time_max);
		/* We have valid stats, send it across */
		message = probe_stats->probe_name[i].name;
		dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_STRING, &message);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &receive_total_ops);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &total_ops);
		res = (double) total_resp * 0.000001 / total_ops;
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		res = (double) min_resp * 0.000001;
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		res = (double) max_resp * 0.000001;
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		op_counter += total_ops;
	}
	{
		receive_total_ops = atomic_fetch_uint64_t(
				&probe_stats->op_total_stats.receive_num_ops);

		total_ops = atomic_fetch_uint64_t(
				&probe_stats->op_total_stats.num_ops);
		if (total_ops != 0){
			total_resp = atomic_fetch_uint64_t(
					&probe_stats->op_total_stats.resp_time);
			min_resp = atomic_fetch_uint64_t(
					&probe_stats->op_total_stats.resp_time_min);
			max_resp = atomic_fetch_uint64_t(
					&probe_stats->op_total_stats.resp_time_max);
			/* We have valid stats, send it across */
			message = "Total";
			dbus_message_iter_append_basic(&struct_iter,
					DBUS_TYPE_STRING, &message);
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_UINT64, &receive_total_ops);
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_UINT64, &total_ops);
			res = (double) total_resp * 0.000001 / total_ops;
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_DOUBLE, &res);
			res = (double) min_resp * 0.000001;
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_DOUBLE, &res);
			res = (double) max_resp * 0.000001;
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_DOUBLE, &res);
		}
	}
	if (op_counter == 0) {
		message = "None";
		/* insert dummy stats to avoid dbus crash */
		dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_STRING, &message);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &receive_total_ops);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &total_ops);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
	} else {
		message = "OK";
	}
	dbus_message_iter_close_container(iter1, &struct_iter);
	dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &message);
}
void print_probe_count_stats(void *iter)
{
	DBusMessageIter struct_iter;
	DBusMessageIter *iter1 = (DBusMessageIter *)iter;
	char *message;
	uint64_t receive_total_ops  = 0;
	uint64_t pad = 0;
	double res = 0.0;
	int i;
	struct probe_stats_count *probe_stats = &g_probe_count_stats;
	
	probe_ops_count_init(&g_probe_count_stats);
	message = "PROBE_COUNT";
	dbus_message_iter_append_basic(iter1, DBUS_TYPE_STRING, &message);

	dbus_message_iter_open_container(iter1, DBUS_TYPE_STRUCT, NULL,
					 &struct_iter);
	for (i = 0; i < PROBE_COUNT_MAX; i++) {
		
		receive_total_ops = atomic_fetch_uint64_t(
				&probe_stats->op_stats[i].count);
		/* We have valid stats, send it across */
		message = probe_stats->op_stats[i].name;
		dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_STRING, &message);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &receive_total_ops);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &pad);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
	}

	message = "Total";
	dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_STRING, &message);
	dbus_message_iter_append_basic(&struct_iter,
		DBUS_TYPE_UINT64, &pad);
	dbus_message_iter_append_basic(&struct_iter,
		DBUS_TYPE_UINT64, &pad);
	dbus_message_iter_append_basic(&struct_iter,
		DBUS_TYPE_DOUBLE, &res);
	dbus_message_iter_append_basic(&struct_iter,
		DBUS_TYPE_DOUBLE, &res);
	dbus_message_iter_append_basic(&struct_iter,
		DBUS_TYPE_DOUBLE, &res);

	message = "OK";
	dbus_message_iter_close_container(iter1, &struct_iter);
	dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &message);
}
void print_probe_rdma_clients(void *iter)
{
	DBusMessageIter struct_iter;
	DBusMessageIter *iter1 = (DBusMessageIter *)iter;
	char *message;
	uint64_t pad = 0;
	double res = 0.0;
	int i;
	
	probe_ops_count_init(&g_probe_count_stats);
	message = "PROBE_RDMA_CLIENTS";
	dbus_message_iter_append_basic(iter1, DBUS_TYPE_STRING, &message);

	dbus_message_iter_open_container(iter1, DBUS_TYPE_STRUCT, NULL,
					 &struct_iter);
	char* str_bufs[40];
	int client_nums = 0;
	svc_rdma_fabric_getclients(str_bufs, 40, &client_nums);
	for (i = 0; i < client_nums; i++) {

		/* We have valid stats, send it across */
		message = str_bufs[i];
		dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_STRING, &message);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &pad);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &pad);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		if(str_bufs[i])
			free(str_bufs[i]);
	}

	message = "OK";
	dbus_message_iter_close_container(iter1, &struct_iter);
	dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &message);
}
void print_probe_rdma_del_clients(void *iter)
{
	DBusMessageIter struct_iter;
	DBusMessageIter *iter1 = (DBusMessageIter *)iter;
	char *message;
	uint64_t pad = 0;
	double res = 0.0;
	int i;
	
	probe_ops_count_init(&g_probe_count_stats);
	message = "PROBE_RDMA_CLIENTS";
	dbus_message_iter_append_basic(iter1, DBUS_TYPE_STRING, &message);

	dbus_message_iter_open_container(iter1, DBUS_TYPE_STRUCT, NULL,
					 &struct_iter);
	char* str_bufs[40];
	int client_nums = 0;
	svc_rdma_fabric_getdelclients(str_bufs, 40, &client_nums);
	for (i = 0; i < client_nums; i++) {

		/* We have valid stats, send it across */
		message = str_bufs[i];
		dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_STRING, &message);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &pad);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &pad);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		if(str_bufs[i])
			free(str_bufs[i]);
	}

	message = "OK";
	dbus_message_iter_close_container(iter1, &struct_iter);
	dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &message);
}

#endif   /* USE_DBUS */

void probe_reset_stats(void)
{
	int i;
	struct probe_stats_delay *stats = &g_probe_delay_stats;


	/* reset all the counters */
	for (i = 0; i < PROBE_DELAY_MAX; i++) {
		atomic_store_uint64_t(
				&stats->op_stats[i].receive_num_ops, 0);
		atomic_store_uint64_t(
				&stats->op_stats[i].num_ops, 0);
		atomic_store_uint64_t(
				&stats->op_stats[i].resp_time, 0);
		atomic_store_uint64_t(
				&stats->op_stats[i].resp_time_min, 0);
		atomic_store_uint64_t(
				&stats->op_stats[i].resp_time_max, 0);
	}
	
	atomic_store_uint64_t(
			&stats->op_total_stats.receive_num_ops, 0);
	atomic_store_uint64_t(
			&stats->op_total_stats.num_ops, 0);
	atomic_store_uint64_t(
			&stats->op_total_stats.resp_time, 0);
	atomic_store_uint64_t(
			&stats->op_total_stats.resp_time_min, 0);
	atomic_store_uint64_t(
			&stats->op_total_stats.resp_time_max, 0);
}
bool probe_stat_begin(uint32_t ops)
{
	struct probe_stats_delay *stats = &g_probe_delay_stats;

	(void)atomic_inc_uint64_t(&stats->op_stats[ops].receive_num_ops);
	(void)atomic_inc_uint64_t(&stats->op_total_stats.receive_num_ops);

	return true;
}
uint64_t probe_stat_end(uint32_t idfs_ops, struct timespec *start_time, struct timespec *stop_time)
{
	//struct timespec stop_time;
	nsecs_elapsed_t resp_time = 0;

	struct probe_stats_delay *stats = &g_probe_delay_stats;

	//now(&stop_time);
	resp_time = timespec_diff(start_time, stop_time);

	/* record FSAL stats */
	(void)atomic_inc_uint64_t(&stats->op_stats[idfs_ops].num_ops);
	(void)atomic_inc_uint64_t(&stats->op_total_stats.num_ops);

	(void)atomic_add_uint64_t(&stats->op_stats[idfs_ops].resp_time,
		resp_time);
	(void)atomic_add_uint64_t(&stats->op_total_stats.resp_time,
		resp_time);

	if (stats->op_stats[idfs_ops].resp_time_max < resp_time)
		stats->op_stats[idfs_ops].resp_time_max = resp_time;
	if (stats->op_stats[idfs_ops].resp_time_min == 0 ||
		stats->op_stats[idfs_ops].resp_time_min > resp_time)
		stats->op_stats[idfs_ops].resp_time_min = resp_time;
	if (stats->op_total_stats.resp_time_max < resp_time)
		stats->op_total_stats.resp_time_max = resp_time;
	if (stats->op_total_stats.resp_time_min == 0 ||
		stats->op_total_stats.resp_time_min > resp_time)
		stats->op_total_stats.resp_time_min = resp_time;
	return true;
}

void probe_stat_xprt_begin(struct svc_xprt *xprt, uint16_t ops){
	if (nfs_param.core_param.enable_PROBEDELAY) {
		now(&xprt->probe_time_start);
		probe_stat_begin(ops);
	}
	return ;
}

void probe_stat_xprt_end(struct svc_xprt *xprt, uint16_t ops){
	if (nfs_param.core_param.enable_PROBEDELAY) {
		struct timespec stop_time;
		now(&stop_time);
		probe_stat_end(ops, &xprt->probe_time_start, &stop_time);
	}
	return;
}

void probe_stat_time_begin(struct timespec *start_timespec, uint16_t ops){
	if (nfs_param.core_param.enable_PROBEDELAY) {
		now(start_timespec);
		probe_stat_begin(ops);
	}
	return ;
}

void probe_stat_time_end(struct timespec *start_timespec, uint16_t ops){
	if (nfs_param.core_param.enable_PROBEDELAY) {
		struct timespec stop_time;
		now(&stop_time);
		probe_stat_end(ops, start_timespec, &stop_time);
	}
	return;
}

void probe_stat_count_inc(uint16_t ops){
	if (nfs_param.core_param.enable_PROBECOUNT) {
		atomic_add_uint64_t(&g_probe_count_stats.op_stats[ops].count, 1);
	}
	return ;
}

void probe_stat_count_dec(uint16_t ops){
	if (nfs_param.core_param.enable_PROBECOUNT) {
		atomic_sub_uint64_t(&g_probe_count_stats.op_stats[ops].count, 1);
	}
	return;
}

/** @} */
