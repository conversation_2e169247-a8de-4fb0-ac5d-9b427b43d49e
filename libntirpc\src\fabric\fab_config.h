#ifndef _CONFIG_H_
#define _CONFIG_H_

#include <stdlib.h>
#include <inttypes.h>
#include <netinet/tcp.h>
#include <sys/uio.h>
#include <stdbool.h>
#include <stdio.h>
#include <getopt.h>
#include <assert.h>
#include <rdma/fabric.h>
#include <rdma/fi_cm.h>
#include <rdma/fi_domain.h>
#include <rdma/fi_endpoint.h>
#include <rdma/fi_errno.h>
#include <rdma/fi_ext.h>
#include <rdma/fi_rma.h>
#include <rdma/fi_tagged.h>
#include "../rpc_rdma.h"
#include "../rpc_com.h"
#include "../svc_internal.h"
#include "../svc_xprt.h"
#include "../svc_internal.h"
#include "../rdma/rdma.h"

#define FIVER FI_VERSION(1,20)
#ifdef __cplusplus
extern "C" {
#endif
    typedef struct mempool_attr
    {
        uint32_t count;
        uint32_t chunk_size;
        uint32_t mem_align;
        bool use_hugepage;
    } mempool_attr;

    typedef struct config
    {
        struct fi_info *hints;
        struct fi_cq_attr cq_attr;
        struct fi_eq_attr eq_attr;
        struct fi_wait_attr wait_attr;
        struct fi_poll_attr poll_attr;
        struct mempool_attr pool_attr;
        struct mempool_attr gane_pool_attr;
    } config;

    config *init_config(const struct rpc_rdma_attr *attr);
#ifdef __cplusplus
}
#endif
#endif

