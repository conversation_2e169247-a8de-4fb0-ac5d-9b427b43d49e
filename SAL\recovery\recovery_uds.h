/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright 2017 Red Hat, Inc. and/or its affiliates.
 * Author: <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */
#ifndef _RECOVERY_UDS_H
#define _RECOVERY_UDS_H
#include <stdio.h>
#include "gsh_refstr.h"

/* A uds key is a decimal string of a uint64_t value which has at most 20
 * digits plus 1 for NUL
 */
#define UDS_KEY_MAX_LEN	21
#define UDS_VAL_MAX_LEN	PATH_MAX

extern uds_t			uds_recov_cluster;
extern uds_ioctx_t		uds_recov_io_ctx;
extern struct gsh_refstr	*uds_recov_oid;
extern struct gsh_refstr	*uds_recov_old_oid;

struct uds_kv_parameter {
	/** Connection to idfs cluster */
	char *idfs_conf;
	/** User ID to idfs cluster */
	char *userid;
	/** Pool for client info */
	char *pool;
	/** Namespace for objects within the pool **/
	char *namespace;
	/** uds_cluster grace database OID */
	char *grace_oid;
	/** uds_cluster node_id */
	char *nodeid;
};
extern struct uds_kv_parameter uds_kv_param;

/* Callback for uds_kv_traverse */
struct pop_args {
	add_clid_entry_hook add_clid_entry;
	add_rfh_entry_hook add_rfh_entry;
	bool old;
	bool takeover;
};
typedef void (*pop_clid_entry_t)(char *, char *, size_t,  struct pop_args *);

int uds_kv_connect(uds_ioctx_t *io_ctx, const char *userid,
			const char *conf, const char *pool, const char *ns);
void uds_kv_shutdown(void);
int uds_kv_put(char *key, char *val, char *object);
int uds_kv_get(char *key, char *val, char *object);
void uds_kv_add_clid(nfs_client_id_t *clientid);
void uds_kv_rm_clid(nfs_client_id_t *clientid);
void uds_kv_add_revoke_fh(nfs_client_id_t *delr_clid, nfs_fh4 *delr_handle);

/**
 * Convert a clientid into a uds key.
 *
 * @param(in)     clientid  The clientid to conevrt into a key
 * @param(in/out) key       The string buffer to put the key in
 * @param(in)     size      Buffer size - expected to be UDS_KEY_MAX_LEN
 */
static inline
void uds_kv_create_key(nfs_client_id_t *clientid, char *key, size_t size)
{
	assert(size == UDS_KEY_MAX_LEN);

	/* Can't overrun UDS_KEY_MAX_LEN and shouldn't return EOVERFLOW or
	 * EINVAL
	 */
	(void) snprintf(key, size, "%lu", (uint64_t)clientid->cid_clientid);
}

char *uds_kv_create_val(nfs_client_id_t *clientid, size_t *len);
int uds_kv_traverse(pop_clid_entry_t callback, struct pop_args *args,
			const char *object);
void uds_kv_add_revoke_fh(nfs_client_id_t *delr_clid, nfs_fh4 *delr_handle);

void uds_ng_pop_clid_entry(char *key, char *val, size_t val_len,
			     struct pop_args *pop_args);
int uds_kv_get_nodeid(char **pnodeid);
#endif	/* _RECOVERY_UDS_H */
