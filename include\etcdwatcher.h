#ifndef _ETCD_WATCHER_H_
#define _ETCD_WATCHER_H_

#include "etcdapi.h"

/**
 * @brief  当 etcd 中某个 key 值发生变化后，将回调这个函数。
 * @param  key 发生变化的 key
 * @param  val key 对应的新值
 */
typedef void(*etcdwatcher_event_callback)(const char *key, const char *val);

/**
 * @brief  初始化 etcd watcher，创建 etcd::Client。
 *         依赖 get_endpoint_url() / get_cert_path() 等函数在 etcdapi.cpp 已经实现。
 * @return 0 表示成功，非 0 表示失败
 */
int etcdwatcher_init(void);

/**
 * @brief  启动对某个 key 的 watch
 * @param  key 要 watch 的 etcd key
 * @param  cb  发生变化时调用的回调函数
 * @return 0 表示成功，非 0 表示失败
 */
int etcdwatcher_start_watch(const char *key, etcdwatcher_event_callback cb);

/**
 * @brief  销毁 watcher，释放资源。会取消所有已启动的 watch。
 * @return 0 表示成功，非 0 表示失败
 */
 
int etcdwatcher_destroy(void);

#endif // _ETCD_WATCHER_H_
