#ifndef _FABRIC_MEMPOOL
#define _FABRIC_MEMPOOL
#include<stdio.h>
#include<stdint.h>
#include<stdlib.h>
#include<pthread.h>
#include<sys/mman.h>
#ifdef __cplusplus
extern "C" {
#endif

#define GET_OFFSET(TYPE, NAME)  ((uint64_t)(&(((TYPE*)0)->NAME)))

#define HUGE_PAGE_SIZE_2MB (2 * 1024 * 1024)
#define ALIGN_TO_PAGE_2MB(x) \
    (((x) + (HUGE_PAGE_SIZE_2MB - 1)) & ~(HUGE_PAGE_SIZE_2MB - 1))

extern int rdma_mem_affinity;

typedef struct chunk
{
    struct chunk *prev;
    struct chunk *next;
    uint8_t use;       //标记该链表是否可用
    char* buffer;    //数据域部分
} chunk;

typedef struct memory_pool
{
    uint32_t count;
    uint32_t chunk_size;
    uint64_t total_size;
    chunk *ptr;
    char* p;
    struct mempool_attr *attr;
    pthread_mutex_t lock;
} memory_pool;

memory_pool* mpool_init(struct mempool_attr* pool_attr);

void* get_chunk(memory_pool* mpool, size_t size);

void free_chunk(memory_pool* mpool, char *ptr);

void mpool_free(memory_pool* mpool);

#ifdef __cplusplus
}
#endif
#endif
