#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/time.h>
#include <syslog.h>
#include <stdio.h>
#include <stddef.h>
#include <assert.h>
#include <reentrant.h>//add by z<PERSON><PERSON>
#include <pthread.h>
#include <malloc.h>
#include <sys/mman.h>

#include "gsh_config.h"
#include "abstract_atomic.h"
#include "mem_pool.h"

#define container_of(addr, type, member) ({			\
			const typeof(((type *) 0)->member) * __mptr = (addr);	\
			(type *)((char *) __mptr - offsetof(type, member)); })


#define MEM_LIST_NUM	8 //???????е????
//#define MEM_UNIT_NUM	2000


struct mem_list g_memlist[MEM_LIST_NUM];

/*mempool polling index*/
unsigned int g_uiIndex;
/*mempool unit total count*/
unsigned int g_unitcount;
/*mempool unit used count*/
unsigned int g_allocnum;

/*add by z<PERSON><PERSON> for mem stat*/

struct stat_mem_data stat_mem;

void mem_stat_init()
{
	pthread_mutex_init(&stat_mem.stat_gsh_lock, NULL);
	stat_mem.alloc_gsh_num = 0;
	stat_mem.alloc_gsh_size = 0;
}

/*add by zhanghao for mem stat*/

char 	*pool = NULL;
unsigned long mem_len = 0;

void mem_pool_clean(int idx)
{
	int i = 0;

	for(i = 0; i < idx; i++)
	{
		free(g_memlist[i].free_addr);
	}

	if (pool)
	{
		free(pool);
		pool = NULL;
	}
	return;
}

int mem_pool_create(uint64_t mem_unit_count ,int mem_unit_size)
{
	int listnum, lastlistnum;
	int i,j, num;

	if (MEM_LIST_NUM > mem_unit_count)
	{
		return -1;
	}

	/* ?????????buf */
	g_unitcount = mem_unit_count;
	g_allocnum = 0;
	mem_len = mem_unit_count * mem_unit_size ;
	//pool = (char*)calloc(1 ,mem_len);
	pool =(char*) memalign(PAGE_SIZE, mem_len);
	if(!pool)
	{
		return -1;
	}
	memset(pool,0,mem_len);

	/* ????????б????????????? */
	listnum = mem_unit_count / MEM_LIST_NUM;
	lastlistnum = listnum + mem_unit_count % MEM_LIST_NUM;

	for(i = 0; i < MEM_LIST_NUM; i++)
	{
		num = listnum;
		if (i == MEM_LIST_NUM - 1)
		{
			num = lastlistnum;
		}

		g_memlist[i].unit_num = num;
		g_memlist[i].unit_size = mem_unit_size;

		g_memlist[i].head = (struct mem_unit*)calloc(1, num*sizeof(struct mem_unit));
		if (g_memlist[i].head == NULL)
		{
			mem_pool_clean(i);
			return -1;
		}
		pthread_mutex_init(&g_memlist[i].mem_mutex, NULL);
		g_memlist[i].start_addr = pool + i * listnum * mem_unit_size;
		g_memlist[i].free_addr = (char*)g_memlist[i].head;

		for(j = 0; j < num -1; j++)
		{
			g_memlist[i].head[j].next = &g_memlist[i].head[j+1];
			g_memlist[i].head[j].current = pool + (i * listnum + j) * mem_unit_size;
		}
		g_memlist[i].head[j].next = NULL;
		g_memlist[i].head[j].current = pool + (i * listnum + j)* mem_unit_size;
	}

	return 0;
}

char * pop_head(struct mem_list *pstmemlist)
{
	struct mem_unit *tmp = pstmemlist->head;

	if(tmp)
	{
		pstmemlist->head = pstmemlist->head->next;
		tmp->next = NULL;
		__sync_fetch_and_add(&g_allocnum, 1);
		return tmp->current ;
	}
	return NULL;
}

unsigned int mem_get_listidx()
{
	unsigned int idx = g_uiIndex % MEM_LIST_NUM;

	__sync_fetch_and_add(&g_uiIndex,1);
	// printf("g_uiIndex %d\n", g_uiIndex);
	if(g_uiIndex > 127)
		__sync_fetch_and_and(&g_uiIndex,0);

	return idx;
}

char * mem_unit_alloc()
{
	unsigned int idx;
	char *buf = NULL;
	int i=0;

	do
	{
		idx = mem_get_listidx();
		// printf("idx %d\n", idx);
		if(i > MEM_LIST_NUM * 2){
			return NULL ;
		}

		pthread_mutex_lock(&g_memlist[idx].mem_mutex);

		buf = pop_head(&g_memlist[idx]);

		pthread_mutex_unlock(&g_memlist[idx].mem_mutex);

		i++ ;

	}while(!buf);

	return buf;
}

int push_head(char *mem)
{
	int offset, idx, idx_of_list;
	struct mem_unit* mu = NULL;

	if (mem == NULL)
		return -1;
	if (g_memlist[0].unit_size <= 0)
        	return -1;
	offset = mem - g_memlist[0].start_addr;
	offset = offset/g_memlist[0].unit_size ;
	idx = offset/g_memlist[0].unit_num;
	idx_of_list = offset % g_memlist[idx].unit_num;
 
	pthread_mutex_lock(&g_memlist[idx].mem_mutex);
	mu = (struct mem_unit*)(g_memlist[idx].free_addr + idx_of_list * sizeof(struct mem_unit));
	mu->next = g_memlist[idx].head;
	g_memlist[idx].head = mu;
	pthread_mutex_unlock(&g_memlist[idx].mem_mutex);

	__sync_fetch_and_sub(&g_allocnum,1);

	return 0;
}

int mem_unit_free(char *buf)
{
	// printf("in mem_unit_free\n");
	unsigned long addr = (unsigned long)buf;
	if ((addr < (unsigned long)pool)
			|| addr > (unsigned long)(pool + mem_len -1))
	{
		return -1;
	}
	else
	{
		push_head(buf);
		//mh.alloc_num-- ;
		return 0;
	}

	return 0;
}

int get_alloc_count()
{
	return g_allocnum;
}

int is_mempool_empty()
{
	if(g_allocnum == g_unitcount)
	{
		return 1 ;
	}

	return 0 ;
}

int is_in_mempool(char *buf)
{
	unsigned long addr = (unsigned long)buf;
	if ((addr < (unsigned long)pool)
			|| addr > (unsigned long)(pool + mem_len -1))
	{
		return 0; // not in mempool
	}

	return 1 ;
}

int mem_pool_init(uint64_t mem_unit_num)
{
	/* ??????д????? */
	return mem_pool_create(mem_unit_num, MEM_UNIT_SIZE);
}

void mem_pool_finalize()
{
	mem_pool_clean(MEM_LIST_NUM);

	return;
}

void
gsh_pool_free__(void *p, size_t size)
{
	int rc = -1;

	if ((nfs_param.core_param.enable_MEMPOOL == true) &&
		(size >= MEM_UNIT_SIZE_MIN) && (size <= MEM_UNIT_SIZE) &&
		(is_in_mempool(p)))
	{
		rc = mem_unit_free(p);
		assert(rc == 0);
	}

	if(rc == -1)
	{
		mem_free(p, size);
		if(nfs_param.core_param.enable_MEMSTAT == true){
			pthread_mutex_lock(&stat_mem.stat_gsh_lock);
			stat_mem.alloc_gsh_num--;
			stat_mem.alloc_gsh_size -= size;
			pthread_mutex_unlock(&stat_mem.stat_gsh_lock);
		}
	}
	return;
}

void *
gsh_pool_alloc__(size_t size, const char *file, int line,
	     	const char *function)
{
	void *r = NULL;

	if ((nfs_param.core_param.enable_MEMPOOL == true) &&
		(size >= MEM_UNIT_SIZE_MIN) && (size <= MEM_UNIT_SIZE) &&
		(!is_mempool_empty()))
	{
		r = mem_unit_alloc();
	}

	if (r == NULL){
		r = mem_alloc(size);
		if(nfs_param.core_param.enable_MEMSTAT == true){
			pthread_mutex_lock(&stat_mem.stat_gsh_lock);
			stat_mem.alloc_gsh_num++;
			stat_mem.alloc_gsh_size += size;
			pthread_mutex_unlock(&stat_mem.stat_gsh_lock);
		}
	}

	assert(r != NULL);
	return r;
}
