/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright CEA/DAM/DIF  (2008)
 * contributeur : <PERSON>   <EMAIL>
 *                Thomas LEIBOVICI  <EMAIL>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * ---------------------------------------
 */

/**
 * @file  nfs_init.c
 * @brief Most of the init routines
 */
#include "config.h"
#include "nfs_init.h"
#include "log.h"
#include "fsal.h"
#include "rquota.h"
#include "nfs_core.h"
#include "nfs_file_handle.h"
#include "nfs_exports.h"
#include "nfs_ip_stats.h"
#include "nfs_proto_functions.h"
#include "nfs_dupreq.h"
#include "config_parsing.h"
#include "nfs4_acls.h"
#include "nfs_rpc_callback.h"
#include "server_stats.h"
#ifdef USE_DBUS
#include "gsh_dbus.h"
#endif
#include "FSAL/fsal_commonlib.h"
#ifdef _USE_CB_SIMULATOR
#include "nfs_rpc_callback_simulator.h"
#endif
#include <sys/time.h>
#include <sys/resource.h>
#include <unistd.h>
#include <string.h>
#include <signal.h>
#include <math.h>
#ifdef _USE_NLM
#include "nlm_util.h"
#endif /* _USE_NLM */
#include "nsm.h"
#include "sal_functions.h"
#include "fridgethr.h"
#include "idmapper.h"
#include "delayed_exec.h"
#include "client_mgr.h"
#include "tenant_mgr.h"
#include "export_mgr.h"
#ifdef USE_CAPS
#include <sys/capability.h>	/* For capget/capset */
#endif
#include "uid2grp.h"
#include "netgroup_cache.h"
#include "pnfs_utils.h"
#include "mdcache.h"
#include "common_utils.h"
#include "nfs_init.h"
#include <urcu-bp.h>
#include "conf_url.h"
#include "FSAL/fsal_localfs.h"
#include "mem_pool.h"
#include "server_stats_private.h"
#include <python3.6m/Python.h>
#include <python3.6m/pylifecycle.h>

#include "database.h"
#include "etcdwatcher_c_wrapper.h"
#include "namespace_mgr.h"
#include <audit/idfsAuditLog.h>

/**
 * @brief init_complete used to indicate if gnfs is during
 * startup or not
 */
struct nfs_init nfs_init;

/* global information exported to all layers (as extern vars) */
nfs_parameter_t nfs_param;
rdma_parameter_t rdma_param;


struct _nfs_health nfs_health_;

static struct _nfs_health healthstats;

struct local_addr_list local_addrs;

/* ServerEpoch is ServerBootTime unless overriden by -E command line option */
struct timespec nfs_ServerBootTime;
time_t nfs_ServerEpoch;

struct gnfsio_stats gnfs_io;

verifier4 NFS4_write_verifier;	/* NFS V4 write verifier */
writeverf3 NFS3_write_verifier;	/* NFS V3 write verifier */
struct client_by_ip client_by_ip;
struct export_by_id export_by_id;
extern struct glist_head exportlist;
struct tenant_by_name tenant_by_name;

/* node ID used to identify an individual node in a cluster */
int g_nodeid;

nfs_start_info_t nfs_start_info;

pthread_t admin_thrid;
pthread_t sigmgr_thrid;

/*add for check_nfs_status*/
pthread_t tid1,tid2;
sem_t sem;

/*add for gnfs_io_stats, bd ,iops*/
pthread_t io_tid1,io_tid2;
sem_t io_sem;

pthread_t report_tid;

uint64_t gnfs_last_handled_count = 0;
uint64_t gnfs_last_request_count = 0;
struct gnfs_io_stats v3_io_old;
struct gnfs_io_stats v4_io_old;
double get_max_latency(struct proto_op v3_new, struct proto_op v4_new){
	
	double max_tmp = 0.0;
	if(v3_new.latency.max > v4_new.latency.max){
		max_tmp = v3_new.latency.max * 0.000001;
		max_tmp = (int)(max_tmp * 100) /100.0;
	}else{
		max_tmp = v4_new.latency.max * 0.000001;
		max_tmp = (int)(max_tmp * 100) /100.0;
	}
	return max_tmp;
}
double get_avg_latency(struct proto_op v3_old, struct proto_op v4_old,
	struct proto_op v3_new, struct proto_op v4_new){

	uint64_t v3_total_tmp = 0.0;
	double v3_latency_tmp = 0.0;
	uint64_t v4_total_tmp = 0.0;
	double v4_latency_tmp = 0.0;
	double avg_tmp = 0.0;
		
	v3_total_tmp = v3_new.total - v3_old.total;
	if(v3_total_tmp >= 0){
		v3_latency_tmp = v3_new.latency.latency - v3_old.latency.latency;
	}else{
		/*Prove that the delay information has been cleaned.*/
		v3_total_tmp = v3_new.total;
		v3_latency_tmp = v3_new.latency.latency;
	}
	
	v4_total_tmp = v4_new.total - v4_old.total;
	if(v4_total_tmp >= 0){
		v4_latency_tmp = v4_new.latency.latency - v4_old.latency.latency;
	}else{
		/*Prove that the delay information has been cleaned.*/
		v4_total_tmp = v4_new.total;
		v4_latency_tmp = v4_new.latency.latency;
	}
	/*Calculate the average delay for the current statistical period.*/
	if ((v3_total_tmp != 0) || (v4_total_tmp != 0)){
		avg_tmp = (double) ((v3_latency_tmp + v4_latency_tmp) / 
			(v3_total_tmp + v4_total_tmp)) * 0.000001;
		avg_tmp = (int)(avg_tmp * 100) /100.0;
	}
	LogDebug(COMPONENT_REPORT_ISM, "v3_new.total:%lu, v3_old.total:%lu,v4_new.total:%lu,v4_old.total:%lu,v3_total_tmp:%lu, v4_total_tmp:%lu, avg_tmp:%lf, ", 
			v3_new.total, v3_old.total, v4_new.total, v4_old.total, v3_total_tmp, v4_total_tmp, avg_tmp);
	return avg_tmp;
}

double get_io_block_percentage(uint64_t io_new, uint64_t io_old,
	uint64_t io_read_total){
	
	double max_tmp = 0.0;
	if((io_read_total > 0) && ((io_new - io_old)>0))
		max_tmp = (int)(((double)(io_new - io_old)/io_read_total) * 100);

	return max_tmp;
}

/* function declaration */
PyObject* create_list_of_dicts(uint64_t time_tmp);  
PyObject* create_dict(uint64_t time_tmp);

//int add_key_double_to_dict(PyObject* pyDict, const char* key, double value);
//int add_key_char_to_dict(PyObject* pyDict, const char* key, char* value);

int add_key_long_to_dict(PyObject* pyDict, const char* key, double value){

	/* creat key value */
	PyObject *pykey = PyUnicode_FromString(key);
	PyObject *pyvalue = PyFloat_FromDouble(value);
	if (!pykey || !pyvalue) {  
		Py_XDECREF(pykey);
		Py_XDECREF(pyvalue);
		LogEvent(COMPONENT_REPORT_ISM, "add_key_long_to_dict is error, pykey or pyvalue is null.");
		/* error */
		return -1;
	} 
	/* add key-value to dict*/
	if (PyDict_SetItem(pyDict, pykey, pyvalue) != 0 ){
		PyErr_Print();
		Py_DECREF(pykey);
		Py_DECREF(pyvalue);
		LogEvent(COMPONENT_REPORT_ISM, "PyDict_SetItem is error");
		/* error*/
		return -1;
	}

	/* release key-value */
	Py_DECREF(pykey);
	Py_DECREF(pyvalue);
	return 0;

}
int add_key_char_to_dict(PyObject* pyDict, const char* key, char* value){

	/* creat key value */
	PyObject *pykey = PyUnicode_FromString(key);
	PyObject *pyvalue = PyUnicode_FromString(value);
	if (!pykey || !pyvalue) {  
		Py_XDECREF(pykey);
		Py_XDECREF(pyvalue);
		LogEvent(COMPONENT_REPORT_ISM, "add_key_char_to_dict is error, pykey or pyvalue is null.");
		/* error */
		return -1;
	} 
	/* add key-value to dict*/
	if (PyDict_SetItem(pyDict, pykey, pyvalue) != 0 ){
		PyErr_Print();
		Py_DECREF(pykey);
		Py_DECREF(pyvalue);
		LogEvent(COMPONENT_REPORT_ISM, "PyDict_SetItem is error");
		/* error*/
		return -1;
	}
	
	/* release key-value */
	Py_DECREF(pykey);
	Py_DECREF(pyvalue);
	return 0;

}

/* implementation function */
PyObject* create_node_dict(uint64_t time_tmp) {
	/* create dict */
	PyObject *pyDict = PyDict_New();
	if (pyDict == NULL) {
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create dict is error, pyDict=null");
		/* error */
		return NULL;
	}

	double read_max_latency = 0.0;
	double write_max_latency = 0.0;
	double read_latency = 0.0;
	double write_latency = 0.0;

	int status = 0;
	struct gnfs_io_stats v3_io_tem = gnfs_io.v3_io;
	struct gnfs_io_stats v4_io_tem = gnfs_io.v4_io;
	uint64_t read_ops = v3_io_tem.gnfs_read_ops + v4_io_tem.gnfs_read_ops;
	uint64_t read_bd = v3_io_tem.gnfs_read_bd + v4_io_tem.gnfs_read_bd;
	
	uint64_t write_ops = v3_io_tem.gnfs_write_ops + v4_io_tem.gnfs_write_ops;
	uint64_t write_bd = v3_io_tem.gnfs_write_bd + v4_io_tem.gnfs_write_bd;
	
	uint64_t total_ops = read_ops + write_ops;
	uint64_t total_bd = read_bd + write_bd;

	char hostname[MAXNAMLEN+1]; /* max hostname length */
	if (gsh_gethostname(hostname, sizeof(hostname), 
		nfs_param.core_param.enable_AUTHSTATS) == -1) {
		strcpy(hostname, "unkown hostname");

	}
	/*Retrieve the Historical maximum delay. Not Update.*/
	read_max_latency = get_max_latency(v3_io_tem.read.cmd, v4_io_tem.read.cmd);
	write_max_latency = get_max_latency(v3_io_tem.write.cmd, v4_io_tem.write.cmd);
	/*Retrieve the average delay for the Reporting Period. Periodic Update.*/
	read_latency = get_avg_latency(v3_io_old.read.cmd, v4_io_old.read.cmd, 
		v3_io_tem.read.cmd, v4_io_tem.read.cmd);

	write_latency = get_avg_latency(v3_io_old.write.cmd, v4_io_old.write.cmd, 
		v3_io_tem.write.cmd, v4_io_tem.write.cmd);
	/*Record the historical information of the last cycle*/
	v3_io_old = v3_io_tem;
	v4_io_old = v4_io_tem;
	/* add key-value to dict */
	status = add_key_char_to_dict(pyDict, "table", "pm_node_nfs");
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:table", status);
	}

	status = add_key_long_to_dict(pyDict, "time", time_tmp);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:time", status);
	}
	status = add_key_char_to_dict(pyDict, "node", hostname);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:node", status);
	}
	
	status = add_key_long_to_dict(pyDict, "band", total_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:band", status);
	}
	status = add_key_long_to_dict(pyDict, "r_band", read_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_band", status);
	}
	status = add_key_long_to_dict(pyDict, "w_band", write_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_band", status);
	}

	status = add_key_long_to_dict(pyDict, "iops", total_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:iops", status);
	}
	status = add_key_long_to_dict(pyDict, "r_iops", read_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_iops", status);
	}
	status = add_key_long_to_dict(pyDict, "w_iops", write_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_iops", status);
	}

	status = add_key_long_to_dict(pyDict, "r_avg_delay", read_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_avg_delay", status);
	}
	status = add_key_long_to_dict(pyDict, "r_max_delay", read_max_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_max_delay", status);
	}
	status = add_key_long_to_dict(pyDict, "w_avg_delay", write_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_avg_delay", status);
	}
	status = add_key_long_to_dict(pyDict, "w_max_delay", write_max_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_max_delay", status);
	}
	LogDebug(COMPONENT_REPORT_ISM, "table:pm_node_nfs, time:%lu, node:%s, band:%lu,r_band:%lu,w_band:%lu, iops:%lu,r_iops:%lu,w_iops:%lu, r_avg_delay:%lf,w_avg_delay:%lf", 
		time_tmp, hostname, total_bd, read_bd, write_bd, total_ops, read_ops, write_ops, read_latency, write_latency);
	/* return the dict*/
	return pyDict;
}

PyObject* create_client_dict(struct gsh_client *client, uint64_t time_tmp) {
	struct server_stats *svr = NULL;
	struct gsh_clnt_allops_stats *c_all = NULL;
	svr = container_of(client, struct server_stats, client);
	c_all = &svr->c_all;

	/* create dict */
	PyObject *pyDict = PyDict_New();
	if (pyDict == NULL) {
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create dict is error, pyDict=null");
		/* error */
		return NULL;
	}
	char *ipaddr = alloca(SOCK_NAME_MAX);
	if (!sprint_sockip(&client->cl_addrbuf, ipaddr, SOCK_NAME_MAX))
		(void) strlcpy(ipaddr, "<unknown>", SOCK_NAME_MAX);
	if ((client->cl_addrbuf.ss_family == AF_INET6) && !strncmp(ipaddr, "::ffff:", 7))
		memmove(ipaddr, ipaddr+7, SOCK_NAME_MAX-7);
	char hostname[MAXNAMLEN+1]; /* max hostname length */
	if (gsh_gethostname(hostname, sizeof(hostname), 
		nfs_param.core_param.enable_AUTHSTATS) == -1) {
		strcpy(hostname, "unkown hostname");

	}

	double read_latency = 0.0;
	double write_latency = 0.0;
	double lookup_latency = 0.0;
	double getattr_latency = 0.0;
	double setattr_latency = 0.0;
	double create_latency = 0.0;
	double readdirplus_latency = 0.0;

	struct v3_full v3op_tem = c_all->v3op;
	struct v4_full v4op_tem = c_all->v4op;

	struct v3_full v3op_old = c_all->v3op_old;
	struct v4_full v4op_old = c_all->v4op_old;

	int status = 0;
	struct gnfs_io_stats v3_io_tem = svr->client_io.v3_io;
	struct gnfs_io_stats v4_io_tem = svr->client_io.v4_io;
	uint64_t read_ops = v3_io_tem.gnfs_read_ops + v4_io_tem.gnfs_read_ops;
	uint64_t read_bd = v3_io_tem.gnfs_read_bd + v4_io_tem.gnfs_read_bd;
	
	uint64_t write_ops = v3_io_tem.gnfs_write_ops + v4_io_tem.gnfs_write_ops;
	uint64_t write_bd = v3_io_tem.gnfs_write_bd + v4_io_tem.gnfs_write_bd;
	
	uint64_t total_ops = read_ops + write_ops;
	uint64_t total_bd = read_bd + write_bd;

	/*Retrieve the average delay for the Reporting Period. Periodic Update.*/
	read_latency = get_avg_latency(v3op_old.op[NFSPROC3_READ].cmd, v4op_old.op[NFS4_OP_READ].cmd, 
		v3op_tem.op[NFSPROC3_READ].cmd, v4op_tem.op[NFS4_OP_READ].cmd);

	write_latency = get_avg_latency(v3op_old.op[NFSPROC3_WRITE].cmd, v4op_old.op[NFS4_OP_WRITE].cmd, 
		v3op_tem.op[NFSPROC3_WRITE].cmd, v4op_tem.op[NFS4_OP_WRITE].cmd);
	
	lookup_latency = get_avg_latency(v3op_old.op[NFSPROC_LOOKUP].cmd, v4op_old.op[NFS4_OP_LOOKUP].cmd, 
		v3op_tem.op[NFSPROC_LOOKUP].cmd, v4op_tem.op[NFS4_OP_LOOKUP].cmd);
	
	getattr_latency = get_avg_latency(v3op_old.op[NFSPROC3_GETATTR].cmd, v4op_old.op[NFS4_OP_GETATTR].cmd, 
		v3op_tem.op[NFSPROC3_GETATTR].cmd, v4op_tem.op[NFS4_OP_GETATTR].cmd);
	
	setattr_latency = get_avg_latency(v3op_old.op[NFSPROC3_SETATTR].cmd, v4op_old.op[NFS4_OP_SETATTR].cmd, 
		v3op_tem.op[NFSPROC3_SETATTR].cmd, v4op_tem.op[NFS4_OP_SETATTR].cmd);
	
	create_latency = get_avg_latency(v3op_old.op[NFSPROC3_CREATE].cmd, v4op_old.op[NFS4_OP_CREATE].cmd, 
		v3op_tem.op[NFSPROC3_CREATE].cmd, v4op_tem.op[NFS4_OP_CREATE].cmd);
	
	readdirplus_latency = get_avg_latency(v3op_old.op[NFSPROC3_READDIRPLUS].cmd, v4op_old.op[NFS4_OP_READDIR].cmd, 
		v3op_tem.op[NFSPROC3_READDIRPLUS].cmd, v4op_tem.op[NFS4_OP_READDIR].cmd);

	/*Record the historical information of the last cycle*/
	c_all->v3op_old = c_all->v3op;
	c_all->v4op_old = c_all->v4op;
	/* add key-value to dict */
	status = add_key_char_to_dict(pyDict, "table", "pm_node_nfs_client");
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:table", status);
	}

	status = add_key_long_to_dict(pyDict, "time", time_tmp);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:time", status);
	}
	status = add_key_char_to_dict(pyDict, "node", hostname);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:node", status);
	}
	status = add_key_char_to_dict(pyDict, "device_ip", ipaddr);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:device_ip", status);
	}	
	status = add_key_long_to_dict(pyDict, "band", total_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:band", status);
	}
	status = add_key_long_to_dict(pyDict, "r_band", read_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_band", status);
	}
	status = add_key_long_to_dict(pyDict, "w_band", write_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_band", status);
	}

	status = add_key_long_to_dict(pyDict, "iops", total_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:iops", status);
	}
	status = add_key_long_to_dict(pyDict, "r_iops", read_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_iops", status);
	}
	status = add_key_long_to_dict(pyDict, "w_iops", write_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_iops", status);
	}

	status = add_key_long_to_dict(pyDict, "ll_read", read_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_read", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_write", write_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_write", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_lookup", lookup_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_lookup", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_getattr", getattr_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_getattr", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_setattr", setattr_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_setattr", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_create", create_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_create", status);
	}
	status = add_key_long_to_dict(pyDict, "readdirplus_r", readdirplus_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:readdirplus_r", status);
	}

	LogDebug(COMPONENT_REPORT_ISM, "table:pm_node_nfs_client, time:%lu, node:%s, clientip:%s, band:%lu,r_band:%lu,w_band:%lu, iops:%lu,r_iops:%lu,w_iops:%lu",
		time_tmp, hostname, ipaddr, total_bd, read_bd, write_bd, total_ops, read_ops, write_ops);
	LogDebug(COMPONENT_REPORT_ISM, "r_avg_delay:%lf,w_avg_delay:%lf,l_avg_delay:%lf,g_avg_delay:%lf,s_avg_delay:%lf,c_avg_delay:%lf,rdir_avg_delay:%lf ",
		read_latency, write_latency, lookup_latency, getattr_latency, setattr_latency, create_latency, readdirplus_latency);
	/* return the dict*/
	return pyDict;
}
PyObject* create_export_dict(struct gsh_export *exp_node, uint64_t time_tmp) {
	struct export_stats *exp = NULL;
	char *path = NULL;
	struct tmp_export_paths tmp = {NULL, NULL};

	tmp_get_exp_paths(&tmp, exp_node);
	path = tmp_export_path(&tmp);
	if(path == NULL || strcmp(path, "/") == 0){
		tmp_put_exp_paths(&tmp);
		LogEvent(COMPONENT_REPORT_ISM, "create dict is error, path == NULL || strcmp(path, /) == 0");
		return NULL;
	}
	tmp_put_exp_paths(&tmp);

	exp = container_of(exp_node, struct export_stats, export);
	
	char hostname[MAXNAMLEN+1]; /* max hostname length */
	if (gsh_gethostname(hostname, sizeof(hostname), 
		nfs_param.core_param.enable_AUTHSTATS) == -1) {
		strcpy(hostname, "unkown hostname");
	}
	/* create dict */
	PyObject *pyDict = PyDict_New();
	if (pyDict == NULL) {
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create dict is error, pyDict=null");
		/* error */
		return NULL;
	}

	double read_latency = 0.0;
	double write_latency = 0.0;
	double lookup_latency = 0.0;
	double getattr_latency = 0.0;
	double setattr_latency = 0.0;
	double create_latency = 0.0;
	double readdirplus_latency = 0.0;

	struct v3_full v3op_tem = exp->v3op;
	struct v4_full v4op_tem = exp->v4op;

	struct v3_full v3op_old = exp->v3op_old;
	struct v4_full v4op_old = exp->v4op_old;

	struct qps_stats v3_qps = exp->v3_qps;
	struct qps_stats v4_qps = exp->v4_qps;

	io_stat  write_io_stat = exp->write_io_stat;
	io_stat  read_io_stat = exp->read_io_stat;
	io_stat  write_io_stat_old = exp->write_io_stat_old;
	io_stat  read_io_stat_old = exp->read_io_stat_old;

	int status = 0;
	struct gnfs_io_stats v3_io_tem = exp->exp_io.v3_io;
	struct gnfs_io_stats v4_io_tem = exp->exp_io.v4_io;
	uint64_t read_ops = v3_io_tem.gnfs_read_ops + v4_io_tem.gnfs_read_ops;
	uint64_t read_bd = v3_io_tem.gnfs_read_bd + v4_io_tem.gnfs_read_bd;

	uint64_t write_ops = v3_io_tem.gnfs_write_ops + v4_io_tem.gnfs_write_ops;
	uint64_t write_bd = v3_io_tem.gnfs_write_bd + v4_io_tem.gnfs_write_bd;

	uint64_t total_ops = read_ops + write_ops;
	uint64_t total_bd = read_bd + write_bd;

	uint64_t qps_lookup = v3_qps.gnfs_lookup_ops + v4_qps.gnfs_lookup_ops;
	uint64_t qps_create = v3_qps.gnfs_create_ops + v4_qps.gnfs_create_ops;
	uint64_t qps_getattr = v3_qps.gnfs_getattr_ops + v4_qps.gnfs_getattr_ops;
	uint64_t qps_readdirplus = v3_qps.gnfs_readdirplus_ops + v4_qps.gnfs_readdirplus_ops;
	uint64_t qps_setattr = v3_qps.gnfs_setattr_ops + v4_qps.gnfs_setattr_ops;

	/*Retrieve the average delay for the Reporting Period. Periodic Update.*/
	read_latency = get_avg_latency(v3op_old.op[NFSPROC3_READ].cmd, v4op_old.op[NFS4_OP_READ].cmd, 
		v3op_tem.op[NFSPROC3_READ].cmd, v4op_tem.op[NFS4_OP_READ].cmd);

	write_latency = get_avg_latency(v3op_old.op[NFSPROC3_WRITE].cmd, v4op_old.op[NFS4_OP_WRITE].cmd, 
		v3op_tem.op[NFSPROC3_WRITE].cmd, v4op_tem.op[NFS4_OP_WRITE].cmd);
	
	lookup_latency = get_avg_latency(v3op_old.op[NFSPROC_LOOKUP].cmd, v4op_old.op[NFS4_OP_LOOKUP].cmd, 
		v3op_tem.op[NFSPROC_LOOKUP].cmd, v4op_tem.op[NFS4_OP_LOOKUP].cmd);
	
	getattr_latency = get_avg_latency(v3op_old.op[NFSPROC3_GETATTR].cmd, v4op_old.op[NFS4_OP_GETATTR].cmd, 
		v3op_tem.op[NFSPROC3_GETATTR].cmd, v4op_tem.op[NFS4_OP_GETATTR].cmd);
	
	setattr_latency = get_avg_latency(v3op_old.op[NFSPROC3_GETATTR].cmd, v4op_old.op[NFS4_OP_SETATTR].cmd, 
		v3op_tem.op[NFSPROC3_GETATTR].cmd, v4op_tem.op[NFS4_OP_SETATTR].cmd);
	
	create_latency = get_avg_latency(v3op_old.op[NFSPROC3_CREATE].cmd, v4op_old.op[NFS4_OP_CREATE].cmd, 
		v3op_tem.op[NFSPROC3_CREATE].cmd, v4op_tem.op[NFS4_OP_CREATE].cmd);
	
	readdirplus_latency = get_avg_latency(v3op_old.op[NFSPROC3_READDIRPLUS].cmd, v4op_old.op[NFS4_OP_READDIR].cmd, 
		v3op_tem.op[NFSPROC3_READDIRPLUS].cmd, v4op_tem.op[NFS4_OP_READDIR].cmd);

	/*Record the historical information of the last cycle*/
	exp->v3op_old = exp->v3op;
	exp->v4op_old = exp->v4op;
	/* add key-value to dict */
	/* Basic Information */
	status = add_key_char_to_dict(pyDict, "table", "pm_node_nfs_share");
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:table", status);
	}

	status = add_key_long_to_dict(pyDict, "time", time_tmp);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:time", status);
	}
	status = add_key_char_to_dict(pyDict, "node", hostname);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:node", status);
	}
	status = add_key_char_to_dict(pyDict, "tenant", exp_node->tenant);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:tenant", status);
	}	
	status = add_key_char_to_dict(pyDict, "export_path", path);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:exp_path", status);
	}	
	/* Bandwidth */
	status = add_key_long_to_dict(pyDict, "band", total_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:band", status);
	}
	status = add_key_long_to_dict(pyDict, "r_band", read_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_band", status);
	}
	status = add_key_long_to_dict(pyDict, "w_band", write_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_band", status);
	}
	/* iops */
	status = add_key_long_to_dict(pyDict, "iops", total_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:iops", status);
	}
	status = add_key_long_to_dict(pyDict, "r_iops", read_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_iops", status);
	}
	status = add_key_long_to_dict(pyDict, "w_iops", write_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_iops", status);
	}
	LogDebug(COMPONENT_REPORT_ISM, "table:pm_node_nfs_share, time:%lu, node:%s, path:%s, band:%lu,r_band:%lu,w_band:%lu, iops:%lu,r_iops:%lu,w_iops:%lu",
		time_tmp, hostname, path, total_bd, read_bd, write_bd, total_ops, read_ops, write_ops);
	/* Read/Write IO Latency and Metadata Latency */
	status = add_key_long_to_dict(pyDict, "ll_read", read_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_read", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_write", write_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_write", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_lookup", lookup_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_lookup", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_getattr", getattr_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_getattr", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_setattr", setattr_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_setattr", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_create", create_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_create", status);
	}
	status = add_key_long_to_dict(pyDict, "readdirplus_r", readdirplus_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:readdirplus_r", status);
	}
	LogDebug(COMPONENT_REPORT_ISM, "r_avg_delay:%lf,w_avg_delay:%lf,l_avg_delay:%lf,g_avg_delay:%lf,s_avg_delay:%lf,c_avg_delay:%lf,rdir_avg_delay:%lf ", 
		read_latency, write_latency, lookup_latency, getattr_latency, setattr_latency, create_latency, readdirplus_latency);	
	/* qps,  Queries Per Second */
	status = add_key_long_to_dict(pyDict, "qps_lookup", qps_lookup);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:qps_lookup", status);
	}
	status = add_key_long_to_dict(pyDict, "qps_getattr", qps_getattr);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:qps_getattr", status);
	}
	status = add_key_long_to_dict(pyDict, "qps_setattr", qps_setattr);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:qps_setattr", status);
	}
	status = add_key_long_to_dict(pyDict, "qps_create", qps_create);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:qps_create", status);
	}
	status = add_key_long_to_dict(pyDict, "qps_readdirplus", qps_readdirplus);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:qps_readdirplus", status);
	}
	LogDebug(COMPONENT_REPORT_ISM, "qps_lookup:%lu,qps_getattr:%lu,qps_setattr:%lu,qps_create:%lu,qps_readdirplus:%lu.", 
		qps_lookup, qps_getattr, qps_setattr, qps_create, qps_readdirplus);
	/* io_block,   */

	uint64_t io_read_total = 0;
	if(read_io_stat.io_total > read_io_stat_old.io_total)
		io_read_total = read_io_stat.io_total - read_io_stat_old.io_total;
	status = add_key_long_to_dict(pyDict, "io_read_total", io_read_total);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_read_total", status);
	}
	double io_read_4k = get_io_block_percentage(read_io_stat.io[IO_STATA_RANGE_0_4K], read_io_stat_old.io[IO_STATA_RANGE_0_4K],
					io_read_total);
	status = add_key_long_to_dict(pyDict, "io_read_4k", io_read_4k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_read_4k", status);
	}
	double io_read_8k = get_io_block_percentage(read_io_stat.io[IO_STATA_RANGE_4K_8K], read_io_stat_old.io[IO_STATA_RANGE_4K_8K],
					io_read_total);
	status = add_key_long_to_dict(pyDict, "io_read_8k", io_read_8k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_read_8k", status);
	}
	double io_read_16k = get_io_block_percentage(read_io_stat.io[IO_STATA_RANGE_8K_16K], read_io_stat_old.io[IO_STATA_RANGE_8K_16K],
					io_read_total);
	status = add_key_long_to_dict(pyDict, "io_read_16k", io_read_16k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_read_16k", status);
	}
	double io_read_32k = get_io_block_percentage(read_io_stat.io[IO_STATA_RANGE_16K_32K], read_io_stat_old.io[IO_STATA_RANGE_16K_32K],
					io_read_total);
	status = add_key_long_to_dict(pyDict, "io_read_32k", io_read_32k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_read_32k", status);
	}
	double io_read_64k = get_io_block_percentage(read_io_stat.io[IO_STATA_RANGE_32K_64K], read_io_stat_old.io[IO_STATA_RANGE_32K_64K],
					io_read_total);
	status = add_key_long_to_dict(pyDict, "io_read_64k", io_read_64k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_read_64k", status);
	}
	double io_read_128k = get_io_block_percentage(read_io_stat.io[IO_STATA_RANGE_64K_128K], read_io_stat_old.io[IO_STATA_RANGE_64K_128K],
					io_read_total);
	status = add_key_long_to_dict(pyDict, "io_read_128k", io_read_128k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_read_128k", status);
	}
	double io_read_max = get_io_block_percentage(read_io_stat.io[IO_STATA_RANGE_128K_MAX], read_io_stat_old.io[IO_STATA_RANGE_128K_MAX],
					io_read_total);
	status = add_key_long_to_dict(pyDict, "io_read_max", io_read_max);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_read_max", status);
	}

	uint64_t io_write_total = 0;
	if(write_io_stat.io_total > write_io_stat_old.io_total)
		io_write_total = write_io_stat.io_total - write_io_stat_old.io_total;

	status = add_key_long_to_dict(pyDict, "io_write_total", io_write_total);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_write_total", status);
	}
	double io_write_4k = get_io_block_percentage(write_io_stat.io[IO_STATA_RANGE_0_4K], write_io_stat_old.io[IO_STATA_RANGE_0_4K],
					io_write_total);
	status = add_key_long_to_dict(pyDict, "io_write_4k", io_write_4k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_write_4k", status);
	}
	double io_write_8k = get_io_block_percentage(write_io_stat.io[IO_STATA_RANGE_4K_8K], write_io_stat_old.io[IO_STATA_RANGE_4K_8K],
					io_write_total);
	status = add_key_long_to_dict(pyDict, "io_write_8k", io_write_8k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_write_8k", status);
	}
	double io_write_16k = get_io_block_percentage(write_io_stat.io[IO_STATA_RANGE_8K_16K], write_io_stat_old.io[IO_STATA_RANGE_8K_16K],
					io_write_total);
	status = add_key_long_to_dict(pyDict, "io_write_16k", io_write_16k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_write_16k", status);
	}
	double io_write_32k = get_io_block_percentage(write_io_stat.io[IO_STATA_RANGE_16K_32K], write_io_stat_old.io[IO_STATA_RANGE_16K_32K],
					io_write_total);
	status = add_key_long_to_dict(pyDict, "io_write_32k", io_write_32k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_write_32k", status);
	}
	double io_write_64k = get_io_block_percentage(write_io_stat.io[IO_STATA_RANGE_32K_64K], write_io_stat_old.io[IO_STATA_RANGE_32K_64K],
					io_write_total);

	status = add_key_long_to_dict(pyDict, "io_write_64k", io_write_64k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_write_64k", status);
	}
	double io_write_128k = get_io_block_percentage(write_io_stat.io[IO_STATA_RANGE_64K_128K], write_io_stat_old.io[IO_STATA_RANGE_64K_128K],
					io_write_total);
	status = add_key_long_to_dict(pyDict, "io_write_128k", io_write_128k);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_write_128k", status);
	}
	double io_write_max = get_io_block_percentage(write_io_stat.io[IO_STATA_RANGE_128K_MAX], write_io_stat_old.io[IO_STATA_RANGE_128K_MAX],
					io_write_total);
	status = add_key_long_to_dict(pyDict, "io_write_max", io_write_max);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:io_write_max", status);
	}
	exp->write_io_stat_old = exp->write_io_stat;
	exp->read_io_stat_old = exp->read_io_stat;
	LogDebug(COMPONENT_REPORT_ISM, "io_read_total:%lu, io_write_total:%lu.", read_io_stat.io_total, write_io_stat.io_total);

	/* return the dict*/
	return pyDict;
}

PyObject* create_tenant_dict(struct gsh_tenant *tenant, uint64_t time_tmp) {
	/* create dict */
	PyObject *pyDict = PyDict_New();
	if (pyDict == NULL) {
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create dict is error, pyDict=null");
		/* error */
		return NULL;
	}

	size_t len = strlen(tenant->tenant_name) + 1;
	char *tenant_name = NULL;
	tenant_name = alloca(len);
	if(tenant_name == NULL){
		Py_DECREF(pyDict);
		LogEvent(COMPONENT_REPORT_ISM, "tenant_name malloc error, tenant name:%s ", tenant->tenant_name);
		return NULL;
	}
	memcpy(tenant_name, tenant->tenant_name, len);

	char hostname[MAXNAMLEN+1]; /* max hostname length */
	if (gsh_gethostname(hostname, sizeof(hostname),
		nfs_param.core_param.enable_AUTHSTATS) == -1) {
		strcpy(hostname, "unkown hostname");

	}
	double read_max_latency = 0.0;
	double write_max_latency = 0.0;
	double read_latency = 0.0;
	double write_latency = 0.0;

	struct v3_full v3op_tem = tenant->v3op;
	struct v4_full v4op_tem = tenant->v4op;

	struct v3_full v3op_old = tenant->v3op_old;
	struct v4_full v4op_old = tenant->v4op_old;

	int status = 0;
	struct gnfs_io_stats v3_io_tem = tenant->tn_iops.v3_io;
	struct gnfs_io_stats v4_io_tem = tenant->tn_iops.v4_io;
	uint64_t read_ops = v3_io_tem.gnfs_read_ops + v4_io_tem.gnfs_read_ops;
	uint64_t read_bd = v3_io_tem.gnfs_read_bd + v4_io_tem.gnfs_read_bd;

	uint64_t write_ops = v3_io_tem.gnfs_write_ops + v4_io_tem.gnfs_write_ops;
	uint64_t write_bd = v3_io_tem.gnfs_write_bd + v4_io_tem.gnfs_write_bd;

	uint64_t total_ops = read_ops + write_ops;
	uint64_t total_bd = read_bd + write_bd;
	/*Retrieve the Historical maximum delay. Not Update.*/
	read_max_latency = get_max_latency(v3_io_tem.read.cmd, v4_io_tem.read.cmd);
	write_max_latency = get_max_latency(v3_io_tem.write.cmd, v4_io_tem.write.cmd);

	/*Retrieve the average delay for the Reporting Period. Periodic Update.*/
	read_latency = get_avg_latency(v3op_old.op[NFSPROC3_READ].cmd, v4op_old.op[NFS4_OP_READ].cmd,
		v3op_tem.op[NFSPROC3_READ].cmd, v4op_tem.op[NFS4_OP_READ].cmd);

	write_latency = get_avg_latency(v3op_old.op[NFSPROC3_WRITE].cmd, v4op_old.op[NFS4_OP_WRITE].cmd,
		v3op_tem.op[NFSPROC3_WRITE].cmd, v4op_tem.op[NFS4_OP_WRITE].cmd);

	/*Record the historical information of the last cycle*/
	tenant->v3op_old = tenant->v3op;
	tenant->v4op_old = tenant->v4op;
	/* add key-value to dict */
	status = add_key_char_to_dict(pyDict, "table", "pm_node_tenant_nfs");
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:table", status);
	}

	status = add_key_long_to_dict(pyDict, "time", time_tmp);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:time", status);
	}
	status = add_key_char_to_dict(pyDict, "node", hostname);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:node", status);
	}
	status = add_key_char_to_dict(pyDict, "tenant", tenant_name);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:device_ip", status);
	}
	status = add_key_long_to_dict(pyDict, "band", total_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:band", status);
	}
	status = add_key_long_to_dict(pyDict, "r_band", read_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_band", status);
	}
	status = add_key_long_to_dict(pyDict, "w_band", write_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_band", status);
	}

	status = add_key_long_to_dict(pyDict, "iops", total_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:iops", status);
	}
	status = add_key_long_to_dict(pyDict, "r_iops", read_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_iops", status);
	}
	status = add_key_long_to_dict(pyDict, "w_iops", write_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_iops", status);
	}

	status = add_key_long_to_dict(pyDict, "r_avg_delay", read_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_read", status);
	}
	status = add_key_long_to_dict(pyDict, "w_avg_delay", write_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_write", status);
	}
	status = add_key_long_to_dict(pyDict, "r_max_delay", read_max_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_max_delay", status);
	}
	status = add_key_long_to_dict(pyDict, "w_max_delay", write_max_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_max_delay", status);
	}
	LogDebug(COMPONENT_REPORT_ISM, "time:%lu, node:%s, tenant_name:%s, band:%lu,r_band:%lu,w_band:%lu, iops:%lu, r_iops:%lu, w_iops:%lu, r_avg_delay:%lf, w_avg_delay:%lf",
		time_tmp, hostname, tenant_name, total_bd, read_bd, write_bd, total_ops, read_ops, write_ops, read_latency, write_latency);

	/* return the dict*/
	return pyDict;
}

PyObject* create_tenant_client_dict(char *tenant_name, struct tenant_client *client, uint64_t time_tmp) {
	/* create dict */
	PyObject *pyDict = PyDict_New();
	if (pyDict == NULL) {
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create dict is error, pyDict=null");
		/* error */
		return NULL;
	}
	char hostname[MAXNAMLEN+1]; /* max hostname length */
	if (gsh_gethostname(hostname, sizeof(hostname),
		nfs_param.core_param.enable_AUTHSTATS) == -1) {
		strcpy(hostname, "unkown hostname");

	}
	char *ipaddr = alloca(SOCK_NAME_MAX);
	if (!sprint_sockip(&client->cl_addrbuf, ipaddr, SOCK_NAME_MAX))
		(void) strlcpy(ipaddr, "<unknown>", SOCK_NAME_MAX);
	if ((client->cl_addrbuf.ss_family == AF_INET6) && !strncmp(ipaddr, "::ffff:", 7))
		memmove(ipaddr, ipaddr+7, SOCK_NAME_MAX-7);

	double read_latency = 0.0;
	double write_latency = 0.0;
	double lookup_latency = 0.0;
	double getattr_latency = 0.0;
	double setattr_latency = 0.0;
	double create_latency = 0.0;
	double readdirplus_latency = 0.0;

	struct v3_full v3op_tem = client->v3op;
	struct v4_full v4op_tem = client->v4op;

	struct v3_full v3op_old = client->v3op_old;
	struct v4_full v4op_old = client->v4op_old;

	int status = 0;
	struct gnfs_io_stats v3_io_tem = client->iops.v3_io;
	struct gnfs_io_stats v4_io_tem = client->iops.v4_io;
	uint64_t read_ops = v3_io_tem.gnfs_read_ops + v4_io_tem.gnfs_read_ops;
	uint64_t read_bd = v3_io_tem.gnfs_read_bd + v4_io_tem.gnfs_read_bd;

	uint64_t write_ops = v3_io_tem.gnfs_write_ops + v4_io_tem.gnfs_write_ops;
	uint64_t write_bd = v3_io_tem.gnfs_write_bd + v4_io_tem.gnfs_write_bd;

	uint64_t total_ops = read_ops + write_ops;
	uint64_t total_bd = read_bd + write_bd;

	/*Retrieve the average delay for the Reporting Period. Periodic Update.*/
	read_latency = get_avg_latency(v3op_old.op[NFSPROC3_READ].cmd, v4op_old.op[NFS4_OP_READ].cmd,
		v3op_tem.op[NFSPROC3_READ].cmd, v4op_tem.op[NFS4_OP_READ].cmd);

	write_latency = get_avg_latency(v3op_old.op[NFSPROC3_WRITE].cmd, v4op_old.op[NFS4_OP_WRITE].cmd,
		v3op_tem.op[NFSPROC3_WRITE].cmd, v4op_tem.op[NFS4_OP_WRITE].cmd);
	lookup_latency = get_avg_latency(v3op_old.op[NFSPROC_LOOKUP].cmd, v4op_old.op[NFS4_OP_LOOKUP].cmd, 
		v3op_tem.op[NFSPROC_LOOKUP].cmd, v4op_tem.op[NFS4_OP_LOOKUP].cmd);
	
	getattr_latency = get_avg_latency(v3op_old.op[NFSPROC3_GETATTR].cmd, v4op_old.op[NFS4_OP_GETATTR].cmd, 
		v3op_tem.op[NFSPROC3_GETATTR].cmd, v4op_tem.op[NFS4_OP_GETATTR].cmd);
	
	setattr_latency = get_avg_latency(v3op_old.op[NFSPROC3_GETATTR].cmd, v4op_old.op[NFS4_OP_SETATTR].cmd, 
		v3op_tem.op[NFSPROC3_GETATTR].cmd, v4op_tem.op[NFS4_OP_SETATTR].cmd);
	
	create_latency = get_avg_latency(v3op_old.op[NFSPROC3_CREATE].cmd, v4op_old.op[NFS4_OP_CREATE].cmd, 
		v3op_tem.op[NFSPROC3_CREATE].cmd, v4op_tem.op[NFS4_OP_CREATE].cmd);
	
	readdirplus_latency = get_avg_latency(v3op_old.op[NFSPROC3_READDIRPLUS].cmd, v4op_old.op[NFS4_OP_READDIR].cmd, 
		v3op_tem.op[NFSPROC3_READDIRPLUS].cmd, v4op_tem.op[NFS4_OP_READDIR].cmd);
	/*Record the historical information of the last cycle*/
	client->v3op_old = client->v3op;
	client->v4op_old = client->v4op;
	LogDebug(COMPONENT_REPORT_ISM, "tenant name:%s, ipaddr:%s ", tenant_name, ipaddr);
	/* add key-value to dict */
	status = add_key_char_to_dict(pyDict, "table", "pm_node_tenant_nfs_client");
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:table", status);
	}

	status = add_key_long_to_dict(pyDict, "time", time_tmp);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:time", status);
	}
	status = add_key_char_to_dict(pyDict, "node", hostname);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:node", status);
	}
	status = add_key_char_to_dict(pyDict, "tenant", tenant_name);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:device_ip", status);
	}
	status = add_key_char_to_dict(pyDict, "device_ip", ipaddr);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:device_ip", status);
	}
	status = add_key_long_to_dict(pyDict, "band", total_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:band", status);
	}
	status = add_key_long_to_dict(pyDict, "r_band", read_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_band", status);
	}
	status = add_key_long_to_dict(pyDict, "w_band", write_bd);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_band", status);
	}

	status = add_key_long_to_dict(pyDict, "iops", total_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:iops", status);
	}
	status = add_key_long_to_dict(pyDict, "r_iops", read_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:r_iops", status);
	}
	status = add_key_long_to_dict(pyDict, "w_iops", write_ops);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:w_iops", status);
	}

	status = add_key_long_to_dict(pyDict, "ll_read", read_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_read", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_write", write_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_write", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_lookup", lookup_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_lookup", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_getattr", getattr_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_getattr", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_setattr", setattr_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_setattr", status);
	}
	status = add_key_long_to_dict(pyDict, "ll_create", create_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:ll_create", status);
	}
	status = add_key_long_to_dict(pyDict, "readdirplus_r", readdirplus_latency);
	if(status != 0){
		LogEvent(COMPONENT_REPORT_ISM, "add_key_to_dict is error, status:%d, key:readdirplus_r", status);
	}
	LogDebug(COMPONENT_REPORT_ISM, "time:%lu, node:%s, tenant_name:%s, client_ip:%s, band:%lu,r_band:%lu,w_band:%lu, iops:%lu, r_iops:%lu, w_iops:%lu",
		time_tmp, hostname, tenant_name, ipaddr, total_bd, read_bd, write_bd, total_ops, read_ops, write_ops);
	LogDebug(COMPONENT_REPORT_ISM, "r_avg_delay:%lf,w_avg_delay:%lf,l_avg_delay:%lf,g_avg_delay:%lf,s_avg_delay:%lf,c_avg_delay:%lf,rdir_avg_delay:%lf ",
		read_latency, write_latency, lookup_latency, getattr_latency, setattr_latency, create_latency, readdirplus_latency);
	/* return the dict*/
	return pyDict;
}

PyObject* create_node_list(uint64_t time_tmp) {
	/* create a Python list  */
	PyObject *pyList = PyList_New(0);
	if (pyList == NULL) {
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create a Python list is error, pyList is null");
		/* error */
		return NULL;
	}

	/* create a Python dict */  
	PyObject *pyDict = create_node_dict(time_tmp);  
	if (pyDict == NULL) {  
		PyErr_Print();  
		Py_DECREF(pyList);
		LogEvent(COMPONENT_REPORT_ISM, "create_dict_of_read is error, pyDict is null");
		/* error */
		return NULL;
	}  

	/*add dict to list */
	if (PyList_Append(pyList, pyDict) != 0) {  
		PyErr_Print();  
		Py_DECREF(pyDict);  
		Py_DECREF(pyList);  
		LogEvent(COMPONENT_REPORT_ISM, "PyList_Append is error, fail to add dict to list");
		/* error */
		return NULL;
	}  

	/* release dict*/ 
	Py_DECREF(pyDict);   
	
	return pyList; 
}  

PyObject* create_client_list(uint64_t time_tmp) {  
	/* create a Python list  */
	PyObject *pyList = PyList_New(0);  
	if (pyList == NULL) {  
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create a Python list is error, pyList is null");
		/* error */
		return NULL;
	}
	struct avltree_node *client_node = NULL;
	struct gsh_client *cl = NULL;

	PTHREAD_RWLOCK_rdlock(&client_by_ip.lock);
	for (client_node = avltree_first(&client_by_ip.t); client_node != NULL;
		client_node = avltree_next(client_node)) {
		cl = avltree_container_of(client_node, struct gsh_client, node_k);
		
		/* create a Python dict */  
		PyObject *pyDict = create_client_dict(cl, time_tmp);  
		if (pyDict == NULL) {  
			PyErr_Print();  
			Py_DECREF(pyList);
			LogEvent(COMPONENT_REPORT_ISM, "create_dict_of_read is error, pyDict is null");
			/* error */
			return NULL;
		}  

		/*add dict to list */
		if (PyList_Append(pyList, pyDict) != 0) {  
			PyErr_Print();  
			Py_DECREF(pyDict);  
			Py_DECREF(pyList);  
			LogEvent(COMPONENT_REPORT_ISM, "PyList_Append is error, fail to add dict to list");
			/* error */
			return NULL;
		}  

		/* release dict*/ 
		Py_DECREF(pyDict);   
	}
	PTHREAD_RWLOCK_unlock(&client_by_ip.lock);

	return pyList; 
}  

PyObject* create_export_list(uint64_t time_tmp) {  
	/* create a Python list  */
	PyObject *pyList = PyList_New(0);  
	if (pyList == NULL) {  
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create a Python list is error, pyList is null");
		/* error */
		return NULL;
	}
	struct glist_head *glist, *glistn;
	struct gsh_export *export = NULL;


	PTHREAD_RWLOCK_rdlock(&export_by_id.lock);
	glist_for_each_safe(glist, glistn, &exportlist) {
		export = glist_entry(glist, struct gsh_export, exp_list);

		char *path = NULL;
		struct tmp_export_paths tmp = {NULL, NULL};
		tmp_get_exp_paths(&tmp, export);
		path = tmp_export_path(&tmp);
		if(path == NULL || strcmp(path, "/") == 0){
			tmp_put_exp_paths(&tmp);
			LogDebug(COMPONENT_REPORT_ISM, "create dict is error, path == NULL || strcmp(path, /) == 0");
			continue;
		}

		/* create a Python dict */  
		PyObject *pyDict = create_export_dict(export, time_tmp);  
		if (pyDict == NULL) {  
			PyErr_Print();  
			Py_DECREF(pyList);
			LogEvent(COMPONENT_REPORT_ISM, "create_export_dict is error, pyDict is null");
			/* error */
			return NULL;
		}  

		/*add dict to list */
		if (PyList_Append(pyList, pyDict) != 0) {  
			PyErr_Print();  
			Py_DECREF(pyDict);  
			Py_DECREF(pyList);  
			LogEvent(COMPONENT_REPORT_ISM, "PyList_Append is error, fail to add dict to list");
			/* error */
			return NULL;
		}

		/* release dict*/
		Py_DECREF(pyDict);
	}
	PTHREAD_RWLOCK_unlock(&export_by_id.lock);

	return pyList;
}

PyObject* create_tenant_list(uint64_t time_tmp) {
	/* create a Python list  */
	PyObject *pyList = PyList_New(0);
	if (pyList == NULL) {
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create a Python list is error, pyList is null");
		/* error */
		return NULL;
	}

	struct avltree_node *tenant_node = NULL;
	struct gsh_tenant *tenant = NULL;

	PTHREAD_RWLOCK_rdlock(&tenant_by_name.lock);

	for (tenant_node = avltree_first(&tenant_by_name.t); tenant_node != NULL;
	     tenant_node = avltree_next(tenant_node)) {
		tenant = avltree_container_of(tenant_node, struct gsh_tenant,
					  node_k);

		/* create a Python dict */
		PyObject *pyDict = create_tenant_dict(tenant, time_tmp);
		if (pyDict == NULL) {
			PyErr_Print();
			Py_DECREF(pyList);
			LogEvent(COMPONENT_REPORT_ISM, "create_tenant_dict is error, pyDict is null");
			/* error */
			return NULL;
		}

		/*add dict to list */
		if (PyList_Append(pyList, pyDict) != 0) {
			PyErr_Print();
			Py_DECREF(pyDict);
			Py_DECREF(pyList);
			LogEvent(COMPONENT_REPORT_ISM, "PyList_Append is error, fail to add dict to list");
			/* error */
			return NULL;
		}

		/* release dict*/
		Py_DECREF(pyDict);
	}
	PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);

	return pyList;
}

PyObject* create_tenant_client_list(uint64_t time_tmp) {
	/* create a Python list  */
	PyObject *pyList = PyList_New(0);
	if (pyList == NULL) {
		PyErr_Print();
		LogEvent(COMPONENT_REPORT_ISM, "create a Python list is error, pyList is null");
		/* error */
		return NULL;
	}

	struct avltree_node *tenant_node = NULL;
	struct gsh_tenant *tenant = NULL;
	struct avltree_node *client_node = NULL;
	struct tenant_client *client = NULL;
	size_t len = 0;
	char *tenant_name = NULL;
	
	PTHREAD_RWLOCK_rdlock(&tenant_by_name.lock);

	for (tenant_node = avltree_first(&tenant_by_name.t); tenant_node != NULL;
		tenant_node = avltree_next(tenant_node)) {
		tenant = avltree_container_of(tenant_node, struct gsh_tenant, node_k);
		
		len = strlen(tenant->tenant_name) + 1;
		tenant_name = malloc(len);
		if(tenant_name == NULL){
			LogEvent(COMPONENT_REPORT_ISM, "tenant_name malloc error, tenant name:%s ", tenant->tenant_name);
			continue;
		}
		memcpy(tenant_name, tenant->tenant_name, len);

		PTHREAD_RWLOCK_rdlock(&tenant->client_lock);
		for (client_node = avltree_first(&tenant->client_tree); client_node != NULL;
			client_node = avltree_next(client_node)) {
			client = avltree_container_of(client_node, struct tenant_client, node_k);

			/* create a Python dict */
			PyObject *pyDict = create_tenant_client_dict(tenant_name, client, time_tmp);
			if (pyDict == NULL) {
				PyErr_Print();
				Py_DECREF(pyList);
				if(tenant_name){
					free(tenant_name);
					tenant_name = NULL;
				}
				LogEvent(COMPONENT_REPORT_ISM, "create_tenant_dict is error, pyDict is null");
				/* error */
				return NULL;
			}

			/*add dict to list */
			if (PyList_Append(pyList, pyDict) != 0) {
				PyErr_Print();
				Py_DECREF(pyDict);
				Py_DECREF(pyList);
				if(tenant_name){
					free(tenant_name);
					tenant_name = NULL;
				}
				LogEvent(COMPONENT_REPORT_ISM, "PyList_Append is error, fail to add dict to list");
				/* error */
				return NULL;
			}

			/* release dict*/
			Py_DECREF(pyDict);
			}
		PTHREAD_RWLOCK_unlock(&tenant->client_lock);
		if(tenant_name){
			free(tenant_name);
			tenant_name = NULL;
		}
	}
	PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);
	return pyList;
}


int node_report(PyObject *pyList){
	int ret = 0;
	if (pyList == NULL) {
		LogEvent(COMPONENT_REPORT_ISM, "pyList is null, cannot report stats");
		ret = -1;
		goto out;
	}

	/* import your python module*/
	PyObject *pName = PyUnicode_FromString("sysmgt.monitor");
	PyObject *pModule = PyImport_Import(pName); 
	Py_DECREF(pName);
	
	if (pModule != NULL) {
		/* get the function you need */
		PyObject *pFunc = PyObject_GetAttrString(pModule, "report_stats"); 
		LogDebug(COMPONENT_REPORT_ISM, "gnfs_report_to_ism_func: load sysmgt.monitor report_stats successfully");
		if (pFunc && PyCallable_Check(pFunc)) {
			/* call the function and pass the list */
			PyObject *pArgs = PyTuple_Pack(1, pyList);
			PyObject *pValue = PyObject_CallObject(pFunc, pArgs);
			Py_DECREF(pArgs);
			LogDebug(COMPONENT_REPORT_ISM, "gnfs_report_to_ism_func: report return, begin release resources.");
			if (pValue != NULL) {
				/* processing the return value */
				Py_DECREF(pValue);
			} else {
				PyErr_Print();
				ret = -2;
			}
		
			Py_DECREF(pFunc);
		} else {
			PyErr_Print();
			ret = -3;
		}
		
		Py_DECREF(pModule);
	} else {
		PyErr_Print();
		ret = -4;
	}
out:
	return ret;

}

void *gnfs_report_to_ism_func(void *d)
{
	/* Initializes the python interpreter*/
	Py_Initialize();
	/* get GIL */
	PyGILState_STATE gstate = PyGILState_Ensure();
	while(1){
		struct timespec time_now;
		/* control reporting cycle*/
		now(&time_now);
		int stat = 0;
		uint64_t sleep_time = nfs_param.core_param.io_report_ism_time - (time_now.tv_sec % nfs_param.core_param.io_report_ism_time);
		sleep(sleep_time);
		/* calculate the report timestamp*/
		now(&time_now);
		uint64_t time_tmp = time_now.tv_sec;
		time_tmp = (time_tmp / nfs_param.core_param.io_report_ism_time) * nfs_param.core_param.io_report_ism_time;
		LogDebug(COMPONENT_REPORT_ISM, "gnfs_report_to_ism_func begin : time:%lu, slepp_time:%lu", time_tmp, sleep_time);
		if(nfs_param.core_param.enable_report_node){
			/* create list and get data */
			PyObject *node_pyList = create_node_list(time_tmp);
			if (node_pyList == NULL) {
				LogEvent(COMPONENT_REPORT_ISM, "node_pyList is null, fail to get data, goto out");
				goto out;
			}
			stat = node_report(node_pyList);
			if (stat != 0) {
				LogEvent(COMPONENT_REPORT_ISM, "node_report failed with status: %d", stat);
			}
			Py_DECREF(node_pyList);
		}
		
		if(nfs_param.core_param.enable_report_client){
			/* create list and get data */
			PyObject *client_pyList = create_client_list(time_tmp);
			if (client_pyList == NULL) {
				LogEvent(COMPONENT_REPORT_ISM, "client_pyList is null, fail to get data, goto out");
				goto out;
			}
			stat = node_report(client_pyList);
			if (stat != 0) {
				LogEvent(COMPONENT_REPORT_ISM, "node_report failed with status: %d", stat);
			}
			Py_DECREF(client_pyList);
		}

		if(nfs_param.core_param.enable_report_export){
			/* create list and get data */
			PyObject *export_pyList = create_export_list(time_tmp);
			if (export_pyList == NULL) {
				LogEvent(COMPONENT_REPORT_ISM, "export_pyList is null, fail to get data, goto out");
				goto out;
			}
			stat = node_report(export_pyList);
			if (stat != 0) {
				LogEvent(COMPONENT_REPORT_ISM, "node_report failed with status: %d", stat);
			}
			Py_DECREF(export_pyList);
		}
		if(nfs_param.core_param.enable_report_tenant){
			/* create list and get data */
			PyObject *tenant_pyList = create_tenant_list(time_tmp);
			if (tenant_pyList == NULL) {
				LogEvent(COMPONENT_REPORT_ISM, "tenant_pyList is null, fail to get data, goto out");
				goto out;
			}
			stat = node_report(tenant_pyList);
			if (stat != 0) {
				LogEvent(COMPONENT_REPORT_ISM, "node_report failed with status: %d", stat);
			}
			Py_DECREF(tenant_pyList);
		}
		if(nfs_param.core_param.enable_report_tenant_client){
			/* create list and get data */
			PyObject *tenant_client_pyList = create_tenant_client_list(time_tmp);
			if (tenant_client_pyList == NULL) {
				LogEvent(COMPONENT_REPORT_ISM, "tenant_client_pyList is null, fail to get data, goto out");
				goto out;
			}
			stat = node_report(tenant_client_pyList);
			if (stat != 0) {
				LogEvent(COMPONENT_REPORT_ISM, "node_report failed with status: %d", stat);
			}
			Py_DECREF(tenant_client_pyList);
		}

out:
		LogDebug(COMPONENT_REPORT_ISM, "gnfs_report_to_ism_func end");
	}
	/* release the GIL and close the python interpreter*/
	PyGILState_Release(gstate);
	Py_Finalize();
}

void *check_nfs_status_func_tick(void *d)
{
	while(1) {
		usleep(200000000);
		sem_post(&sem);
	}
}
void *check_nfs_status_func(void *d)
{
	while(1){
		sem_wait(&sem);
		uint64_t request_count = 0;
		uint64_t handled_count = 0;
		int nfs_status = 1;
		struct fsal_module *fsal_hdl;
		char *fsal_name = "idfs";
		struct fsal_stats *idfs_stats;
		struct req_op_context op_context;
		if(nfs_param.core_param.enable_check_nfs_service){

			init_op_context_simple(&op_context, NULL, NULL);
			fsal_hdl = lookup_fsal(fsal_name);
			release_op_context();
			idfs_stats = fsal_hdl->stats;

			request_count = atomic_fetch_uint64_t(&idfs_stats->op_total_stats.receive_num_ops);
			handled_count = atomic_fetch_uint64_t(&idfs_stats->op_total_stats.num_ops);
		
			if (request_count > gnfs_last_request_count && 
				handled_count == gnfs_last_handled_count) {
				nfs_status = 103;
				
			}else if(request_count == gnfs_last_request_count && 
				handled_count == gnfs_last_handled_count && request_count > handled_count){
				nfs_status = 103;

			}
				
			if(nfs_status !=1 ){
				LogEvent(COMPONENT_CONFIG, "gnfs service request stuck, statu:%d, request:%lu, handle:%lu, last_request:%lu, last_handle:%lu, ",
					nfs_status, request_count, handled_count, gnfs_last_request_count, gnfs_last_handled_count);
			}
			if(nfs_param.core_param.enable_nfs_service_status){
				nfs_param.core_param.nfs_service_status = nfs_status;
			}
			LogDebug(COMPONENT_CONFIG, "request:%lu, handle:%lu, last_request:%lu, last_handle:%lu, nfs_status:%d, nfs_service_status:%lu",
				request_count, handled_count, gnfs_last_request_count, gnfs_last_handled_count, nfs_status, nfs_param.core_param.nfs_service_status);

			gnfs_last_request_count = request_count;
			gnfs_last_handled_count = handled_count;
		}
	}
}

void check_nfs_tick_shutdown()
{
    pthread_cancel(tid1);
    pthread_cancel(tid2);
}
/*add for gnfs_io_stats  */

void *gnfs_io_stats_tick(void *d)
{
	while(1) {
		usleep(1000000);
		sem_post(&io_sem);
	}
}
void update_tenant_bd_iops()
{
	struct avltree_node *tenant_node = NULL;
	struct gsh_tenant *tenant = NULL;

	PTHREAD_RWLOCK_rdlock(&tenant_by_name.lock);
	for (tenant_node = avltree_first(&tenant_by_name.t); tenant_node != NULL;
		tenant_node = avltree_next(tenant_node)) {
		tenant = avltree_container_of(tenant_node, struct gsh_tenant, node_k);
		LogDebug(COMPONENT_CONFIG, "update_tenant_bd_iops, tenant: %s tenant: %p", tenant->tenant_name, tenant);
		struct gnfs_io_stats *v3_io = &tenant->tn_iops.v3_io;
		struct gnfs_io_stats *v4_io = &tenant->tn_iops.v4_io;
		/* refresh v3 read /write/total bd*/
		v3_io->gnfs_write_bd = atomic_fetch_uint64_t(&v3_io->gnfs_write_flow);
		v3_io->gnfs_read_bd = atomic_fetch_uint64_t(&v3_io->gnfs_read_flow);
		v3_io->gnfs_total_bd = v3_io->gnfs_write_bd + v3_io->gnfs_read_bd;
		/* refresh v3 read /write/total ops*/
		v3_io->gnfs_write_ops = atomic_fetch_uint64_t(&v3_io->gnfs_writeops_flow);
		v3_io->gnfs_read_ops = atomic_fetch_uint64_t(&v3_io->gnfs_readops_flow);
		v3_io->gnfs_total_ops = v3_io->gnfs_write_ops + v3_io->gnfs_read_ops;
		/* clear tem variables*/
		(void)atomic_store_uint64_t(&v3_io->gnfs_write_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_read_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_writeops_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_readops_flow, 0);
		/* refresh v4 read /write/total bd*/
		v4_io->gnfs_write_bd = atomic_fetch_uint64_t(&v4_io->gnfs_write_flow);
		v4_io->gnfs_read_bd = atomic_fetch_uint64_t(&v4_io->gnfs_read_flow);
		v4_io->gnfs_total_bd = v4_io->gnfs_write_bd + v4_io->gnfs_read_bd;
		/* refresh v4 read /write/total ops*/
		v4_io->gnfs_write_ops = atomic_fetch_uint64_t(&v4_io->gnfs_writeops_flow);
		v4_io->gnfs_read_ops = atomic_fetch_uint64_t(&v4_io->gnfs_readops_flow);
		v4_io->gnfs_total_ops = v4_io->gnfs_write_ops + v4_io->gnfs_read_ops;
		/* clear tem variables*/
		(void)atomic_store_uint64_t(&v4_io->gnfs_write_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_read_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_writeops_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_readops_flow, 0);
	}
	PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);

}
void update_tenant_client_bd_iops()
{
	struct avltree_node *tenant_node = NULL;
	struct gsh_tenant *tenant = NULL;
	struct avltree_node *client_node = NULL;
	struct tenant_client *client = NULL;

	PTHREAD_RWLOCK_rdlock(&tenant_by_name.lock);
	for (tenant_node = avltree_first(&tenant_by_name.t); tenant_node != NULL;
		tenant_node = avltree_next(tenant_node)) {
		tenant = avltree_container_of(tenant_node, struct gsh_tenant, node_k);

		PTHREAD_RWLOCK_rdlock(&tenant->client_lock);
		for (client_node = avltree_first(&tenant->client_tree); client_node != NULL;
			client_node = avltree_next(client_node)) {
			client = avltree_container_of(client_node, struct tenant_client, node_k);
			LogDebug(COMPONENT_CONFIG, "tenant: %s, client ip: %s", tenant->tenant_name, client->hostaddr_str);
			struct gnfs_io_stats *v3_io = &client->iops.v3_io;
			struct gnfs_io_stats *v4_io = &client->iops.v4_io;
			/* refresh v3 read /write/total bd*/
			v3_io->gnfs_write_bd = atomic_fetch_uint64_t(&v3_io->gnfs_write_flow);
			v3_io->gnfs_read_bd = atomic_fetch_uint64_t(&v3_io->gnfs_read_flow);
			v3_io->gnfs_total_bd = v3_io->gnfs_write_bd + v3_io->gnfs_read_bd;
			/* refresh v3 read /write/total ops*/
			v3_io->gnfs_write_ops = atomic_fetch_uint64_t(&v3_io->gnfs_writeops_flow);
			v3_io->gnfs_read_ops = atomic_fetch_uint64_t(&v3_io->gnfs_readops_flow);
			v3_io->gnfs_total_ops = v3_io->gnfs_write_ops + v3_io->gnfs_read_ops;
			/* clear tem variables*/
			(void)atomic_store_uint64_t(&v3_io->gnfs_write_flow, 0);
			(void)atomic_store_uint64_t(&v3_io->gnfs_read_flow, 0);
			(void)atomic_store_uint64_t(&v3_io->gnfs_writeops_flow, 0);
			(void)atomic_store_uint64_t(&v3_io->gnfs_readops_flow, 0);
			/* refresh v4 read /write/total bd*/
			v4_io->gnfs_write_bd = atomic_fetch_uint64_t(&v4_io->gnfs_write_flow);
			v4_io->gnfs_read_bd = atomic_fetch_uint64_t(&v4_io->gnfs_read_flow);
			v4_io->gnfs_total_bd = v4_io->gnfs_write_bd + v4_io->gnfs_read_bd;
			/* refresh v4 read /write/total ops*/
			v4_io->gnfs_write_ops = atomic_fetch_uint64_t(&v4_io->gnfs_writeops_flow);
			v4_io->gnfs_read_ops = atomic_fetch_uint64_t(&v4_io->gnfs_readops_flow);
			v4_io->gnfs_total_ops = v4_io->gnfs_write_ops + v4_io->gnfs_read_ops;
			/* clear tem variables*/
			(void)atomic_store_uint64_t(&v4_io->gnfs_write_flow, 0);
			(void)atomic_store_uint64_t(&v4_io->gnfs_read_flow, 0);
			(void)atomic_store_uint64_t(&v4_io->gnfs_writeops_flow, 0);
			(void)atomic_store_uint64_t(&v4_io->gnfs_readops_flow, 0);

		}
		PTHREAD_RWLOCK_unlock(&tenant->client_lock);
	}
	PTHREAD_RWLOCK_unlock(&tenant_by_name.lock);

}
void update_client_bd_iops()
{
	struct avltree_node *client_node;
	struct gsh_client *cl;
	struct server_stats *cl_stats;

	PTHREAD_RWLOCK_rdlock(&client_by_ip.lock);
	for (client_node = avltree_first(&client_by_ip.t); client_node != NULL;
		client_node = avltree_next(client_node)) {
		cl = avltree_container_of(client_node, struct gsh_client, node_k);
		cl_stats = container_of(cl, struct server_stats, client);

		struct gnfs_io_stats *v3_io = &cl_stats->client_io.v3_io;
		struct gnfs_io_stats *v4_io = &cl_stats->client_io.v4_io;
		/* refresh v3 read /write/total bd*/
		v3_io->gnfs_write_bd = atomic_fetch_uint64_t(&v3_io->gnfs_write_flow);
		v3_io->gnfs_read_bd = atomic_fetch_uint64_t(&v3_io->gnfs_read_flow);
		v3_io->gnfs_total_bd = v3_io->gnfs_write_bd + v3_io->gnfs_read_bd;
		/* refresh v3 read /write/total ops*/
		v3_io->gnfs_write_ops = atomic_fetch_uint64_t(&v3_io->gnfs_writeops_flow);
		v3_io->gnfs_read_ops = atomic_fetch_uint64_t(&v3_io->gnfs_readops_flow);
		v3_io->gnfs_total_ops = v3_io->gnfs_write_ops + v3_io->gnfs_read_ops;
		/* clear tem variables*/
		(void)atomic_store_uint64_t(&v3_io->gnfs_write_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_read_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_writeops_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_readops_flow, 0);
		/* refresh v4 read /write/total bd*/
		v4_io->gnfs_write_bd = atomic_fetch_uint64_t(&v4_io->gnfs_write_flow);
		v4_io->gnfs_read_bd = atomic_fetch_uint64_t(&v4_io->gnfs_read_flow);
		v4_io->gnfs_total_bd = v4_io->gnfs_write_bd + v4_io->gnfs_read_bd;
		/* refresh v4 read /write/total ops*/
		v4_io->gnfs_write_ops = atomic_fetch_uint64_t(&v4_io->gnfs_writeops_flow);
		v4_io->gnfs_read_ops = atomic_fetch_uint64_t(&v4_io->gnfs_readops_flow);
		v4_io->gnfs_total_ops = v4_io->gnfs_write_ops + v4_io->gnfs_read_ops;
		/* clear tem variables*/
		(void)atomic_store_uint64_t(&v4_io->gnfs_write_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_read_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_writeops_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_readops_flow, 0);
	}
	PTHREAD_RWLOCK_unlock(&client_by_ip.lock);

}
void update_export_bd_iops()
{
	struct glist_head *glist, *glistn;
	struct gsh_export *export;
	struct export_stats *exp;

	PTHREAD_RWLOCK_rdlock(&export_by_id.lock);
	glist_for_each_safe(glist, glistn, &exportlist) {
		export = glist_entry(glist, struct gsh_export, exp_list);

		exp = container_of(export, struct export_stats, export);

		struct gnfs_io_stats *v3_io = &exp->exp_io.v3_io;
		struct gnfs_io_stats *v4_io = &exp->exp_io.v4_io;

		struct qps_stats *v3_qps = &exp->v3_qps;
		struct qps_stats *v4_qps = &exp->v4_qps;

		/* refresh v3 read /write/total bd*/
		v3_io->gnfs_write_bd = atomic_fetch_uint64_t(&v3_io->gnfs_write_flow);
		v3_io->gnfs_read_bd = atomic_fetch_uint64_t(&v3_io->gnfs_read_flow);
		v3_io->gnfs_total_bd = v3_io->gnfs_write_bd + v3_io->gnfs_read_bd;
		/* refresh v3 read /write/total ops*/
		v3_io->gnfs_write_ops = atomic_fetch_uint64_t(&v3_io->gnfs_writeops_flow);
		v3_io->gnfs_read_ops = atomic_fetch_uint64_t(&v3_io->gnfs_readops_flow);
		v3_io->gnfs_total_ops = v3_io->gnfs_write_ops + v3_io->gnfs_read_ops;
		/* clear tem variables*/
		(void)atomic_store_uint64_t(&v3_io->gnfs_write_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_read_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_writeops_flow, 0);
		(void)atomic_store_uint64_t(&v3_io->gnfs_readops_flow, 0);
		/* refresh v4 read /write/total bd*/
		v4_io->gnfs_write_bd = atomic_fetch_uint64_t(&v4_io->gnfs_write_flow);
		v4_io->gnfs_read_bd = atomic_fetch_uint64_t(&v4_io->gnfs_read_flow);
		v4_io->gnfs_total_bd = v4_io->gnfs_write_bd + v4_io->gnfs_read_bd;
		/* refresh v4 read /write/total ops*/
		v4_io->gnfs_write_ops = atomic_fetch_uint64_t(&v4_io->gnfs_writeops_flow);
		v4_io->gnfs_read_ops = atomic_fetch_uint64_t(&v4_io->gnfs_readops_flow);
		v4_io->gnfs_total_ops = v4_io->gnfs_write_ops + v4_io->gnfs_read_ops;

		v3_qps->gnfs_create_ops = atomic_fetch_uint64_t(&v3_qps->gnfs_create_flow);
		v3_qps->gnfs_getattr_ops = atomic_fetch_uint64_t(&v3_qps->gnfs_getattr_flow);
		v3_qps->gnfs_setattr_ops = atomic_fetch_uint64_t(&v3_qps->gnfs_setattr_flow);
		v3_qps->gnfs_lookup_ops = atomic_fetch_uint64_t(&v3_qps->gnfs_lookup_flow);
		v3_qps->gnfs_readdirplus_ops = atomic_fetch_uint64_t(&v3_qps->gnfs_readdirplus_flow);

		v4_qps->gnfs_create_ops = atomic_fetch_uint64_t(&v4_qps->gnfs_create_flow);
		v4_qps->gnfs_getattr_ops = atomic_fetch_uint64_t(&v4_qps->gnfs_getattr_flow);
		v4_qps->gnfs_setattr_ops = atomic_fetch_uint64_t(&v4_qps->gnfs_setattr_flow);
		v4_qps->gnfs_lookup_ops = atomic_fetch_uint64_t(&v4_qps->gnfs_lookup_flow);
		v4_qps->gnfs_readdirplus_ops = atomic_fetch_uint64_t(&v4_qps->gnfs_readdirplus_flow);

		/* clear tem variables*/
		(void)atomic_store_uint64_t(&v4_io->gnfs_write_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_read_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_writeops_flow, 0);
		(void)atomic_store_uint64_t(&v4_io->gnfs_readops_flow, 0);

		(void)atomic_store_uint64_t(&v3_qps->gnfs_create_flow, 0);
		(void)atomic_store_uint64_t(&v3_qps->gnfs_getattr_flow, 0);
		(void)atomic_store_uint64_t(&v3_qps->gnfs_setattr_flow, 0);
		(void)atomic_store_uint64_t(&v3_qps->gnfs_lookup_flow, 0);
		(void)atomic_store_uint64_t(&v3_qps->gnfs_readdirplus_flow, 0);

		(void)atomic_store_uint64_t(&v4_qps->gnfs_create_flow, 0);
		(void)atomic_store_uint64_t(&v4_qps->gnfs_getattr_flow, 0);
		(void)atomic_store_uint64_t(&v4_qps->gnfs_setattr_flow, 0);
		(void)atomic_store_uint64_t(&v4_qps->gnfs_lookup_flow, 0);
		(void)atomic_store_uint64_t(&v4_qps->gnfs_readdirplus_flow, 0);
	}
	PTHREAD_RWLOCK_unlock(&export_by_id.lock);

}

void update_gnfs_bd_iops()
{
	pthread_mutex_lock(&gnfs_io.v3_io.lock);
	gnfs_io.v3_io.gnfs_write_bd = gnfs_io.v3_io.gnfs_write_flow;
	gnfs_io.v3_io.gnfs_read_bd = gnfs_io.v3_io.gnfs_read_flow;
	gnfs_io.v3_io.gnfs_total_bd = gnfs_io.v3_io.gnfs_write_bd + gnfs_io.v3_io.gnfs_read_bd;

	gnfs_io.v3_io.gnfs_write_ops = gnfs_io.v3_io.gnfs_writeops_flow;
	gnfs_io.v3_io.gnfs_read_ops = gnfs_io.v3_io.gnfs_readops_flow;
	gnfs_io.v3_io.gnfs_total_ops = gnfs_io.v3_io.gnfs_write_ops + gnfs_io.v3_io.gnfs_read_ops;

	gnfs_io.v3_io.gnfs_write_flow = 0;
	gnfs_io.v3_io.gnfs_read_flow = 0;
	gnfs_io.v3_io.gnfs_writeops_flow = 0;
	gnfs_io.v3_io.gnfs_readops_flow = 0;
	pthread_mutex_unlock(&gnfs_io.v3_io.lock);

	pthread_mutex_lock(&gnfs_io.v4_io.lock);
	gnfs_io.v4_io.gnfs_write_bd = gnfs_io.v4_io.gnfs_write_flow;
	gnfs_io.v4_io.gnfs_read_bd = gnfs_io.v4_io.gnfs_read_flow;
	gnfs_io.v4_io.gnfs_total_bd = gnfs_io.v4_io.gnfs_write_bd + gnfs_io.v4_io.gnfs_read_bd;

	gnfs_io.v4_io.gnfs_write_ops = gnfs_io.v4_io.gnfs_writeops_flow;
	gnfs_io.v4_io.gnfs_read_ops = gnfs_io.v4_io.gnfs_readops_flow;
	gnfs_io.v4_io.gnfs_total_ops = gnfs_io.v4_io.gnfs_write_ops + gnfs_io.v4_io.gnfs_read_ops;

	gnfs_io.v4_io.gnfs_write_flow = 0;
	gnfs_io.v4_io.gnfs_read_flow = 0;
	gnfs_io.v4_io.gnfs_writeops_flow = 0;
	gnfs_io.v4_io.gnfs_readops_flow = 0;
	pthread_mutex_unlock(&gnfs_io.v4_io.lock);

}

void *gnfs_io_stats_refresh_func(void *d)
{
	while(1){
		sem_wait(&io_sem);
		update_client_bd_iops();
		update_export_bd_iops();
		update_gnfs_bd_iops();
		update_tenant_bd_iops();
		update_tenant_client_bd_iops();
	}
}

void gnfs_io_stats_tick_shutdown()
{
	pthread_cancel(io_tid1);
	pthread_cancel(io_tid2);
}

void gnfs_report_ism_shutdown()
{
	pthread_cancel(report_tid);
}


tirpc_pkg_params ntirpc_pp = {
	TIRPC_DEBUG_FLAG_DEFAULT,
	0,
	SetNameFunction,
	(mem_format_t)rpc_warnx,
	gsh_free_size,
	gsh_malloc__,
	gsh_malloc_aligned__,
	gsh_calloc__,
	gsh_realloc__,
	gsh_pool_alloc__,
	gsh_pool_free__,
};

#ifdef _USE_9P
pthread_t _9p_dispatcher_thrid;
#endif

#ifdef _USE_9P_RDMA
pthread_t _9p_rdma_dispatcher_thrid;
#endif

#ifdef _USE_NFS_RDMA
pthread_t nfs_rdma_dispatcher_thrid;
#endif

char *nfs_config_path = GNFS_CONFIG_PATH;

char *nfs_pidfile_path = GNFS_PIDFILE_PATH;

/**
 * @brief Reread the configuration file to accomplish update of options.
 *
 * The following option blocks are currently supported for update:
 *
 * LOG {}
 * LOG { COMPONENTS {} }
 * LOG { FACILITY {} }
 * LOG { FORMAT {} }
 * EXPORT {}
 * EXPORT { CLIENT {} }
 *
 */

struct config_error_type err_type;

void reread_config(void)
{
	int status = 0;
	config_file_t config_struct;

	/* If no configuration file is given, then the caller must want to
	 * reparse the configuration file from startup.
	 */
	if (nfs_config_path[0] == '\0') {
		LogCrit(COMPONENT_CONFIG,
			"No configuration file was specified for reloading log config.");
		return;
	}

	/* Create a memstream for parser+processing error messages */
	if (!init_error_type(&err_type))
		return;
	/* Attempt to parse the new configuration file */
	config_struct = config_ParseFile(nfs_config_path, &err_type);
	if (!config_error_no_error(&err_type)) {
		config_Free(config_struct);
		LogCrit(COMPONENT_CONFIG,
			"Error while parsing new configuration file %s",
			nfs_config_path);
		report_config_errors(&err_type, NULL, config_errs_to_log);
		return;
	}

	/* Update the logging configuration */
	status = read_log_config(config_struct, &err_type);
	if (status < 0)
		LogCrit(COMPONENT_CONFIG, "Error while parsing LOG entries");

	/* Update the export configuration */
	status = reread_exports(config_struct, &err_type);
	if (status < 0)
		LogCrit(COMPONENT_CONFIG, "Error while parsing EXPORT entries");

	report_config_errors(&err_type, NULL, config_errs_to_log);
	config_Free(config_struct);
}

/**
 * @brief This thread is in charge of signal management
 *
 * @param[in] UnusedArg Unused
 *
 * @return NULL.
 */
static void *sigmgr_thread(void *UnusedArg)
{
	int signal_caught = 0;

	SetNameFunction("sigmgr");
	rcu_register_thread();

	/* Loop until we catch SIGTERM */
	while (signal_caught != SIGTERM) {
		sigset_t signals_to_catch;

		sigemptyset(&signals_to_catch);
		sigaddset(&signals_to_catch, SIGTERM);
		sigaddset(&signals_to_catch, SIGHUP);
		if (sigwait(&signals_to_catch, &signal_caught) != 0) {
			LogFullDebug(COMPONENT_THREAD,
				     "sigwait exited with error");
			continue;
		}
		if (signal_caught == SIGHUP) {
			LogEvent(COMPONENT_MAIN,
				 "SIGHUP_HANDLER: Received SIGHUP.... initiating export list reload");
			reread_config();
#ifdef _HAVE_GSSAPI
			svcauth_gss_release_cred();
#endif /* _HAVE_GSSAPI */
		}
	}
	LogDebug(COMPONENT_THREAD, "sigmgr thread exiting");

	admin_halt();

	/* Might as well exit - no need for this thread any more */
	rcu_unregister_thread();
	return NULL;
}


static void crash_handler(int signo, siginfo_t *info, void *ctx)
{
	gsh_backtrace();
	/* re-raise the signal for the default signal handler to dump core */
	raise(signo);
}

static void install_sighandler(int signo,
			       void (*handler)(int, siginfo_t *, void *))
{
	struct sigaction sa = {};
	int ret;

	sa.sa_sigaction = handler;
	/* set SA_RESETHAND to restore default handler */
	sa.sa_flags = SA_SIGINFO | SA_RESETHAND | SA_NODEFER;

	sigemptyset(&sa.sa_mask);

	ret = sigaction(signo, &sa, NULL);
	if (ret) {
		LogWarn(COMPONENT_INIT,
			"Install handler for signal (%s) failed",
			strsignal(signo));
	}
}

static void init_crash_handlers(void)
{
	install_sighandler(SIGSEGV, crash_handler);
	install_sighandler(SIGABRT, crash_handler);
	install_sighandler(SIGBUS, crash_handler);
	install_sighandler(SIGILL, crash_handler);
	install_sighandler(SIGFPE, crash_handler);
	install_sighandler(SIGQUIT, crash_handler);
}

/**
 * @brief Initialize NFSd prerequisites
 *
 * @param[in] program_name Name of the program
 * @param[in] host_name    Server host name
 * @param[in] debug_level  Debug level
 * @param[in] log_path     Log path
 * @param[in] dump_trace   Dump trace when segfault
 */
void nfs_prereq_init(const char *program_name, const char *host_name,
		     int debug_level, const char *log_path, bool dump_trace)
{
	healthstats.enqueued_reqs = nfs_health_.enqueued_reqs = 0;
	healthstats.dequeued_reqs = nfs_health_.dequeued_reqs = 0;

	/* Initialize logging */
	SetNamePgm(program_name);
	SetNameFunction("main");
	SetNameHost(host_name);

	init_logging(log_path, debug_level);
	if (dump_trace) {
		init_crash_handlers();
	}

	/* Redirect TI-RPC allocators, log channel */
	if (!tirpc_control(TIRPC_PUT_PARAMETERS, &ntirpc_pp)) {
		LogFatal(COMPONENT_INIT, "Setting nTI-RPC parameters failed");
	}
}

/**
 * @brief Print the nfs_parameter_structure
 */
void nfs_print_param_config(void)
{
	printf("NFS_Core_Param\n{\n");

	printf("\tNFS_Port = %u ;\n", nfs_param.core_param.port[P_NFS]);
#ifdef _USE_NFS3
	printf("\tMNT_Port = %u ;\n", nfs_param.core_param.port[P_MNT]);
#endif
	printf("\tNFS_Program = %u ;\n", nfs_param.core_param.program[P_NFS]);
	printf("\tMNT_Program = %u ;\n", nfs_param.core_param.program[P_NFS]);
	printf("\tDRC_TCP_Npart = %u ;\n", nfs_param.core_param.drc.tcp.npart);
	printf("\tDRC_TCP_Size = %u ;\n", nfs_param.core_param.drc.tcp.size);
	printf("\tDRC_TCP_Cachesz = %u ;\n",
	       nfs_param.core_param.drc.tcp.cachesz);
	printf("\tDRC_TCP_Hiwat = %u ;\n", nfs_param.core_param.drc.tcp.hiwat);
	printf("\tDRC_TCP_Recycle_Npart = %u ;\n",
	       nfs_param.core_param.drc.tcp.recycle_npart);
	printf("\tDRC_TCP_Recycle_Expire_S = %u ;\n",
	       nfs_param.core_param.drc.tcp.recycle_expire_s);
	printf("\tDRC_TCP_Checksum = %u ;\n",
	       nfs_param.core_param.drc.tcp.checksum);
	printf("\tDRC_UDP_Npart = %u ;\n", nfs_param.core_param.drc.udp.npart);
	printf("\tDRC_UDP_Size = %u ;\n", nfs_param.core_param.drc.udp.size);
	printf("\tDRC_UDP_Cachesz = %u ;\n",
	       nfs_param.core_param.drc.udp.cachesz);
	printf("\tDRC_UDP_Hiwat = %u ;\n", nfs_param.core_param.drc.udp.hiwat);
	printf("\tDRC_UDP_Checksum = %u ;\n",
	       nfs_param.core_param.drc.udp.checksum);
	printf("\tBlocked_Lock_Poller_Interval = %" PRIu64 " ;\n",
	       (uint64_t) nfs_param.core_param.blocked_lock_poller_interval);

	printf("\tManage_Gids_Expiration = %" PRIu64 " ;\n",
	       (uint64_t) nfs_param.core_param.manage_gids_expiration);

	printf("\tDrop_IO_Errors = %s ;\n",
	       nfs_param.core_param.drop_io_errors ?  "true" : "false");

	printf("\tDrop_Inval_Errors = %s ;\n",
	       nfs_param.core_param.drop_inval_errors ?  "true" : "false");

	printf("\tDrop_Delay_Errors = %s ;\n",
	       nfs_param.core_param.drop_delay_errors ? "true" : "false");

	printf("\tEnable UDP = %u ;\n", nfs_param.core_param.enable_UDP);

	printf("\tEnable_write_ZEROCPY = %s ;\n", nfs_param.core_param.enable_write_ZEROCPY ?
	       "true" : "false");
	printf("\tEnable_read_ZEROCPY = %s ;\n", nfs_param.core_param.enable_read_ZEROCPY ?
	       "true" : "false");
	printf("}\n\n");
}

/**
 * @brief Load parameters from config file
 *
 * @param[in]  config_struct Parsed config file
 * @param[out] p_start_info  Startup parameters
 * @param[out] err_type error reporting state
 *
 * @return -1 on failure.
 */
int nfs_set_param_from_conf(config_file_t parse_tree,
			    nfs_start_info_t *p_start_info,
			    struct config_error_type *err_type)
{
	/*
	 * Initialize exports and clients so config parsing can use them
	 * early.
	 */
	client_pkginit();
	export_pkginit();
	server_pkginit();
	tenant_pkginit();
	ns_audit_pkginit();

	/* Core parameters */
	(void) load_config_from_parse(parse_tree,
				      &nfs_core,
				      &nfs_param.core_param,
				      true,
				      err_type);
	if (!config_error_is_harmless(err_type)) {
		LogCrit(COMPONENT_INIT,
			"Error while parsing core configuration");
		return -1;
	}
	/* rdma config */
	(void)load_config_from_parse(parse_tree, 
					  &rdma_config,
					  &rdma_param,
					  true,
					  err_type);

	/*add by zhanghao at 2021.4.20 for performance opt*/
	LogEvent(COMPONENT_INIT, 
			"the performance optimization is %d", nfs_param.core_param.performance_OPT);

	/* Worker paramters: ip/name hash table and expiration for each entry */
	(void) load_config_from_parse(parse_tree,
				      &nfs_ip_name,
				      NULL,
				      true,
				      err_type);
	if (!config_error_is_harmless(err_type)) {
		LogCrit(COMPONENT_INIT,
			"Error while parsing IP/name configuration");
		return -1;
	}

#ifdef _HAVE_GSSAPI
	/* NFS kerberos5 configuration */
	(void) load_config_from_parse(parse_tree,
				      &krb5_param,
				      &nfs_param.krb5_param,
				      true,
				      err_type);
	if (!config_error_is_harmless(err_type)) {
		LogCrit(COMPONENT_INIT,
			"Error while parsing NFS/KRB5 configuration for RPCSEC_GSS");
		return -1;
	}
#endif

	/* NFSv4 specific configuration */
	(void) load_config_from_parse(parse_tree,
				      &version4_param,
				      &nfs_param.nfsv4_param,
				      true,
				      err_type);
	if (!config_error_is_harmless(err_type)) {
		LogCrit(COMPONENT_INIT,
			"Error while parsing NFSv4 specific configuration");
		return -1;
	}

#ifdef _USE_9P
	(void) load_config_from_parse(parse_tree,
				      &_9p_param_blk,
				      NULL,
				      true,
				      err_type);
	if (!config_error_is_harmless(err_type)) {
		LogCrit(COMPONENT_INIT,
			"Error while parsing 9P specific configuration");
		return -1;
	}
#endif

	if (mdcache_set_param_from_conf(parse_tree, err_type) < 0)
		return -1;

	if (load_recovery_param_from_conf(parse_tree, err_type) < 0)
		return -1;

	if (gsh_uds_url_setup_watch() != 0) {
		LogEvent(COMPONENT_INIT, "Couldn't setup uds_urls");
		return -1;
	}

	LogEvent(COMPONENT_INIT, "Configuration file successfully parsed");

	return 0;
}

int init_server_pkgs(void)
{
	fsal_status_t fsal_status;
	state_status_t state_status;

	/* init uid2grp cache */
	uid2grp_cache_init();

	ng_cache_init(); /* netgroup cache */

	/* MDCACHE Initialisation */
	fsal_status = mdcache_pkginit();
	if (FSAL_IS_ERROR(fsal_status)) {
		LogCrit(COMPONENT_INIT,
			"MDCACHE FSAL could not be initialized, status=%s",
			fsal_err_txt(fsal_status));
		return -1;
	}
	init_local_ips();
	io_block_stat_init();
	state_status = state_lock_init();
	if (state_status != STATE_SUCCESS) {
		LogCrit(COMPONENT_INIT,
			"State Lock Layer could not be initialized, status=%s",
			state_err_str(state_status));
		return -1;
	}
	LogInfo(COMPONENT_INIT, "State lock layer successfully initialized");

	/* Init the IP/name cache */
	LogDebug(COMPONENT_INIT, "Now building IP/name cache");
	if (nfs_Init_ip_name() != IP_NAME_SUCCESS) {
		LogCrit(COMPONENT_INIT,
			"Error while initializing IP/name cache");
		return -1;
	}
	LogInfo(COMPONENT_INIT, "IP/name cache successfully initialized");

	LogEvent(COMPONENT_INIT, "Initializing ID Mapper.");
	if (!idmapper_init()) {
		LogCrit(COMPONENT_INIT, "Failed initializing ID Mapper.");
		return -1;
	}
	LogEvent(COMPONENT_INIT, "ID Mapper successfully initialized.");
	return 0;
}

static void nfs_Start_threads(void)
{
	int rc = 0;
	pthread_attr_t attr_thr;

	LogDebug(COMPONENT_THREAD, "Starting threads");

	/* Init for thread parameter (mostly for scheduling) */
	if (pthread_attr_init(&attr_thr) != 0)
		LogDebug(COMPONENT_THREAD, "can't init pthread's attributes");

	if (pthread_attr_setscope(&attr_thr, PTHREAD_SCOPE_SYSTEM) != 0)
		LogDebug(COMPONENT_THREAD, "can't set pthread's scope");

	if (pthread_attr_setdetachstate(&attr_thr,
					PTHREAD_CREATE_JOINABLE) != 0)
		LogDebug(COMPONENT_THREAD, "can't set pthread's join state");

	LogEvent(COMPONENT_THREAD, "Starting delayed executor.");
	delayed_start();

	/* Starting the thread dedicated to signal handling */
	rc = pthread_create(&sigmgr_thrid, &attr_thr, sigmgr_thread, NULL);
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create sigmgr_thread, error = %d (%s)",
			 errno, strerror(errno));
	}
	LogDebug(COMPONENT_THREAD, "sigmgr thread started");

#ifdef _USE_9P
	if (nfs_param.core_param.core_options & CORE_OPTION_9P) {
		/* Start 9P worker threads */
		rc = _9p_worker_init();
		if (rc != 0) {
			LogFatal(COMPONENT_THREAD,
				 "Could not start worker threads: %d", errno);
		}

		/* Starting the 9P/TCP dispatcher thread */
		rc = pthread_create(&_9p_dispatcher_thrid, &attr_thr,
				    _9p_dispatcher_thread, NULL);
		if (rc != 0) {
			LogFatal(COMPONENT_THREAD,
				 "Could not create  9P/TCP dispatcher, error = %d (%s)",
				 errno, strerror(errno));
		}
		LogEvent(COMPONENT_THREAD,
			 "9P/TCP dispatcher thread was started successfully");
	}
#endif

#ifdef _USE_9P_RDMA
	/* Starting the 9P/RDMA dispatcher thread */
	if (nfs_param.core_param.core_options & CORE_OPTION_9P) {
		rc = pthread_create(&_9p_rdma_dispatcher_thrid, &attr_thr,
				    _9p_rdma_dispatcher_thread, NULL);
		if (rc != 0) {
			LogFatal(COMPONENT_THREAD,
				 "Could not create  9P/RDMA dispatcher, error = %d (%s)",
				 errno, strerror(errno));
		}
		LogEvent(COMPONENT_THREAD,
			 "9P/RDMA dispatcher thread was started successfully");
	}
#endif

#ifdef USE_DBUS
	/* DBUS event thread */
	rc = pthread_create(&gsh_dbus_thrid, &attr_thr, gsh_dbus_thread, NULL);
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create gsh_dbus_thread, error = %d (%s)",
			 errno, strerror(errno));
	}
	LogEvent(COMPONENT_THREAD, "gsh_dbusthread was started successfully");
#endif

	/*start check_nfs_status_func and tick thread, 20s*/
	if (nfs_param.core_param.enable_check_nfs_service_thread) {
		sem_init(&sem, 0, 0);
		rc = pthread_create(&tid1, 0, check_nfs_status_func, 0);
		if (rc != 0) {
			LogFatal(COMPONENT_THREAD,
				 "Could not create check_nfs_status_func thread, error = %d (%s)",
				 errno, strerror(errno));
		}
		LogEvent(COMPONENT_THREAD, "check_nfs_status_func thread was started successfully");

		rc = pthread_create(&tid2, 0, check_nfs_status_func_tick, 0);
		if (rc != 0) {
			LogFatal(COMPONENT_THREAD,
				 "Could not create check_nfs_status_func_tick thread, error = %d (%s)",
				 errno, strerror(errno));
		}
		LogEvent(COMPONENT_THREAD, "check_nfs_status_func_tick thread was started successfully");
	}
	/* Starting the gnfs_io_stats  thread , refresh gnfs bd, read write iops */
	sem_init(&io_sem, 0, 0);
	rc = pthread_create(&io_tid1, 0, gnfs_io_stats_refresh_func, 0);
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create gnfs_io_stats_refresh_func, error = %d (%s)",
			 errno, strerror(errno));
	}
	LogEvent(COMPONENT_THREAD, "gnfs_io_stats_refresh_func thread was started successfully");
	
	rc = pthread_create(&io_tid2, 0, gnfs_io_stats_tick, 0);
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create gnfs_io_stats_tick thread, error = %d (%s)",
			 errno, strerror(errno));
	}
	LogEvent(COMPONENT_THREAD, "gnfs_io_stats_tick thread was started successfully");

	/* Starting the gnfs_report_to_ism_func thread , report gnfs bd, read write iops to ism  */
	rc = pthread_create(&report_tid, 0, gnfs_report_to_ism_func, 0);
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create gnfs_report_to_ism_func, error = %d (%s)",
			 errno, strerror(errno));
	}
	LogEvent(COMPONENT_THREAD, "gnfs_report_to_ism_func thread was started successfully");

	/* Starting the admin thread */
	rc = pthread_create(&admin_thrid, &attr_thr, admin_thread, NULL);
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create admin_thread, error = %d (%s)",
			 errno, strerror(errno));
	}
	LogEvent(COMPONENT_THREAD, "admin thread was started successfully");

	/* Starting the reaper thread */
	rc = reaper_init();
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create reaper_thread, error = %d (%s)",
			 errno, strerror(errno));
	}
	LogEvent(COMPONENT_THREAD, "reaper thread was started successfully");

	/* Starting the general fridge */
	rc = general_fridge_init();
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create general fridge, error = %d (%s)",
			 errno, strerror(errno));
	}
	LogEvent(COMPONENT_THREAD, "General fridge was started successfully");

	pthread_attr_destroy(&attr_thr);
}

void nfs_audit_log_cb(auditlog_cb_params *cb_params,const char *log_info) {
	if(log_info){
		LogEvent(COMPONENT_INIT,"%s", log_info);
		//free(log_info);
		//log_info = NULL;
	}else{
		LogEvent(COMPONENT_INIT,"nfs_audit_log_cb return log_info = NULL.");
	}
	
}

/**
 * @brief Init the nfs daemon
 *
 * @param[in] p_start_info Unused
 */

static void nfs_Init(const nfs_start_info_t *p_start_info)
{
#ifdef _HAVE_GSSAPI
	gss_buffer_desc gss_service_buf;
	OM_uint32 maj_stat, min_stat;
	char GssError[MAXNAMLEN + 1];
#endif

#ifdef USE_DBUS
	/* DBUS init */
	gsh_dbus_pkginit();
	dbus_export_init();
	dbus_client_init();
	dbus_cache_init();
#endif

	const char *audit_protocol = "NFS";
	idfs_auditlog_init(audit_protocol);
	struct auditlog_cb_params auditlog_params;
	idfs_auditlog_register_cb(nfs_audit_log_cb, auditlog_params);

	/* acls cache may be needed by exports_pkginit */
	LogDebug(COMPONENT_INIT, "Now building NFSv4 ACL cache");
	if (nfs4_acls_init() != 0)
		LogFatal(COMPONENT_INIT, "Error while initializing NFSv4 ACLs");
	LogInfo(COMPONENT_INIT, "NFSv4 ACL cache successfully initialized");

	/* finish the job with exports by caching the root entries
	 */
	exports_pkginit();

	nfs41_session_pool =
	    pool_basic_init("NFSv4.1 session pool", sizeof(nfs41_session_t));

	/* If rpcsec_gss is used, set the path to the keytab */
#ifdef _HAVE_GSSAPI
#ifdef HAVE_KRB5
	if (nfs_param.krb5_param.active_krb5) {
		OM_uint32 gss_status = GSS_S_COMPLETE;

		if (strcmp(nfs_param.krb5_param.keytab, DEFAULT_NFS_KEYTAB))
			gss_status = krb5_gss_register_acceptor_identity(
						nfs_param.krb5_param.keytab);

		if (gss_status != GSS_S_COMPLETE) {
			log_sperror_gss(GssError, gss_status, 0);
			LogFatal(COMPONENT_INIT,
				 "Error setting krb5 keytab to value %s is %s",
				 nfs_param.krb5_param.keytab, GssError);
		}
		LogInfo(COMPONENT_INIT,
			"krb5 keytab path successfully set to %s",
			nfs_param.krb5_param.keytab);
#endif				/* HAVE_KRB5 */

		/* Set up principal to be use for GSSAPPI within GSSRPC/KRB5 */
		gss_service_buf.value = nfs_param.krb5_param.svc.principal;
		gss_service_buf.length =
			strlen(nfs_param.krb5_param.svc.principal) + 1;
		/* The '+1' is not to be forgotten, for the '\0' at the end */

		maj_stat = gss_import_name(&min_stat, &gss_service_buf,
					   (gss_OID) GSS_C_NT_HOSTBASED_SERVICE,
					   &nfs_param.krb5_param.svc.gss_name);
		if (maj_stat != GSS_S_COMPLETE) {
			log_sperror_gss(GssError, maj_stat, min_stat);
			LogFatal(COMPONENT_INIT,
				 "Error importing gss principal %s is %s",
				 nfs_param.krb5_param.svc.principal, GssError);
		}

		if (nfs_param.krb5_param.svc.gss_name == GSS_C_NO_NAME)
			LogInfo(COMPONENT_INIT,
				"Regression:  svc.gss_name == GSS_C_NO_NAME");

		LogInfo(COMPONENT_INIT, "gss principal \"%s\" successfully set",
			nfs_param.krb5_param.svc.principal);

		/* Set the principal to GSSRPC */
		if (!svcauth_gss_set_svc_name
		    (nfs_param.krb5_param.svc.gss_name)) {
			LogFatal(COMPONENT_INIT,
				 "Impossible to set gss principal to GSSRPC");
		}

		/* Don't release name until shutdown, it will be used by the
		 * backchannel. */

#ifdef HAVE_KRB5
	}			/*  if( nfs_param.krb5_param.active_krb5 ) */
#endif				/* HAVE_KRB5 */
#endif				/* _HAVE_GSSAPI */
	/* Init the NFSv4 Clientid cache */
	LogDebug(COMPONENT_INIT, "Now building NFSv4 clientid cache");
	if (nfs_Init_client_id() !=
	    CLIENT_ID_SUCCESS) {
		LogFatal(COMPONENT_INIT,
			 "Error while initializing NFSv4 clientid cache");
	}
	LogInfo(COMPONENT_INIT,
		"NFSv4 clientid cache successfully initialized");

	/* Init duplicate request cache */
	dupreq2_pkginit();
	LogInfo(COMPONENT_INIT,
		"duplicate request hash table cache successfully initialized");

	/* Init The NFSv4 State id cache */
	LogDebug(COMPONENT_INIT, "Now building NFSv4 State Id cache");
	if (nfs4_Init_state_id() != 0) {
		LogFatal(COMPONENT_INIT,
			 "Error while initializing NFSv4 State Id cache");
	}
	LogInfo(COMPONENT_INIT,
		"NFSv4 State Id cache successfully initialized");

	/* Init The NFSv4 Open Owner cache */
	LogDebug(COMPONENT_INIT, "Now building NFSv4 Owner cache");
	if (Init_nfs4_owner() != 0) {
		LogFatal(COMPONENT_INIT,
			 "Error while initializing NFSv4 Owner cache");
	}
	LogInfo(COMPONENT_INIT,
		"NFSv4 Open Owner cache successfully initialized");

#ifdef _USE_NLM
	if (nfs_param.core_param.enable_NLM) {
		/* Init The NLM Owner cache */
		LogDebug(COMPONENT_INIT, "Now building NLM Owner cache");
		if (Init_nlm_hash() != 0) {
			LogFatal(COMPONENT_INIT,
				 "Error while initializing NLM Owner cache");
		}
		LogInfo(COMPONENT_INIT,
			"NLM Owner cache successfully initialized");
		/* Init The NLM Owner cache */
		LogDebug(COMPONENT_INIT, "Now building NLM State cache");
		if (Init_nlm_state_hash() != 0) {
			LogFatal(COMPONENT_INIT,
				 "Error while initializing NLM State cache");
		}
		LogInfo(COMPONENT_INIT,
			"NLM State cache successfully initialized");
		nlm_init();
	}
#endif /* _USE_NLM */
#ifdef _USE_9P
	/* Init the 9P lock owner cache */
	LogDebug(COMPONENT_INIT, "Now building 9P Owner cache");
	if (Init_9p_hash() != 0) {
		LogFatal(COMPONENT_INIT,
			 "Error while initializing 9P Owner cache");
	}
	LogInfo(COMPONENT_INIT, "9P Owner cache successfully initialized");
#endif

	LogDebug(COMPONENT_INIT, "Now building NFSv4 Session Id cache");
	if (nfs41_Init_session_id() != 0) {
		LogFatal(COMPONENT_INIT,
			 "Error while initializing NFSv4 Session Id cache");
	}
	LogInfo(COMPONENT_INIT,
		"NFSv4 Session Id cache successfully initialized");

#ifdef _USE_9P
	LogDebug(COMPONENT_INIT, "Now building 9P resources");
	if (_9p_init()) {
		LogCrit(COMPONENT_INIT,
			"Error while initializing 9P Resources");
		exit(1);
	}
	LogInfo(COMPONENT_INIT, "9P resources successfully initialized");
#endif				/* _USE_9P */

	/* Creates the pseudo fs */
	LogDebug(COMPONENT_INIT, "Now building pseudo fs");

	create_pseudofs();

	LogInfo(COMPONENT_INIT,
		"NFSv4 pseudo file system successfully initialized");

	/* Save Gnfs thread credentials with Frank's routine for later use */
	fsal_save_gnfs_credentials();

	/* RPC Initialisation - exits on failure */
	nfs_Init_svc();
	LogInfo(COMPONENT_INIT, "RPC resources successfully initialized");

	/* Admin initialisation */
	nfs_Init_admin_thread();

	/* callback dispatch */
	nfs_rpc_cb_pkginit();
#ifdef _USE_CB_SIMULATOR
	nfs_rpc_cbsim_pkginit();
#endif				/*  _USE_CB_SIMULATOR */

}				/* nfs_Init */

#ifdef USE_CAPS
/**
 * @brief Lower my capabilities (privs) so quotas work right
 *
 * This will/should be moved to set_credentials where it belongs
 * Deal with capabilities in order to remove CAP_SYS_RESOURCE (needed
 * for proper management of data quotas)
 */

static void lower_my_caps(void)
{
	cap_value_t cap_values[] = {CAP_SYS_RESOURCE};
	cap_t cap;
	char *cap_text;
	ssize_t capstrlen = 0;
	int ret;

	if (!nfs_start_info.drop_caps) {
		/* Skip dropping caps by request */
		return;
	}

	cap = cap_get_proc();
	if (cap == NULL) {
		LogFatal(COMPONENT_INIT,
			 "cap_get_proc() failed, %s", strerror(errno));
	}

	ret = cap_set_flag(cap, CAP_EFFECTIVE,
			   sizeof(cap_values) / sizeof(cap_values[0]),
			   cap_values, CAP_CLEAR);
	if (ret != 0) {
		LogFatal(COMPONENT_INIT,
			 "cap_set_flag() failed, %s", strerror(errno));
	}

	ret = cap_set_flag(cap, CAP_PERMITTED,
			   sizeof(cap_values) / sizeof(cap_values[0]),
			   cap_values, CAP_CLEAR);
	if (ret != 0) {
		LogFatal(COMPONENT_INIT,
			 "cap_set_flag() failed, %s", strerror(errno));
	}

	ret = cap_set_flag(cap, CAP_INHERITABLE,
			   sizeof(cap_values) / sizeof(cap_values[0]),
			   cap_values, CAP_CLEAR);
	if (ret != 0) {
		LogFatal(COMPONENT_INIT,
			 "cap_set_flag() failed, %s", strerror(errno));
	}

	ret = cap_set_proc(cap);
	if (ret != 0) {
		LogFatal(COMPONENT_INIT,
			 "Failed to set capabilities for process, %s",
			 strerror(errno));
	}

	LogEvent(COMPONENT_INIT,
		 "CAP_SYS_RESOURCE was successfully removed for proper quota management in FSAL");

	/* Print newly set capabilities (same as what CLI "getpcaps" displays */
	cap_text = cap_to_text(cap, &capstrlen);
	LogEvent(COMPONENT_INIT, "currenty set capabilities are: %s",
		 cap_text);
	cap_free(cap_text);
	cap_free(cap);
}
#endif


/**
 * @brief Start NFS service
 *
 * @param[in] p_start_info Startup parameters
 */
void nfs_start(nfs_start_info_t *p_start_info)
{
	/* store the start info so it is available for all layers */
	nfs_start_info = *p_start_info;

	if (p_start_info->dump_default_config == true) {
		nfs_print_param_config();
		exit(0);
	}
	/* memery pool initialize */
	if (nfs_param.core_param.enable_MEMPOOL == true) {
		if (mem_pool_init(nfs_param.core_param.mempool_SIZE) != 0){
			LogWarn(COMPONENT_INIT,
				"Unable to initialize mempool.\n");
			nfs_param.core_param.enable_MEMPOOL = false;
		}
	}
	/* Make sure Gnfs runs with a 0000 umask. */
	umask(0000);

	{
		/* Set the write verifiers */
		union {
			verifier4 NFS4_write_verifier;
			writeverf3 NFS3_write_verifier;
			uint64_t epoch;
		} build_verifier;

		build_verifier.epoch = (uint64_t) nfs_ServerEpoch;

		memcpy(NFS3_write_verifier, build_verifier.NFS3_write_verifier,
		       sizeof(NFS3_write_verifier));
		memcpy(NFS4_write_verifier, build_verifier.NFS4_write_verifier,
		       sizeof(NFS4_write_verifier));
	}

#ifdef USE_CAPS
	lower_my_caps();
#endif
	int ret = etcdwatchdatabase();
	if (ret != 0){
		LogEvent(COMPONENT_INIT, "etcdwatchdatabase failed!, ret = %d", ret);
		destroy_fsals();
		LogEvent(COMPONENT_MAIN, "Destroying the FSAL system over.");
		exit(0);
	}

	if (load_all_ns_audit_from_db() != 0) {
		LogCrit(COMPONENT_INIT,
			 "Error setting ns_audit from database file.");
	}
	
	/* Initialize all layers and service threads */
	nfs_Init(p_start_info);
	nfs_Start_threads(); /* Spawns service threads */

	nfs_init_complete();

#ifdef _USE_NLM
	if (nfs_param.core_param.enable_NLM) {
		/* NSM Unmonitor all */
		nsm_unmonitor_all();
	}
#endif /* _USE_NLM */
	LogEvent(COMPONENT_INIT, "nfs_start_threads success, epoch is %lu", (uint64_t)nfs_ServerEpoch);
	LogEvent(COMPONENT_INIT,
		 "-------------------------------------------------");
	LogEvent(COMPONENT_INIT, "             NFS SERVER INITIALIZED");
	LogEvent(COMPONENT_INIT,
		 "-------------------------------------------------");

	/* Set the time of NFS stat counting */
	nfs_init_stats_time();

	/* Wait for dispatcher to exit */
	LogDebug(COMPONENT_THREAD, "Wait for admin thread to exit");
	pthread_join(admin_thrid, NULL);

	/* Regular exit */
	LogEvent(COMPONENT_MAIN, "NFS EXIT: regular exit");
	/* memery pool finalize */
	if (nfs_param.core_param.enable_MEMPOOL == true) {
		mem_pool_finalize();
	}

	Cleanup();
	/* let main return 0 to exit */
	LogEvent(COMPONENT_MAIN, "NFS EXIT: regular exit Cleanup over");
}

void nfs_init_init(void)
{
	PTHREAD_MUTEX_init(&nfs_init.init_mutex, NULL);
	PTHREAD_COND_init(&nfs_init.init_cond, NULL);
	nfs_init.init_complete = false;
}

void nfs_init_complete(void)
{
	PTHREAD_MUTEX_lock(&nfs_init.init_mutex);
	nfs_init.init_complete = true;
	pthread_cond_broadcast(&nfs_init.init_cond);
	PTHREAD_MUTEX_unlock(&nfs_init.init_mutex);
}

void nfs_init_wait(void)
{
	PTHREAD_MUTEX_lock(&nfs_init.init_mutex);
	while (!nfs_init.init_complete) {
		pthread_cond_wait(&nfs_init.init_cond, &nfs_init.init_mutex);
	}
	PTHREAD_MUTEX_unlock(&nfs_init.init_mutex);
}

int nfs_init_wait_timeout(int timeout)
{
	int rc = 0;

	PTHREAD_MUTEX_lock(&nfs_init.init_mutex);
	if (!nfs_init.init_complete) {
		struct timespec ts;

		ts.tv_sec = time(NULL) + timeout;
		ts.tv_nsec = 0;
		rc = pthread_cond_timedwait(&nfs_init.init_cond,
					    &nfs_init.init_mutex, &ts);
	}
	PTHREAD_MUTEX_unlock(&nfs_init.init_mutex);

	return rc;
}

bool nfs_health(void)
{
	uint64_t newenq, newdeq;
	uint64_t dequeue_diff, enqueue_diff;
	bool healthy;

	newenq = nfs_health_.enqueued_reqs;
	newdeq = nfs_health_.dequeued_reqs;
	enqueue_diff = newenq - healthstats.enqueued_reqs;
	dequeue_diff = newdeq - healthstats.dequeued_reqs;

	/* Consider healthy and making progress if we have dequeued some
	 * requests or there is one or less to dequeue.  Don't check
	 * enqueue_diff == 0 here, as there will be suprious warnings during
	 * times of low traffic, when an enqueue happens to coincide with the
	 * heartbeat firing.
	 */
	healthy = dequeue_diff > 0 || enqueue_diff <= 1;

	if (!healthy) {
		LogWarn(COMPONENT_DBUS,
			"Health status is unhealthy. "
			"enq new: %" PRIu64 ", old: %" PRIu64 "; "
			"deq new: %" PRIu64 ", old: %" PRIu64,
			newenq, healthstats.enqueued_reqs,
			newdeq, healthstats.dequeued_reqs);
	}

	healthstats.enqueued_reqs = newenq;
	healthstats.dequeued_reqs = newdeq;

	return healthy;
}

static int parse_etcd_val(const char *val, 
                          char *op_buf, size_t op_bufsz,
                          char *tenant_buf, size_t tenant_bufsz,
                          char *ns_buf, size_t ns_bufsz)
{
    if (!val || !op_buf || !tenant_buf || !ns_buf)
        return -1;

    char tmp[512] = {0};
    strncpy(tmp, val, sizeof(tmp)-1);

    // 1) split operation
    char *p = strchr(tmp, ';');
    if (!p) {
        return -1;
    }
    *p = '\0';
    strncpy(op_buf, tmp, op_bufsz - 1);

    // 2) rest -> tenant, ns(optional)
    char *rest = p + 1;
    // find the second ',' 
    char *comma = strchr(rest, ',');
    if (comma) {
        // "tenantA,namespaceX"
        *comma = '\0';
        // rest -> tenant
        strncpy(tenant_buf, rest, tenant_bufsz - 1);
        // comma+1 -> namespace
        strncpy(ns_buf, comma + 1, ns_bufsz - 1);
    } else {
        // only tenant
        strncpy(tenant_buf, rest, tenant_bufsz - 1);
    }
    return 0;
}

void tenant_audit_changed(const char *key, const char *val)
{
	sleep(1);
	LogDebug(COMPONENT_INIT, "etcd key=%s changed, new value=%s", key, val);

	char op[ETCD_VAL_OP_MAX] = {0}, tenant[TENANT_NAME_MAX] = {0}, ns[NAMESPACE_NAME_MAX] = {0};
	if (parse_etcd_val(val, op, sizeof(op), tenant, sizeof(tenant), ns, sizeof(ns)) != 0) {
		LogCrit(COMPONENT_INIT, "Parse etcd val failed: %s", val);
		return;
	}

	LogDebug(COMPONENT_INIT, "Parsed op=%s tenant=%s", op, tenant);
	//reload param
	reload_tenant_info(op, tenant);
	
	return;
}

void ns_audit_changed(const char *key, const char *val)
{
	sleep(1);
	LogDebug(COMPONENT_INIT, "etcd key=%s changed, new value=%s", key, val);

	char op[ETCD_VAL_OP_MAX] = {0}, tenant[TENANT_NAME_MAX] = {0}, ns[NAMESPACE_NAME_MAX] = {0};
	if (parse_etcd_val(val, op, sizeof(op), tenant, sizeof(tenant), ns, sizeof(ns)) != 0) {
		LogCrit(COMPONENT_INIT, "Parse etcd val failed: %s", val);
		return;
	}

	LogDebug(COMPONENT_INIT, "Parsed op=%s tenant=%s", op, tenant);
	//reload param
	reload_ns_info(op, tenant, ns);
	
	return;
}

int etcdwatchdatabase(){
	int ret = init_database_connection();
	if (ret != 0){
		LogCrit(COMPONENT_INIT, "database connection failed, ret=%d", ret);
		return -1;
	}

	// 1. 初始化 etcd watcher
	ret = etcdwatcher_init_c();
	if (ret != 0) {
		LogCrit(COMPONENT_INIT, "etcdwatcher_init failed, ret=%d", ret);
		return -1;
	}

	// 2. 监听 key="/database/table/sub_network"
	ret = etcdwatcher_start_watch_c("/database/table/tenant_audit_info", tenant_audit_changed);
	if (ret != 0) {
		LogCrit(COMPONENT_INIT, "watch sub_network failed, ret=%d", ret);
		return -2;
	}

	// 3. 监听 key="/database/table/access_zone"
	ret = etcdwatcher_start_watch_c("/database/table/ns_audit_info", ns_audit_changed);
	if (ret != 0) {
		LogCrit(COMPONENT_INIT, "watch access_zone failed, ret=%d", ret);
		return -3;
	}

	// 4. 监听 key="/database/master_ip"
        ret = etcdwatcher_start_watch_c("/database/master_ip", database_masterip_changed);
        if (ret != 0) {
                LogCrit(COMPONENT_INIT, "watch master_ip failed, ret=%d", ret);
                return -4;
        }

	LogEvent(COMPONENT_INIT, "etcdwatchdatabase successfully.");
	return 0;
}

int etcd_destroy(){
	close_database_connection();
	etcdwatcher_destroy_c();
	return 0;
}

