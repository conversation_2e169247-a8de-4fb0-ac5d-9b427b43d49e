/**********************************
 * <AUTHOR>
 * License:     MIT
 *
 **********************************/
#ifndef _THPOOL_
#define _THPOOL_

#ifdef __cplusplus
extern "C" {
#endif

//Hygon C86
#define CPU_NUM 128
extern int cq_cpu;
extern int worker_cpus[CPU_NUM];
extern int worker_cpu_num;
extern int cur_cpu;
extern int binding;
extern int numa_node;
extern int thread_cpus[CPU_NUM];
/* =================================== API ======================================= */
typedef struct thpool_* threadpool;


/* ========================== STRUCTURES ============================ */


/* Binary semaphore */
typedef struct bsem {
	pthread_mutex_t mutex;
	pthread_cond_t   cond;
	int v;
} bsem;


/* Job */
typedef struct job {
	struct job*  prev;                   /* pointer to previous job   */
	void   (*function)(void* arg);       /* function pointer          */
	void*  arg;                          /* function's argument       */
} job;


/* Job queue */
typedef struct jobqueue{
	pthread_spinlock_t rwlock;             /* used for queue r/w access */
	job  *front;                         /* pointer to front of queue */
	job  *rear;                          /* pointer to rear  of queue */
	//bsem *has_jobs;                      /* flag as binary semaphore  */
	int   len;                           /* number of jobs in queue   */
} jobqueue;

/* queue_ctrl_t */
typedef uint32_t queuesize_t;

typedef struct {
	//volatile uint8_t state;

	volatile queuesize_t end;
	volatile queuesize_t head;

	queuesize_t size;

	job **jobs;
} queue_ctrl_t;

#define THR_NAME_LEN 19
/* Thread */
typedef struct thread{
	int       id;                        /* friendly id               */
	pthread_t pthread;                   /* pointer to actual thread  */
	struct thpool_* thpool_p;            /* access to thpool          */
	char thr_name[THR_NAME_LEN + 1];
	bool binding;
	bool threads_keepalive;
	bool no_lock;
} thread;

#define CALL_THEAD_INDEXMAX 200
/* Threadpool */
typedef struct thpool_{
	int num_threads;
	thread**   threads;                  /* pointer to threads        */
	queue_ctrl_t **jobqueues;
	jobqueue *reply_jobqueues;
	jobqueue *recv_jobqueues;
	volatile int num_threads_alive;      /* threads currently alive   */
	volatile int num_threads_working;    /* threads currently working */
	pthread_mutex_t  thcount_lock;       /* used for thread count etc */
	pthread_cond_t  threads_all_idle;    /* signal to thpool_wait     */
	jobqueue  jobqueue;                  /* job queue                 */
	int last_job;
	int last_reply_job;
	bool binding;
	bool need_sleep;
	bool threads_keepalive;
	bool no_lock;
	queue_ctrl_t **jobqueues_nolock[CALL_THEAD_INDEXMAX];
	int last_job_nolock[CALL_THEAD_INDEXMAX];
	int producer_thr_max;
	int self_thr_index_min;
	int self_thr_index_max;
	int producer_thr_index_min;
	int producer_thr_index_max;
	uint32_t sleep_time;
} thpool_;

typedef struct th_curr_ {
	int pthread_index;
	char thr_name[THR_NAME_LEN + 1];
} th_curr;


/**
 * @brief  Initialize threadpool
 *
 * Initializes a threadpool. This function will not return until all
 * threads have initialized successfully.
 *
 * @example
 *
 *    ..
 *    threadpool thpool;                     //First we declare a threadpool
 *    thpool = thpool_init(4);               //then we initialize it to 4 threads
 *    ..
 *
 * @param  num_threads   number of threads to be created in the threadpool
 * @return threadpool    created threadpool on success,
 *                       NULL on error
 */
threadpool thpool_init(const char *thread_name, int num_threads);
threadpool thpool_init_nolock(const char *thread_name, int num_threads);


/**
 * @brief Add work to the job queue
 *
 * Takes an action and its argument and adds it to the threadpool's job queue.
 * If you want to add to work a function with more than one arguments then
 * a way to implement this is by passing a pointer to a structure.
 *
 * NOTICE: You have to cast both the function and argument to not get warnings.
 *
 * @example
 *
 *    void print_num(int num){
 *       printf("%d\n", num);
 *    }
 *
 *    int main() {
 *       ..
 *       int a = 10;
 *       thpool_add_work(thpool, (void*)print_num, (void*)a);
 *       ..
 *    }
 *
 * @param  threadpool    threadpool to which the work will be added
 * @param  function_p    pointer to function to add as work
 * @param  arg_p         pointer to an argument
 * @return 0 on success, -1 otherwise.
 */
int thpool_add_job(threadpool, void (*function_p)(void*), void* arg_p);
int thpool_add_job_nolock(threadpool, void (*function_p)(void*), void* arg_p);
int thpool_add_job_nolock_withthrid(threadpool, void (*function_p)(void*), void* arg_p, int destid);
int thpool_add_reply_job(threadpool, void (*function_p)(void*), void* arg_p);
bool thread_queue_pop_nolock(threadpool, int thrid);
extern int cur_eps_count;

/**
 * @brief Wait for all queued jobs to finish
 *
 * Will wait for all jobs - both queued and currently running to finish.
 * Once the queue is empty and all work has completed, the calling thread
 * (probably the main program) will continue.
 *
 * Smart polling is used in wait. The polling is initially 0 - meaning that
 * there is virtually no polling at all. If after 1 seconds the threads
 * haven't finished, the polling interval starts growing exponentially
 * until it reaches max_secs seconds. Then it jumps down to a maximum polling
 * interval assuming that heavy processing is being used in the threadpool.
 *
 * @example
 *
 *    ..
 *    threadpool thpool = thpool_init(4);
 *    ..
 *    // Add a bunch of work
 *    ..
 *    thpool_wait(thpool);
 *    puts("All added work has finished");
 *    ..
 *
 * @param threadpool     the threadpool to wait for
 * @return nothing
 */
void thpool_wait(threadpool);


/**
 * @brief Pauses all threads immediately
 *
 * The threads will be paused no matter if they are idle or working.
 * The threads return to their previous states once thpool_resume
 * is called.
 *
 * While the thread is being paused, new work can be added.
 *
 * @example
 *
 *    threadpool thpool = thpool_init(4);
 *    thpool_pause(thpool);
 *    ..
 *    // Add a bunch of work
 *    ..
 *    thpool_resume(thpool); // Let the threads start their magic
 *
 * @param threadpool    the threadpool where the threads should be paused
 * @return nothing
 */
void thpool_pause(threadpool);


/**
 * @brief Unpauses all threads if they are paused
 *
 * @example
 *    ..
 *    thpool_pause(thpool);
 *    sleep(10);              // Delay execution 10 seconds
 *    thpool_resume(thpool);
 *    ..
 *
 * @param threadpool     the threadpool where the threads should be unpaused
 * @return nothing
 */
void thpool_resume(threadpool);


/**
 * @brief Destroy the threadpool
 *
 * This will wait for the currently active threads to finish and then 'kill'
 * the whole threadpool to free up memory.
 *
 * @example
 * int main() {
 *    threadpool thpool1 = thpool_init(2);
 *    threadpool thpool2 = thpool_init(2);
 *    ..
 *    thpool_destroy(thpool1);
 *    ..
 *    return 0;
 * }
 *
 * @param threadpool     the threadpool to destroy
 * @return nothing
 */
void thpool_destroy(threadpool);
void qos_thread_do(void *arg);

/**
 * @brief Show currently working threads
 *
 * Working threads are the threads that are performing work (not idle).
 *
 * @example
 * int main() {
 *    threadpool thpool1 = thpool_init(2);
 *    threadpool thpool2 = thpool_init(2);
 *    ..
 *    printf("Working threads: %d\n", thpool_num_threads_working(thpool1));
 *    ..
 *    return 0;
 * }
 *
 * @param threadpool     the threadpool of interest
 * @return integer       number of threads working
 */
int thpool_num_threads_working(threadpool);
int thpool_get_curthreadid();


#ifdef __cplusplus
}
#endif

#endif
