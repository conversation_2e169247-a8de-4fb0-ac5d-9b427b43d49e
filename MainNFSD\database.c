#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include "log.h"
#include "database.h"
#include "string.h"
#include <sys/wait.h>
#include "namespace_mgr.h"
#include <netinet/in.h>
#include <time.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/time.h>
#include <errno.h>
#include "etcdwatcher_c_wrapper.h"
#include <stdatomic.h>
#include <semaphore.h>
#include <string.h>

#define		GET_DB_INFO		"/usr/local/ism/db/get_database_info.sh"
#define		TIME_OUT	10
#define OPEN_MAX_GUESS 1024
#define SQL_BUF_LEN 1024

static long openmax = 0;
static pid_t *childpid = NULL;
static int maxfd;

static struct db_connection db_mem = {0};
static struct db_connection *db = &db_mem;
pthread_mutex_t db_mutex = PTHREAD_MUTEX_INITIALIZER;

static sem_t  sem_has_task;     /* 主线程 post，工作线程 wait */

static pthread_t tid;
static bool thread_ok = false;
static bool reconnect_database_falg = false;

typedef enum { S_IDLE = 0, S_RUNNING, S_DONE, S_FAIL } st_t;
// 查询线程的参数结构
typedef struct {
	char sql[SQL_BUF_LEN];
	dbi_result *result;
	int state;      /* st_t */
} task_t;
static task_t gTask = { .state = S_IDLE };

/**
 * @brief 数据库表变动时调的回调函数，用于更新配置文件
 */
void database_masterip_changed(const char *key, const char *val)
{
	LogWarn(COMPONENT_DATABASE, "etcd key=%s changed, new value=%s\n", key, val);
	if (strcmp(val, "") == 0)
		return;
	if (strcmp(db->host, val) == 0) {
		LogWarn(COMPONENT_DATABASE, "the database master ip don`t change");
		return;
	}
	reconnect_database_falg = true;
	memset(db->host, 0, sizeof(db->host));
	strncpy(db->host, val, sizeof(db->host)-1);
	return;
}

// 查询线程函数
static void* query_thread_func(void *arg) {
	(void)arg;
	while (1) { 
		sem_wait(&sem_has_task);
		
		// 执行查询
		*(gTask.result) = dbi_conn_query(db->conn, gTask.sql);
		if (*(gTask.result)) {
			gTask.state = S_DONE;
		} else { 
			gTask.state = S_FAIL;
		}
	}
	return NULL;
}
/* 启动 / 停止线程 */
static void start_thread(void)
{
    sem_init(&sem_has_task, 0, 0);
    if (pthread_create(&tid, NULL, query_thread_func, NULL) != 0) {
        LogCrit(COMPONENT_DATABASE, "create query_thread err");
        exit(EXIT_FAILURE);
    }
    thread_ok = true;
	LogWarn(COMPONENT_DATABASE, "create query_thread successfuly");
}
static void stop_thread(void)
{
    if (!thread_ok) return;
	pthread_cancel(tid);
	pthread_join(tid, NULL);
    sem_destroy(&sem_has_task);
    thread_ok = false;
	LogWarn(COMPONENT_DATABASE, "stop query_thread successfuly");
}


long open_max(void)
{
	if (openmax == 0) {
		errno = 0;
		if ((openmax = sysconf(_SC_OPEN_MAX)) < 0) {
			if (errno == 0) 
				openmax = OPEN_MAX_GUESS;
			else 
				LogEvent(COMPONENT_FSAL, "IMODE sysconf error for _SC_OPEN_MAX, errno(%d)", errno);
		}
	}
	return(openmax);
}

void init_vpopen(){

	maxfd = open_max();
	if ((childpid = (pid_t *)calloc(maxfd, sizeof(pid_t))) == NULL ) {
		LogEvent(COMPONENT_FSAL, "IMODE init vopen failed, errno(%d)", errno);
	} else {
		LogFullDebug(COMPONENT_FSAL, "IMODE init vopen success, errno(%d)", errno);
	}
	return ;
}
void fini_vpopen(){
	if (childpid != NULL) {
		free(childpid);
	}
}
FILE *vpopen(const char* cmdstring, const char *type) {
	int fd,pfd[2];
	FILE *fp;
	pid_t pid;
	if(((type[0] != 'r') && (type[0] != 'w')) || (type[1] != 0)) {
		errno = EINVAL;
		return NULL;
	}

	if (pipe(pfd) != 0) {
		return NULL;
	}
	if ((pid = vfork()) < 0){
		return NULL;
	} else if (pid == 0){
		if (*type == 'r') {
			close(pfd[0]);
			if (pfd[1] != STDOUT_FILENO) {
				dup2(pfd[1], STDOUT_FILENO);
				close(pfd[1]);
			}
		} else {
			close(pfd[1]);
			if (pfd[0] != STDIN_FILENO) {
				dup2(pfd[0],STDIN_FILENO);
				close(pfd[0]);
			}
		}
		/*close all descriptors in childpid */
		for (int i = 0; i < maxfd; i++) {
			if (childpid[i] > 0) {
				close(childpid[i]);
			}
		}
		execl("/bin/sh", "sh", "-c", cmdstring, (char *) 0);
		_exit(127);
	}
	if (*type == 'r') {
		close(pfd[1]);
		if ((fp = fdopen(pfd[0], type)) == NULL)
			return NULL;
	} else {
		close(pfd[0]);
		if ((fp = fdopen(pfd[1], type)) == NULL)
			return NULL;
	}
	fd = fileno(fp);
	childpid[fd] = pid;
	LogFullDebug(COMPONENT_FSAL, "IMODE call python pid%p[%d]:%d", childpid, fd, pid);
	return(fp);	
}
int vpclose(FILE *fp)
{
	int fd,stat;
	pid_t pid;

	if (childpid == NULL) {
		return -1;
	}
	fd = fileno(fp);
	if ((pid = childpid[fd]) == 0){
		return -1;
	}
	childpid[fd] = 0;
	if (fclose(fp) == EOF)
		return -1;
	while(waitpid(pid, &stat,0) < 0) {	
		if (errno != EINTR) {
			return -1;
		}
	}
	LogFullDebug(COMPONENT_FSAL, "IMODE call python pid %p[%d]:%d", childpid, fd, pid);
	return stat;
}

static void set_common_pg_options() {
	dbi_conn_set_option(db->conn, "host", db->host);
	dbi_conn_set_option(db->conn, "username", db->username);
	dbi_conn_set_option(db->conn, "password", db->password);
	dbi_conn_set_option(db->conn, "dbname", db->dbname);
	dbi_conn_set_option(db->conn, "port", db->port);

	// 保活参数
	dbi_conn_set_option(db->conn, "connect_timeout", "10");           // 连接超时时间，单位秒
	dbi_conn_set_option(db->conn, "statement_timeout", "10000");      // 查询超时时间，单位毫秒
	dbi_conn_set_option(db->conn, "keepalives", "1");                 // 启用保活机制
	dbi_conn_set_option(db->conn, "tcp_keepalives_idle", "10");       // 空闲10s后发送keepalive
	dbi_conn_set_option(db->conn, "tcp_keepalives_interval", "5");    // 每5s发送一次
	dbi_conn_set_option(db->conn, "tcp_keepalives_count", "3");       // 尝试3次

	// 添加数据同步重试选项，以更好地处理I/O错误
	dbi_conn_set_option(db->conn, "data_sync_retry", "on");
}

int get_host(char *host, size_t host_size) {
	FILE *fp;
	char output[512];

	// 1. 获取etcd endpoints
	LogWarn(COMPONENT_DATABASE, "start idfs config-key get config-client/etcd/endpoints");
	fp = vpopen("idfs config-key get config-client/etcd/endpoints", "r");
		if (fp == NULL) {
		LogCrit(COMPONENT_DATABASE, "idfs config-key get config-client/etcd/endpoints execute failed");
		return 1;
	}
	LogWarn(COMPONENT_DATABASE, "end idfs config-key get config-client/etcd/endpoints");
		
	char endpoints[1024];
	if (fgets(endpoints, sizeof(endpoints), fp) == NULL) {
		vpclose(fp);
		LogCrit(COMPONENT_DATABASE, "Failed to read endpoints\n");
		return 1;
	}
	vpclose(fp);

	// 移除换行符
	endpoints[strcspn(endpoints, "\n")] = '\0';
	//printf("endpoints=%s\n", endpoints);

	// 2. 查询master_ip
	char cmd[2048];
	snprintf(cmd, sizeof(cmd),
		"etcdctl get /database/master_ip --endpoints=http://%s", endpoints);

	//printf("cmd=%s\n", cmd);
	LogWarn(COMPONENT_DATABASE, "start etcdctl get /database/master_ip");
	fp = vpopen(cmd, "r");
	if (fp == NULL) {
		LogCrit(COMPONENT_DATABASE, "etcdctl get /database/master_ip --endpoints=http://%s failed", endpoints);
		return 1;
	}
	LogWarn(COMPONENT_DATABASE, "end etcdctl get /database/master_ip");

	// 跳过第一行
	if (fgets(output, sizeof(output), fp) == NULL) {
		vpclose(fp);
		LogCrit(COMPONENT_DATABASE, "No output from etcdctl\n");
		return 1;
	}
	//printf("output=%s\n", output);

	// 获取第二行(master_ip)
	if (fgets(output, sizeof(output), fp) == NULL) {
		vpclose(fp);
		LogCrit(COMPONENT_DATABASE, "No master IP found\n");
		return 1;
	}
	//printf("output=%s\n", output);

	// 移除换行符
	output[strcspn(output, "\n")] = '\0';
	//printf("Final IP: %s\n", output);
	LogDebug(COMPONENT_DATABASE, "Find the master ip %s", output);
	memset(host, 0, host_size);
	strncpy(host, output, host_size-1);
	vpclose(fp);
	return 0;
}


int get_shell_output(const char* cmd, char *output, size_t output_size) {
	FILE *fp = vpopen(cmd,"r");
	if (NULL == fp)
	{
		LogCrit(COMPONENT_DATABASE, "%s execute failed,%s--", cmd, strerror(errno));
		return -1;
	}

	if (fgets(output, output_size, fp) == NULL)
	{
		LogCrit(COMPONENT_DATABASE, "%s fgets failed,output:%s--", cmd, output);
		vpclose(fp);
		return -1;
	}

	//LogCrit(COMPONENT_DATABASE, "output:%s--", output);
	
	vpclose(fp);
	return 0;
}

int get_db_user_info() {
	const char* sql_get_user = GET_DB_INFO;
	char script_output[1024];
	int r;

	r = get_shell_output(sql_get_user, script_output, sizeof(script_output));
	if (r == 0){
		char *username = strstr(script_output, "\"username\":");
		char *password = strstr(script_output, "\"password\":");
		char *dbname = strstr(script_output, "\"dbname\":");
		char *port = strstr(script_output, "\"port\":");

		if (username) {
			sscanf(username, "\"username\": \"%99[^\"]\"", db->username);
		} else {
			strncpy(db->username, "icfs", sizeof(db->username)-1);
		}
		if (password) {
			sscanf(password, "\"password\": \"%99[^\"]\"", db->password);
		} else {
			strncpy(db->password, "inspuR@468", sizeof(db->password)-1);
		}
		if (dbname) {
			sscanf(dbname, "\"dbname\": \"%99[^\"]\"", db->dbname);
		} else {
			strncpy(db->dbname, "sysmgt", sizeof(db->dbname)-1);
		}
		if (port) {
			sscanf(port, "\"port\": %99[^,}]s", db->port);
		} else {
			strncpy(db->port, "3308", sizeof(db->port)-1);
		}
	}

	LogDebug(COMPONENT_DATABASE, "get for userinfo: host=%s, username=%s, password=%s, dbname=%s, port=%s",
		db->host ? db->host : "NULL", db->username, db->password, db->dbname, db->port);

	return 0;
}


int init_database_connection() {
	init_vpopen();
	
	// get database ip by etcd
	get_host(db->host, sizeof(db->host));
	// get database info
	get_db_user_info();	
	
	int r = dbi_initialize_r(NULL, &db->inst);
	if(r == -1){
		LogCrit(COMPONENT_DATABASE, "Failed to dbi_initialize_r");
		fini_vpopen();
		return -1;
	}
	db->conn = dbi_conn_new_r("pgsql", db->inst);
	if (db->conn == NULL) {
		LogCrit(COMPONENT_DATABASE, "Failed to create new database connection");
		fini_vpopen();
		return -1;
	}
	
	set_common_pg_options();
	
	r = dbi_conn_connect(db->conn);
	if (r < 0) {
		LogCrit(COMPONENT_DATABASE, "Connection to database failed.");
		close_database_connection();
		fini_vpopen();
		return -1;
	}
	LogWarn(COMPONENT_DATABASE, "Connected to database successfully.");

	return 0;
}

//重连数据库
int database_reconnect() {
	if (db == NULL) {
		db = &db_mem;
	}

	if (db->conn){
		dbi_conn_close(db->conn);
		db->conn = NULL;
	}
	if (db->inst){
		dbi_shutdown_r(db->inst);
		db->inst = NULL;
	}

	for (;;) {
		LogWarn(COMPONENT_DATABASE, "reconnect to database %s.", db->host ? db->host : "NULL");

		int r = dbi_initialize_r(NULL, &db->inst);
		if(r == -1){
			LogCrit(COMPONENT_DATABASE, "Failed to dbi_initialize_r");
			if (db->inst) 
				dbi_shutdown_r(db->inst);
			db->inst = NULL;
			sleep(1);
			continue;
		}
		db->conn = dbi_conn_new_r("pgsql", db->inst);
		if (db->conn == NULL) {
			LogCrit(COMPONENT_DATABASE, "Failed to create new database connection");
			if (db->inst) 
				dbi_shutdown_r(db->inst);
			db->inst = NULL;
			sleep(1);
			continue;
		}
		
		set_common_pg_options();
		
		r = dbi_conn_connect(db->conn);
		if (r < 0) {
			LogCrit(COMPONENT_DATABASE, "reconnect to database failed.");
			if (db->conn)
				dbi_conn_close(db->conn);
			db->conn = NULL;
			if (db->inst) 
				dbi_shutdown_r(db->inst);
			db->inst = NULL;
			get_host(db->host, sizeof(db->host));
			sleep(1);
			continue;
		}
		LogWarn(COMPONENT_DATABASE, "reconnect to database successfully.");
		return 0;
	}
}

void close_database_connection() {
	if (db->conn)
		dbi_conn_close(db->conn);
	if (db->inst)
		dbi_shutdown_r(db->inst);
	LogWarn(COMPONENT_DATABASE, "Database connection closed.");
}

bool check_network_error(){
	const char* error;
	int r = dbi_conn_error(db->conn, &error);
	/*
	if(r == DBI_ERROR_NOCONN || strstr(error, "server not reachable") != NULL ||
		strstr(error, "server closed the connection unexpectedly") != NULL ||
		strstr(error, "could not connect to server") != NULL){
		LogCrit(COMPONENT_DATABASE, "check_network_error: %s", error);
		return true;
	}
	*/
	sleep(1);
	LogCrit(COMPONENT_DATABASE, " check_network_error: %s, r:%d ", error, r);
	return true;
}

int db_query_with_timeout(const char *query, dbi_result *result, int timeout_seconds) {
	if (db == NULL || db->conn == NULL || reconnect_database_falg) {
		database_reconnect();
		reconnect_database_falg = false;
	}

	if (!thread_ok) start_thread();
	while (gTask.state != S_IDLE) {
		usleep(10 * 1000);
	}

	LogDebug(COMPONENT_DATABASE, "%s", query);
	memset(gTask.sql, 0, sizeof(gTask.sql));
	strncpy(gTask.sql, query, sizeof(gTask.sql) - 1);
	gTask.result = result;
	gTask.state = S_RUNNING;
	sem_post(&sem_has_task);				   /* 派发任务 */

	// 等待查询完成或超时
	struct timespec start_time, current_time;
	clock_gettime(CLOCK_MONOTONIC, &start_time);
	
	while (gTask.state == S_RUNNING) {
		/* 1、查询超时，强制重连*/
		clock_gettime(CLOCK_MONOTONIC, &current_time);
		if ((current_time.tv_sec - start_time.tv_sec) >= timeout_seconds) {
			LogWarn(COMPONENT_DATABASE, "query databse timeout");
			// 取消查询线程
			stop_thread();
			//重新获取数据库IP
			memset(db->host, 0, sizeof(db->host));
			get_host(db->host, sizeof(db->host));
			// 强制重连数据库
			database_reconnect();
			reconnect_database_falg = false;
			//解锁，重新投递查询任务
			gTask.state = S_IDLE;
			return -1;
		}
		if (reconnect_database_falg) {
			LogWarn(COMPONENT_DATABASE, "The database master ip is changed(ip:%s), need to reconnect database", db->host ? db->host : "NULL");
			// 取消查询线程
			stop_thread();
			// 强制重连数据库
			database_reconnect();
			reconnect_database_falg = false;
			//重新投递查询任务
			gTask.state = S_IDLE;
			return -1;
		}
		// 短暂休眠，避免CPU占用过高
		usleep(100000);  // 100ms
	}


	// 查询已完成，检查结果
	if (gTask.state == S_FAIL) {
		// 查询失败
		const char *errmsg;
		int err = dbi_conn_error(db->conn, &errmsg);
		LogWarn(COMPONENT_DATABASE, "Database error: %s (code: %d)", errmsg, err);
		// 取消查询线程
		stop_thread();
		// 重连数据库
		database_reconnect();
		//重新投递查询任务
		gTask.state = S_IDLE;
		return -1;
	}
	gTask.state = S_IDLE;
	return 0;
}

int db_query_table(const char *query, dbi_result *result) {
	pthread_mutex_lock(&db_mutex);
	int rc = db_query_with_timeout(query, result, TIME_OUT);
	int count = 0;
	while (rc != 0) {
		LogWarn(COMPONENT_DATABASE, "query failed, retry, count=%d", count);
		count++;
		rc = db_query_with_timeout(query, result, TIME_OUT);
	}
	pthread_mutex_unlock(&db_mutex);
	return rc;
}


int get_tenant_name_by_ip(char *ip_str, const char **tenant_name) {
	//SELECT DISTINCT a.tenant FROM access_zone a JOIN zone_ip z ON a.id = z.zone_id WHERE z.ip = '************'
	dbi_result result;
	char sql_buf[SQL_BUF_LEN];
	memset(sql_buf, 0, sizeof(sql_buf));
	snprintf(sql_buf, sizeof(sql_buf), "SELECT DISTINCT a.tenant FROM access_zone a JOIN zone_ip z ON a.id = z.zone_id WHERE z.ip = '%s'", ip_str);
	
	int r = db_query_table(sql_buf, &result);
	
	if (r != 0){
		if (result) dbi_result_free(result);
		LogCrit(COMPONENT_DATABASE, "Query access_zone fail: %d", r);
		return -1;
	}
	
	if (dbi_result_next_row(result)) {
		const char *name = dbi_result_get_string(result, "tenant");
		if (name == NULL) {
			*tenant_name = NULL;
			return -2;
		}
		*tenant_name = strdup(name);
		if (*tenant_name == NULL) {
			LogCrit(COMPONENT_DATABASE, "failed to creat mem.");
			return -3;
		}
	}
	
	if (result) dbi_result_free(result);
	return 0;
}

/**
 * parse_audit_op_words
 *  将类似 "write|read|open" 解析为对应的 bitmask。
 */
static uint32_t parse_audit_op_words(const char *op_words)
{
	uint32_t flags = 0;
	if (!op_words || !(*op_words)) {
		return 0;
	}

	char buf[SQL_BUF_LEN];
	memset(buf, 0, sizeof(buf));
	strncpy(buf, op_words, sizeof(buf) - 1);
	
	char *saveptr;
	char *token = strtok_r(buf, "|", &saveptr);
	
	while (token) {
		if (strcasecmp(token, "create") == 0) {
			flags |= AUDIT_OP_CREATE;
		} else if (strcasecmp(token, "delete") == 0) {
			flags |= AUDIT_OP_DELETE;
		} else if (strcasecmp(token, "open") == 0) {
			flags |= AUDIT_OP_OPEN;
		} else if (strcasecmp(token, "close") == 0) {
			flags |= AUDIT_OP_CLOSE;
		} else if (strcasecmp(token, "write") == 0) {
			flags |= AUDIT_OP_WRITE;
		} else if (strcasecmp(token, "read") == 0) {
			flags |= AUDIT_OP_READ;
		} else if (strcasecmp(token, "set_attr") == 0) {
			flags |= AUDIT_OP_SETATTR;
		} else if (strcasecmp(token, "get_attr") == 0) {
			flags |= AUDIT_OP_GETATTR;
		} else if (strcasecmp(token, "rename") == 0) {
			flags |= AUDIT_OP_RENAME;
		} else if (strcasecmp(token, "list") == 0) {
			flags |= AUDIT_OP_LIST;
		} else if (strcasecmp(token, "set_security") == 0) {
			flags |= AUDIT_OP_SETSECURITY;
		} else if (strcasecmp(token, "get_security") == 0) {
			flags |= AUDIT_OP_GETSECURITY;
		}
		/* pass set_xattr, get_xattr, concat  */
		token = strtok_r(NULL, "|", &saveptr);
	}

	return flags;
}

/**
 * load_all_ns_audit_from_db
 *  从 tenant_audit_info / ns_audit_info 两张表，读取审计配置并加载到内存缓存。
 */
int load_all_ns_audit_from_db(void)
{
	dbi_result result = NULL;
	char sql_buf[SQL_BUF_LEN];
	memset(sql_buf, 0, sizeof(sql_buf));

	/* 1) 先找出所有 audit_switch=1 的租户 */
	snprintf(sql_buf, sizeof(sql_buf),
			"SELECT tenant_name, audit_switch FROM tenant_audit_info WHERE audit_switch = 1");

	int r = db_query_table(sql_buf, &result);
	if (r != 0) {
		if (result) dbi_result_free(result);
		LogCrit(COMPONENT_DATABASE, "Query tenant_audit_info failed: err=%d", r);
		return -1;
	}

	/* 2) 对于每个租户, 再去读取 ns_audit_info */
	while (dbi_result_next_row(result)) {
		const char *tenant_name = dbi_result_get_string(result, "tenant_name");

		if (!tenant_name || !(*tenant_name)) {
			continue;
		}

		/* 调用下一个函数，加载该租户对应的所有 namespace 审计记录 */
		load_ns_audit_for_tenant(tenant_name);
	}

	if (result) dbi_result_free(result);
	return 0;
}

/**
 * 加载租户为tenant_name的所有命名空间的审计
 */
int load_ns_audit_for_tenant(const char *tenant_name)
{
	dbi_result result = NULL;
	char sql_buf[SQL_BUF_LEN];
	memset(sql_buf, 0, sizeof(sql_buf));

	snprintf(sql_buf, sizeof(sql_buf),
			"SELECT tenant_name, ns_name, audit_op_words FROM ns_audit_info "
			"WHERE tenant_name = '%s'", tenant_name);

	int r = db_query_table(sql_buf, &result);
	if (r != 0) {
		if (result) dbi_result_free(result);
		LogCrit(COMPONENT_DATABASE, "Query ns_audit_info failed for tenant=%s, err=%d",
			tenant_name, r);
		return -1;
	}

	while (dbi_result_next_row(result)) {
		const char *t_name = dbi_result_get_string(result, "tenant_name");
		const char *ns_name = dbi_result_get_string(result, "ns_name");
		const char *op_words = dbi_result_get_string(result, "audit_op_words");

		if (!t_name || !ns_name || !op_words) {
			continue;
		}

		/* 解析 audit_op_words => bitmask */
		uint32_t flags = parse_audit_op_words(op_words);

		/* 写入缓存 */
		struct ns_audit *au;
		au = get_ns_audit(t_name, ns_name, false);
		if (au) {
			au->audit_flags = flags;
			put_ns_audit(au);
		}
	}

	if (result) dbi_result_free(result);
	return 0;
}

/**
 * 加载租户为tenant_name，命名空间为ns_name的命名空间的审计
 */
int load_ns_audit_for_tenant_namespace(const char *tenant_name, const char *ns_name)
{
	dbi_result result = NULL;
	char sql_buf[SQL_BUF_LEN];
	memset(sql_buf, 0, sizeof(sql_buf));

	snprintf(sql_buf, sizeof(sql_buf),
			"SELECT tenant_name, ns_name, audit_op_words FROM ns_audit_info "
			"WHERE tenant_name = '%s' AND ns_name = '%s'", tenant_name, ns_name);

	int r = db_query_table(sql_buf, &result);
	if (r != 0) {
		if (result) dbi_result_free(result);
			LogCrit(COMPONENT_DATABASE, "Query ns_audit_info failed for tenant=%s, err=%d",
				tenant_name, r);
		return -1;
	}

	while (dbi_result_next_row(result)) {
		const char *t_name = dbi_result_get_string(result, "tenant_name");
		const char *ns_name = dbi_result_get_string(result, "ns_name");
		const char *op_words = dbi_result_get_string(result, "audit_op_words");

		if (!t_name || !ns_name || !op_words) {
			continue;
		}

		/* 解析 audit_op_words => bitmask */
		uint32_t flags = parse_audit_op_words(op_words);

		/* 写入缓存 */
		struct ns_audit *au;
		au = get_ns_audit(t_name, ns_name, false);
		if (au) {
			au->audit_flags = flags;
			put_ns_audit(au);
		}
	}

	if (result) dbi_result_free(result);
	return 0;
}


void remove_all_namespaces_of_tenant(const char *tenant_name)
{
	if (!tenant_name || !*tenant_name){
		LogCrit(COMPONENT_DATABASE, "remove_all_namespaces_of_tenant failed. tenant_name=NULL");
		return;
	}

	char sql_buf[SQL_BUF_LEN];
	memset(sql_buf, 0, sizeof(sql_buf));
	snprintf(sql_buf, sizeof(sql_buf),
		"SELECT ns_name FROM ns_audit_info WHERE tenant_name='%s'",
		tenant_name);

	dbi_result result = NULL;
	int r = db_query_table(sql_buf, &result);
	if (r != 0) {
		if (result) dbi_result_free(result);
		LogCrit(COMPONENT_DATABASE, "Query ns_audit_info fail for tenant=%s", tenant_name);
		return;
	}

	while (dbi_result_next_row(result)) {
		const char *ns_name = dbi_result_get_string(result, "ns_name");
		if (!ns_name) {
			continue;
		}

		struct ns_audit *au;
		au = get_ns_audit(tenant_name, ns_name, true);
		if (au) {
			put_ns_audit(au);
			put_ns_audit(au); /* 删除该au */
		}
	}

	if (result) dbi_result_free(result);
}

int reload_tenant_info(const char *op, const char *tenant_name)
{
	if (strcasecmp(op, "delete") == 0) {
		LogInfo(COMPONENT_DATABASE, "tenant=%s: delete op => remove all ns from cache", tenant_name);
		remove_all_namespaces_of_tenant(tenant_name);
	} else if (strcasecmp(op, "insert") == 0) {
		char sql_buf[SQL_BUF_LEN];
		memset(sql_buf, 0, sizeof(sql_buf));
		snprintf(sql_buf, sizeof(sql_buf),
			"SELECT tenant_name, audit_switch FROM tenant_audit_info WHERE tenant_name='%s'",
			tenant_name);

		dbi_result result = NULL;
		int r = db_query_table(sql_buf, &result);
		if (r != 0) {
			if (result) dbi_result_free(result);
			LogCrit(COMPONENT_DATABASE, "Query tenant_audit_info fail: %d", r);
			return -1;
		}

		int audit_switch = 0;
		if (dbi_result_next_row(result)) {
			audit_switch = dbi_result_get_int(result, "audit_switch");
		}
		if (result) dbi_result_free(result);

		// 删除旧缓存
		remove_all_namespaces_of_tenant(tenant_name);
		if (audit_switch == 1) {
			//reload
			LogInfo(COMPONENT_DATABASE, "tenant=%s: audit_switch=1 => load ns from DB", tenant_name);
			load_ns_audit_for_tenant(tenant_name);
		}
	}else {
		LogWarn(COMPONENT_DATABASE, "Unknown operation: %s", op);
	}
	
	return 0;
}

int reload_ns_info(const char *op, const char *tenant_name, const char *ns_name) 
{
	if (strcasecmp(op, "delete") == 0) {
		LogInfo(COMPONENT_DATABASE, "tenant=%s: delete op => remove all ns from cache", tenant_name);
		struct ns_audit *au;
		au = get_ns_audit(tenant_name, ns_name, true);
		if (au) {
			put_ns_audit(au);
			put_ns_audit(au); /* 删除该au */
		}
	} else if (strcasecmp(op, "insert") == 0) {
		char sql_buf[SQL_BUF_LEN];
		memset(sql_buf, 0, sizeof(sql_buf));
		snprintf(sql_buf, sizeof(sql_buf),
			"SELECT tenant_name, audit_switch FROM tenant_audit_info WHERE tenant_name='%s'",
			tenant_name);

		dbi_result result = NULL;
		int r = db_query_table(sql_buf, &result);
		if (r != 0) {
			if (result) dbi_result_free(result);
			LogCrit(COMPONENT_DATABASE, "Query tenant_audit_info fail: %d", r);
			return -1;
		}

		int audit_switch = 0;
		if (dbi_result_next_row(result)) {
			audit_switch = dbi_result_get_int(result, "audit_switch");
		}
		if (result) dbi_result_free(result);

		// 删除旧缓存
		struct ns_audit *au;
		au = get_ns_audit(tenant_name, ns_name, true);
		if (au) {
			put_ns_audit(au);
			put_ns_audit(au); /* 删除该au */
		}
		
		if (audit_switch == 1) {
			//reload
			LogInfo(COMPONENT_DATABASE, "tenant=%s: audit_switch=1 => load ns from DB", tenant_name);
			load_ns_audit_for_tenant(tenant_name);
		}
	}else {
		LogWarn(COMPONENT_DATABASE, "Unknown operation: %s", op);
	}
	return 0;
}

