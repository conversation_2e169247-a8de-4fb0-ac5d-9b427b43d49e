/* ********************************
 * Author:       <PERSON>
 * License:	     MIT
 * Description:  Library providing a threading pool where you can add
 *               work. For usage, check the thpool.h file or README.md
 *
 *//** @file thpool.h *//*
 *
 ********************************/

#if defined(__APPLE__)
#include <AvailabilityMacros.h>
#else
#ifndef _POSIX_C_SOURCE
#define _POSIX_C_SOURCE 200809L
#endif
#ifndef _XOPEN_SOURCE
#define _XOPEN_SOURCE 500
#endif
#endif
#include <unistd.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>
#include <errno.h>
#include <time.h>
#include <stdint.h>
#include <stdbool.h>
#if defined(__linux__)
#include <sys/prctl.h>
#endif
#if defined(__FreeBSD__) || defined(__OpenBSD__)
#include <pthread_np.h>
#endif
#include "config.h"
#include "thpool.h"
#include "fabric.h"
#include "fab_common.h"
#include "../rdma/rdma.h"
#ifdef THPOOL_DEBUG
#define THPOOL_DEBUG 1
#else
#define THPOOL_DEBUG 0
#endif

#define __DEBUG_SUBMIT_NOLOCK 0


#if !defined(DISABLE_PRINT) || defined(THPOOL_DEBUG)
#define err(str) fprintf(stderr, str)
#else
#define err(str)
#endif

#ifndef THPOOL_THREAD_NAME
#define THPOOL_THREAD_NAME thpool
#endif

#define STRINGIFY(x) #x
#define TOSTRING(x) STRINGIFY(x)

#ifndef NULL
#define NULL (void *)0
#endif


static volatile int threads_keepalive;
static volatile int threads_on_hold;
int cq_cpu = -1;
int worker_cpus[CPU_NUM] = {-1};
int worker_cpu_num = 0;
int cur_cpu = 0;
int numa_node = -1;
int binding = 0;
int thread_cnt = 0;
int total_thread_cnt = 0;
int thread_cpus[CPU_NUM] = {-1};


__thread th_curr  *thread_cur = NULL;
__thread int  thread_id;


/* ========================== PROTOTYPES ============================ */


static int  thread_init(thpool_* thpool_p, struct thread** thread_p, int id, const char *name);
static void* thread_do(struct thread* thread_p);
static void* thread_do_nolock(struct thread* thread_p);
static void  thread_hold(int sig_id);
static void  thread_destroy(struct thread* thread_p);

static int   jobqueue_init(jobqueue* jobqueue_p);
static void  jobqueue_clear(jobqueue* jobqueue_p);
static void  jobqueue_push(jobqueue* jobqueue_p, struct job* newjob_p);
static struct job* jobqueue_pull(jobqueue* jobqueue_p);
static void  jobqueue_destroy(jobqueue* jobqueue_p);

//static void  bsem_init(struct bsem *bsem_p, int value);
//static void  bsem_reset(struct bsem *bsem_p);
//static void  bsem_post(struct bsem *bsem_p);
//static void  bsem_post_all(struct bsem *bsem_p);
//static void  bsem_wait(struct bsem *bsem_p);

//#define QUEUE_EXIT_DATA		(0x01)
//#define QUEUE_DATA_FULL		(0x02)
//
//#define IS_QUEUE_STATE_SET(x, mask) (((x->state) & (mask)) == (mask) ? 1 : 0)
//#define QUEUE_STATE_SET(x, mask) ((x->state) |= (mask))
//#define QUEUE_STATE_CLEAR(x, mask) ((x->state) &= ~(mask))

static queue_ctrl_t *job_queue_create(queuesize_t queue_size)
{
	if (queue_size == 0) {
		return NULL;
	}

	queue_ctrl_t *queue = (queue_ctrl_t *)malloc(sizeof(queue_ctrl_t));
	memset(queue, 0, sizeof(queue_ctrl_t));
	if (NULL != queue) {
		queue->end = 0;
		queue->head = 0;
		queue->size = queue_size;
		//queue->state = 0x00;
		queue->jobs = (job **)malloc(sizeof(job *) * queue->size);
		memset(queue->jobs, 0, sizeof(job *) * queue->size);
	}

	return queue;
}

bool is_job_queue_full(queue_ctrl_t *queue)
{
    return ((queue->end + 1) % queue->size) == queue->head;
}

bool is_job_queue_empty(queue_ctrl_t *queue)
{
    return (queue->head == queue->end);
}

int job_queue_push(queue_ctrl_t *queue, job *job, int thread_id)
{
    if (queue == NULL || job == NULL) {
        return -1;
    }

    if (is_job_queue_full(queue)) {
	__warnx_1hz(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC job_queue_push is full.", __func__);
        return -1;
    }

    queue->jobs[queue->end] = job;
    queue->end = (queue->end + 1) % queue->size;

    //printf("thread_id: %d, push head: %d, tail: %d, job: %p\n", thread_id, queue->head, queue->end, job);
    return 0;
}

job *job_queue_pop(queue_ctrl_t *queue, int thread_id)
{
    if (queue == NULL) {
        return NULL;
    }

    if (is_job_queue_empty(queue)) {
        return NULL;
    }


    job *job = queue->jobs[queue->head];
    queue->head = (queue->head + 1) % queue->size;

    //printf("thread id: %d, pop head: %d, tail: %d, job: %p\n", thread_id, queue->head, queue->end, job);
    return job;
}

/* ========================== THREADPOOL ============================ */

/* Initialise thread pool */
struct thpool_* thpool_init(const char *thread_name, int num_threads){

	threads_on_hold   = 0;
	threads_keepalive = 1;

	if (num_threads < 0){
		num_threads = 0;
	}

	/* Make new thread pool */
	thpool_* thpool_p;
	thpool_p = (struct thpool_*)malloc(sizeof(struct thpool_));
	if (thpool_p == NULL){
		err("thpool_init(): Could not allocate memory for thread pool\n");
		return NULL;
	}
	thpool_p->num_threads = num_threads;
	thpool_p->num_threads_alive   = 0;
	thpool_p->num_threads_working = 0;
	thpool_p->binding = binding;
	thpool_p->threads_keepalive = true;
	thpool_p->no_lock = false;

	/* Initialise the job queue */
	if (jobqueue_init(&thpool_p->jobqueue) == -1){
		err("thpool_init(): Could not allocate memory for job queue\n");
		free(thpool_p);
		return NULL;
	}

	/* Make threads in pool */
	thpool_p->threads = (struct thread**)malloc(num_threads * sizeof(struct thread *));
	if (thpool_p->threads == NULL){
		err("thpool_init(): Could not allocate memory for threads\n");
		jobqueue_destroy(&thpool_p->jobqueue);
		free(thpool_p);
		return NULL;
	}
	thpool_p->jobqueues = (queue_ctrl_t **)malloc(num_threads * sizeof(queue_ctrl_t *));
	thpool_p->reply_jobqueues = (struct jobqueue *)malloc(num_threads * sizeof(struct jobqueue));
	thpool_p->recv_jobqueues = (struct jobqueue *)malloc(num_threads * sizeof(struct jobqueue));
	if (thpool_p->jobqueues == NULL || thpool_p->reply_jobqueues == NULL) {
		err("thpool_init(): Could not allocate memory for jobqueues\n");
		free(thpool_p->threads);
		free(thpool_p);
		return NULL;
	}


	pthread_mutex_init(&(thpool_p->thcount_lock), NULL);
	pthread_cond_init(&thpool_p->threads_all_idle, NULL);

	/* Thread init */
	int n;
	for (n = 0; n < num_threads; n++) {
		thpool_p->jobqueues[n] = job_queue_create(400);
		jobqueue_init(&thpool_p->reply_jobqueues[n]);
		jobqueue_init(&thpool_p->recv_jobqueues[n]);
		thread_init(thpool_p, &thpool_p->threads[n], n, thread_name);
#if THPOOL_DEBUG
		printf("THPOOL_DEBUG: Created thread %d in pool\n", n);
#endif
	}

	thpool_p->last_job = 0;
	thpool_p->last_reply_job = 0;

	/* Wait for threads to initialize */
	while (thpool_p->num_threads_alive != num_threads) {}

	return thpool_p;
}

/* Initialise thread pool */
struct thpool_* thpool_init_nolock(const char *thread_name, int num_threads){

	threads_on_hold   = 0;
	threads_keepalive = 1;

	if (num_threads < 0){
		num_threads = 0;
	}

	/* Make new thread pool */
	thpool_* thpool_p;
	thpool_p = (struct thpool_*)malloc(sizeof(struct thpool_));
	if (thpool_p == NULL){
		err("thpool_init(): Could not allocate memory for thread pool\n");
		return NULL;
	}
	memset(thpool_p, 0, sizeof(struct thpool_));
	thpool_p->num_threads = num_threads;
	thpool_p->num_threads_alive   = 0;
	thpool_p->num_threads_working = 0;

	/* Initialise the job queue */
	if (jobqueue_init(&thpool_p->jobqueue) == -1){
		err("thpool_init(): Could not allocate memory for job queue\n");
		free(thpool_p);
		return NULL;
	}

	/* Make threads in pool */
	thpool_p->threads = (struct thread**)malloc(num_threads * sizeof(struct thread *));
	if (thpool_p->threads == NULL){
		err("thpool_init(): Could not allocate memory for threads\n");
		jobqueue_destroy(&thpool_p->jobqueue);
		free(thpool_p);
		return NULL;
	}
	
	thpool_p->no_lock = true;
	thpool_p->binding = false;
	thpool_p->self_thr_index_min = thread_cnt;
	thpool_p->self_thr_index_max = thread_cnt + num_threads - 1;
	thpool_p->producer_thr_max = total_thread_cnt;  /*生产者的数量*/
	thpool_p->threads_keepalive = true;
	thpool_p->need_sleep = true;
	if (strcmp(thread_name, "cq_thpool") == 0) { 
		thpool_p->binding = binding;
		thpool_p->sleep_time = 5;
	}
	if (strcmp(thread_name, "rpc_thpool") == 0) { 
		thpool_p->binding = binding;
		thpool_p->need_sleep = false;
		thpool_p->sleep_time = 5;
	}
	if (strcmp(thread_name, "work_thpool") == 0) { 
		thpool_p->sleep_time = rdma_xprt_class.rdmaxprt->sm_dr.xprt.work_sleep_time;
	}


	printf("thpool name %s, total cnt %d, min_index %d, max_index %d. total thread %d \n", 
			thread_name, 
			num_threads,
			thpool_p->self_thr_index_min, 
			thpool_p->self_thr_index_max, 
			thpool_p->producer_thr_max);
	if (thpool_p->no_lock ==  true) {
		int i = 0;
		int n = 0;
		pthread_mutex_init(&(thpool_p->thcount_lock), NULL);
		pthread_cond_init(&thpool_p->threads_all_idle, NULL);

		thpool_p->threads_keepalive = true;
		/* Thread init */
		for (i=0; i< thpool_p->producer_thr_max; i++) {
			thpool_p->jobqueues_nolock[i] = (queue_ctrl_t **)malloc(thpool_p->producer_thr_max * sizeof(queue_ctrl_t *));
			memset( thpool_p->jobqueues_nolock[i], 0, thpool_p->producer_thr_max * sizeof(queue_ctrl_t *));
			if (thpool_p->jobqueues_nolock[i] == NULL) {
				err("thpool_init(): Could not allocate memory for jobqueues\n");
				free(thpool_p->threads);
				free(thpool_p);
				return NULL;
			}
			for (n = 0; n < thpool_p->producer_thr_max; n++) {
				thpool_p->jobqueues_nolock[i][n] = job_queue_create(400);
			}
		}
		thpool_p->jobqueues = (queue_ctrl_t **)malloc(thpool_p->producer_thr_max * sizeof(queue_ctrl_t *));
		thpool_p->reply_jobqueues = (struct jobqueue *)malloc(thpool_p->producer_thr_max * sizeof(struct jobqueue));
		thpool_p->recv_jobqueues = (struct jobqueue *)malloc(thpool_p->producer_thr_max * sizeof(struct jobqueue));
		if (thpool_p->jobqueues == NULL || thpool_p->reply_jobqueues == NULL) {
			err("thpool_init(): Could not allocate memory for jobqueues\n");
			free(thpool_p->threads);
			free(thpool_p);
			return NULL;
		}
		for (n = 0; n < thpool_p->producer_thr_max; n++) {
			jobqueue_init(&thpool_p->reply_jobqueues[n]);
			jobqueue_init(&thpool_p->recv_jobqueues[n]);
			thpool_p->jobqueues[n] = job_queue_create(400);
		}


		for (n = 0; n < num_threads; n++) {
			thread_init(thpool_p, &thpool_p->threads[n], thread_cnt, thread_name);
			thread_cnt ++;
#if THPOOL_DEBUG
			printf("THPOOL_DEBUG: Created thread %d in pool\n", n);
#endif
		}
	} else {
		int n = 0;
		thpool_p->jobqueues = (queue_ctrl_t **)malloc(thpool_p->producer_thr_max * sizeof(queue_ctrl_t *));
		thpool_p->reply_jobqueues = (struct jobqueue *)malloc(thpool_p->producer_thr_max * sizeof(struct jobqueue));
		thpool_p->recv_jobqueues = (struct jobqueue *)malloc(thpool_p->producer_thr_max * sizeof(struct jobqueue));
		if (thpool_p->jobqueues == NULL || thpool_p->reply_jobqueues == NULL) {
			err("thpool_init(): Could not allocate memory for jobqueues\n");
			free(thpool_p->threads);
			free(thpool_p);
			return NULL;
		}
		for (n = 0; n < thpool_p->producer_thr_max; n++) {
			jobqueue_init(&thpool_p->reply_jobqueues[n]);
			jobqueue_init(&thpool_p->recv_jobqueues[n]);
			thpool_p->jobqueues[n] = job_queue_create(400);
		}

		pthread_mutex_init(&(thpool_p->thcount_lock), NULL);
		pthread_cond_init(&thpool_p->threads_all_idle, NULL);

		thpool_p->threads_keepalive = true;
		/* Thread init */
		for (n = 0; n < num_threads; n++) {

			thread_init(thpool_p, &thpool_p->threads[n], thread_cnt, thread_name);
			thread_cnt ++;
#if THPOOL_DEBUG
			printf("THPOOL_DEBUG: Created thread %d in pool\n", n);
#endif
		}
	}

	thpool_p->last_job = 0;
	thpool_p->last_reply_job = 0;

	/* Wait for threads to initialize */
	while (thpool_p->num_threads_alive != num_threads) {}

	return thpool_p;
}


/* Add work to the thread pool */
int thpool_add_job(thpool_* thpool_p, void (*function_p)(void*), void* arg_p) {
	if (thpool_p->num_threads == 0) {
	    return -1;
	}
	job* newjob;

	newjob=(struct job*)malloc(sizeof(struct job));
	if (newjob==NULL){
		err("thpool_add_job(): Could not allocate memory for new job\n");
		return -1;
	}

	/* add function and argument */
	newjob->function=function_p;
	newjob->arg=arg_p;

	thpool_p->last_job = (thpool_p->last_job + 1) % thpool_p->num_threads;

	/* add job to queue */
	while (job_queue_push(thpool_p->jobqueues[thpool_p->last_job], newjob, thpool_p->last_job) != 0) {
	//jobqueue_push(&thpool_p->recv_jobqueues[thpool_p->last_job], newjob);
		thpool_p->last_job = (thpool_p->last_job + 1) % thpool_p->num_threads;
	}

	return 0;
}
/* Add work to the thread pool */
int thpool_add_job_nolock(thpool_* thpool_p, void (*function_p)(void*), void* arg_p){
	job* newjob;
	int thread_curr_id = 0;
	if (thread_cur != NULL) {
		thread_curr_id = thread_cur->pthread_index;
		if ((thread_curr_id >= thpool_p->self_thr_index_min) &&  
		    (thread_curr_id <= thpool_p->self_thr_index_max)) {
			function_p(arg_p);		
			return 0;
		}
	} else {
		thread_curr_id = 0;
	}

//	int thread_curr_id = thread_id;
	newjob=(struct job*)malloc(sizeof(struct job));
	if (newjob==NULL){
		err("thpool_add_job(): Could not allocate memory for new job\n");
		return -1;
	}

	/* add function and argument */
	newjob->function=function_p;
	newjob->arg=arg_p;

	thpool_p->last_job_nolock[thread_curr_id] ++;
	if  (thpool_p->last_job_nolock[thread_curr_id] < thpool_p->self_thr_index_min)
		thpool_p->last_job_nolock[thread_curr_id] = thpool_p->self_thr_index_min;
	if  (thpool_p->last_job_nolock[thread_curr_id] > thpool_p->self_thr_index_max)
		thpool_p->last_job_nolock[thread_curr_id] = thpool_p->self_thr_index_min;
#if __DEBUG_SUBMIT_NOLOCK
	printf("func %s,work id %d , jobqueues_nolock[%d][%d] start! min %d, max %d th name %s!  \n",  __func__, thread_curr_id, thread_curr_id, thpool_p->last_job_nolock[thread_curr_id], thpool_p->self_thr_index_min, 
		thpool_p->self_thr_index_max, (thread_cur != NULL) ? thread_cur->thr_name : "cq" );
#endif
	//thpool_p->last_job_nolock[thread_curr_id] = (thpool_p->last_job_nolock[thread_curr_id] + 1) % thpool_p->num_threads;

	/* add job to queue */
	while (job_queue_push(thpool_p->jobqueues_nolock[thread_curr_id][thpool_p->last_job_nolock[thread_curr_id]], newjob, 0) != 0) {
#if __DEBUG_SUBMIT_NOLOCK
		printf("func %s,work id %d , jobqueues_nolock[%d][%d] error! \n",  __func__, thread_curr_id, thread_curr_id, thpool_p->last_job_nolock[thread_curr_id]);
#endif 
		__warnx_1hz(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC job_queue_push != 0, jobqueues_nolock[%d][%d].", __func__,
				thread_curr_id, thpool_p->last_job_nolock[thread_curr_id]);
		thpool_p->last_job_nolock[thread_curr_id] ++;
		if  (thpool_p->last_job_nolock[thread_curr_id] > thpool_p->self_thr_index_max)
			thpool_p->last_job_nolock[thread_curr_id] = thpool_p->self_thr_index_min;
	}
#if __DEBUG_SUBMIT_NOLOCK
	printf("func %s,work id %d , jobqueues_nolock[%d][%d] success ! \n",  __func__, thread_curr_id, thread_curr_id, thpool_p->last_job_nolock[thread_curr_id]);
#endif
	return 0;
}
/* Add work to the thread pool */
int thpool_add_job_nolock_withthrid(thpool_* thpool_p, void (*function_p)(void*), void* arg_p, int destid){
	job* newjob;
	int thread_curr_id = 0;
	if (thread_cur != NULL) {
		thread_curr_id = thread_cur->pthread_index;
		if ((thread_curr_id >= thpool_p->self_thr_index_min) &&  
		    (thread_curr_id <= thpool_p->self_thr_index_max)) {
			function_p(arg_p);		
			return 0;
		}
	} else {
		thread_curr_id = 0;
	}
	newjob=(struct job*)malloc(sizeof(struct job));
	if (newjob==NULL){
		//err("thpool_add_job(): Could not allocate memory for new job\n");
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC : Could not allocate memory for new job.", __func__);
		return -1;
	}
	/* add function and argument */
	newjob->function=function_p;
	newjob->arg=arg_p;

	/* add job to queue */
	while (job_queue_push(thpool_p->jobqueues_nolock[thread_curr_id][destid], newjob, 0) != 0) {
#if __DEBUG_SUBMIT_NOLOCK
		printf("func %s,work id %d , jobqueues_nolock[%d][%d] error! \n",  __func__, thread_curr_id, thread_curr_id, thpool_p->last_job_nolock[thread_curr_id]);
#endif 
		__warnx_1hz(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC job_queue_push != 0, jobqueues_nolock[%d][%d].", __func__,
			thread_curr_id, thpool_p->last_job_nolock[thread_curr_id]);
	}
#if __DEBUG_SUBMIT_NOLOCK
	printf("func %s,work id %d , jobqueues_nolock[%d][%d] success ! \n",  __func__, thread_curr_id, thread_curr_id, thpool_p->last_job_nolock[thread_curr_id]);
#endif
	return 0;
	//return thread_curr_id;
}

/* Add work to the thread pool */
int thpool_add_reply_job(thpool_* thpool_p, void (*function_p)(void*), void* arg_p){
	if (thpool_p->num_threads == 0) {
		return -1;
	}
	job* newjob;

	newjob = (struct job*)malloc(sizeof(struct job));
	if (newjob==NULL){
		err("thpool_add_work(): Could not allocate memory for new job\n");
		return -1;
	}

	/* add function and argument */
	newjob->function=function_p;
	newjob->arg=arg_p;
	thpool_p->last_reply_job = (thpool_p->last_reply_job + 1) % thpool_p->num_threads;
	/* add job to queue */
	jobqueue_push(&thpool_p->reply_jobqueues[thpool_p->self_thr_index_min], newjob);
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "NFS/FABRIC  %s() jobqueue_push end, thpool_p->last_reply_job %d, num_threads %d, reply_jobqueues:%p  %d \n",
		__func__, thpool_p->last_reply_job, thpool_p->num_threads, thpool_p->reply_jobqueues, thpool_p->self_thr_index_min);
	return 0;
}

/* Wait until all jobs have finished */
void thpool_wait(thpool_* thpool_p){
	pthread_mutex_lock(&thpool_p->thcount_lock);
	while (thpool_p->jobqueue.len || thpool_p->num_threads_working) {
		pthread_cond_wait(&thpool_p->threads_all_idle, &thpool_p->thcount_lock);
	}
	pthread_mutex_unlock(&thpool_p->thcount_lock);
}


/* Destroy the threadpool */
void thpool_destroy(thpool_* thpool_p){
	/* No need to destroy if it's NULL */
	if (thpool_p == NULL) return ;

	volatile int threads_total = thpool_p->num_threads_alive;

	/* End each thread 's infinite loop */
	threads_keepalive = 0;
	thpool_p->threads_keepalive = false;
	/* Give one second to kill idle threads */
	double TIMEOUT = 1.0;
	time_t start, end;
	double tpassed = 0.0;
	time (&start);
	while (tpassed < TIMEOUT && thpool_p->num_threads_alive){
		//bsem_post_all(thpool_p->jobqueue.has_jobs);
		time (&end);
		tpassed = difftime(end,start);
	}

	/* Poll remaining threads */
	while (thpool_p->num_threads_alive){
		//bsem_post_all(thpool_p->jobqueue.has_jobs);
		sleep(1);
	}

	/* Job queue cleanup */
	jobqueue_destroy(&thpool_p->jobqueue);
	/* Deallocs */
	int n;
	for (n=0; n < threads_total; n++){
		thread_destroy(thpool_p->threads[n]);
	}
	free(thpool_p->threads);
	free(thpool_p);
}


/* Pause all threads in threadpool */
void thpool_pause(thpool_* thpool_p) {
	int n;
	for (n=0; n < thpool_p->num_threads_alive; n++){
		pthread_kill(thpool_p->threads[n]->pthread, SIGUSR1);
	}
}


/* Resume all threads in threadpool */
void thpool_resume(thpool_* thpool_p) {
    // resuming a single threadpool hasn't been
    // implemented yet, meanwhile this suppresses
    // the warnings
    (void)thpool_p;

	threads_on_hold = 0;
}


int thpool_num_threads_working(thpool_* thpool_p){
	return thpool_p->num_threads_working;
}


/* ============================ THREAD ============================== */


/* Initialize a thread in the thread pool
 *
 * @param thread        address to the pointer of the thread to be created
 * @param id            id to be given to the thread
 * @return 0 on success, -1 otherwise.
 */
static int thread_init (thpool_* thpool_p, struct thread** thread_p, int id, const char *name) {

	*thread_p = (struct thread*)malloc(sizeof(struct thread));
	if (*thread_p == NULL){
		err("thread_init(): Could not allocate memory for thread\n");
		return -1;
	}
	memset(*thread_p, 0, sizeof(struct thread));
	(*thread_p)->thpool_p = thpool_p;
	(*thread_p)->id       = id;
	strncpy((*thread_p)->thr_name, name, THR_NAME_LEN);
	(*thread_p)->threads_keepalive = thpool_p->threads_keepalive;
	(*thread_p)->binding = thpool_p->binding;
	(*thread_p)->no_lock = thpool_p->no_lock;
	if ((*thread_p)->no_lock == true) {
		pthread_create(&(*thread_p)->pthread, NULL, (void * (*)(void *)) thread_do_nolock, (*thread_p));
	} else {
		pthread_create(&(*thread_p)->pthread, NULL, (void * (*)(void *)) thread_do, (*thread_p));
	}

	char thread_name[16] = {0};
	snprintf(thread_name, 16, "%s-%d", name, id);
	//snprintf(thread_name, 16, "%s", name);
	pthread_setname_np((*thread_p)->pthread, thread_name);
	pthread_detach((*thread_p)->pthread);
	return 0;
}


/* Sets the calling thread on hold */
static void thread_hold(int sig_id) {
    (void)sig_id;
	threads_on_hold = 1;
	while (threads_on_hold){
		sleep(1);
	}
}


/* What each thread is doing
*
* In principle this is an endless loop. The only time this loop gets interrupted is once
* thpool_destroy() is invoked or the program exits.
*
* @param  thread        thread that will run this function
* @return nothing
*/
static void* thread_do(struct thread* thread_p){

	/* Set thread name for profiling and debugging */
	int thread_id = thread_p->id;
	thread_cur = (th_curr *)malloc(sizeof(*thread_cur));
	thread_cur->pthread_index = thread_p->id;
	thread_id = thread_p->id;
	/* Assure all threads have been created before starting serving */
	thpool_* thpool_p = thread_p->thpool_p;

	/* Register signal handler */
	struct sigaction act;
	sigemptyset(&act.sa_mask);
	act.sa_flags = SA_ONSTACK;
	act.sa_handler = thread_hold;
	if (sigaction(SIGUSR1, &act, NULL) == -1) {
		err("thread_do(): cannot handle SIGUSR1");
	}

	printf("create thread name %s, thread id %d.\n", thread_p->thr_name, thread_id);
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC create thread name %s, thread id %d.", __func__, thread_p->thr_name, thread_id);
	/* Mark thread as alive (initialized) */
	pthread_mutex_lock(&thpool_p->thcount_lock);
	thpool_p->num_threads_alive += 1;
#if 0
    if (thpool_p->binding) {
	    cpu_set_t cpuset;
	    CPU_ZERO(&cpuset);
	    CPU_SET(worker_cpus[cur_cpu%worker_cpu_num], &cpuset);
	    pthread_setaffinity_np(pthread_self(), sizeof(cpuset), &cpuset);
	    __warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC setaffinity %d .", __func__, worker_cpus[cur_cpu%worker_cpu_num]);
	    cur_cpu ++;
	}
#endif
	pthread_mutex_unlock(&thpool_p->thcount_lock);

	void (*func_buff)(void*);
	void*  arg_buff;
	bool have_job = false;

	while (thpool_p->threads_keepalive) {
		//bsem_wait(thpool_p->jobqueues[thread_id].has_jobs);

		if (thpool_p->threads_keepalive) {
			have_job = false;
			//pthread_mutex_lock(&thpool_p->thcount_lock);
			//thpool_p->num_threads_working++;
			//pthread_mutex_unlock(&thpool_p->thcount_lock);

			/* Read job from queue and execute it */
			job* job_p = job_queue_pop(thpool_p->jobqueues[thread_id], thread_id);
			//job* job_p = jobqueue_pull(&thpool_p->recv_jobqueues[thread_id]);
			if (job_p != NULL) {
				//printf("thread_id: %d, got job_p\n", thread_id);
				have_job = true;
				func_buff = job_p->function;
				arg_buff  = job_p->arg;

				// run job_func
				func_buff(arg_buff);

				free(job_p);
			}
			job_p = jobqueue_pull(&thpool_p->reply_jobqueues[thread_id]);
			if (job_p != NULL) {
				//printf("thread_id: %d, got reply job_p\n", thread_id);
				have_job = true;
				func_buff = job_p->function;
				arg_buff  = job_p->arg;

				// run job_func
				func_buff(arg_buff);

				free(job_p);
			}
			if (!have_job) {
				struct timespec ts = {
					.tv_sec = 0,
					.tv_nsec = 5,
				};
				nanosleep(&ts, NULL);
			}

			//pthread_mutex_lock(&thpool_p->thcount_lock);
			//thpool_p->num_threads_working--;
			//if (!thpool_p->num_threads_working) {
			//	pthread_cond_signal(&thpool_p->threads_all_idle);
			//}
			//pthread_mutex_unlock(&thpool_p->thcount_lock);
		}
	}
	pthread_mutex_lock(&thpool_p->thcount_lock);
	thpool_p->num_threads_alive --;
	pthread_mutex_unlock(&thpool_p->thcount_lock);

	if (thread_cur != NULL)
		free(thread_cur);

	return NULL;
}

/* What each thread is doing
*
* In principle this is an endless loop. The only time this loop gets interrupted is once
* thpool_destroy() is invoked or the program exits.
*
* @param  thread        thread that will run this function
* @return nothing
*/
extern struct rdma_xprt_class_t rdma_xprt_class;
static void* thread_do_nolock(struct thread* thread_p){

	/* Set thread name for profiling and debugging */
	int thread_id = thread_p->id;
	int i = 0;

	thread_cur = (th_curr *)malloc(sizeof(*thread_cur));
	thread_cur->pthread_index = thread_p->id;
	strncpy(thread_cur->thr_name, thread_p->thr_name, THR_NAME_LEN);
	//thread_id = thread_p->id;
	/* Assure all threads have been created before starting serving */
	thpool_* thpool_p = thread_p->thpool_p;

	/* Register signal handler */
	struct sigaction act;
	sigemptyset(&act.sa_mask);
	act.sa_flags = SA_ONSTACK;
	act.sa_handler = thread_hold;
	if (sigaction(SIGUSR1, &act, NULL) == -1) {
		err("thread_do_nolock(): cannot handle SIGUSR1");
	}

	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "create nolock thread name %s, thread id %d.\n", thread_p->thr_name, thread_id);

	/* Mark thread as alive (initialized) */
	pthread_mutex_lock(&thpool_p->thcount_lock);
	thpool_p->num_threads_alive += 1;
#if 0
	if (thpool_p->binding) {
		cpu_set_t cpuset;
		CPU_ZERO(&cpuset);
		CPU_SET(worker_cpus[cur_cpu%worker_cpu_num], &cpuset);
		pthread_setaffinity_np(pthread_self(), sizeof(cpuset), &cpuset);
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC setaffinity %d .", __func__, worker_cpus[cur_cpu%worker_cpu_num]);
		cur_cpu ++;
	}
#endif
	if (thread_cpus[thread_cur->pthread_index] != -1) {
		cpu_set_t cpuset;
		CPU_ZERO(&cpuset);
		CPU_SET(thread_cpus[thread_cur->pthread_index], &cpuset);
		pthread_setaffinity_np(pthread_self(), sizeof(cpuset), &cpuset);
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC setaffinity %d .", __func__, worker_cpus[thread_cur->pthread_index]);
	}

	pthread_mutex_unlock(&thpool_p->thcount_lock);

	void (*func_buff)(void*);
	void*  arg_buff;
	bool have_job = false;
	bool need_sleep = thpool_p->need_sleep;

	while (rdma_xprt_class.g_rdma_xprt_init != true){
		struct timespec ts = {
			.tv_sec = 0,
			.tv_nsec = 500,
		};
		nanosleep(&ts, NULL);
	}
	while (thpool_p->threads_keepalive) {

		if (thpool_p->threads_keepalive) {
			have_job = false;
 			for (i=0; i<thpool_p->producer_thr_max; i++) {
				//if ((i >= thpool_p->self_thr_index_min) && (i <= thpool_p->self_thr_index_max))
				//	continue;

				/* Read job from queue and execute it */
				job* job_p = job_queue_pop(thpool_p->jobqueues_nolock[i][thread_id], thread_id);
				if (job_p != NULL) {
#if __DEBUG_SUBMIT_NOLOCK
					printf("thread_id: %d,name %s, got job_p\n", thread_id,thread_p->thr_name);
#endif
					have_job = true;
					func_buff = job_p->function;
					arg_buff  = job_p->arg;

					func_buff(arg_buff);

					free(job_p);
				} else {
#if __DEBUG_SUBMIT_NOLOCK
					//printf("thread_id: %d,name %s, jobqueues_nolock[%d][%d], no job\n", thread_id, thread_p->thr_name, i, thread_id);
#endif
				}
			}
			if ((!have_job) && (need_sleep)) {
				struct timespec ts = {
#if __DEBUG_SUBMIT_NOLOCK
					.tv_sec = 1,
#else
					.tv_sec = 0,
#endif
					.tv_nsec = thpool_p->sleep_time,
				};
				nanosleep(&ts, NULL);
			}
			if ((!have_job) && (cur_eps_count == 0)) {
				struct timespec ts = {
					.tv_sec = 0,
					.tv_nsec = 50000,
				};
				nanosleep(&ts, NULL);
			}

		}
	}
	pthread_mutex_lock(&thpool_p->thcount_lock);
	thpool_p->num_threads_alive --;
	pthread_mutex_unlock(&thpool_p->thcount_lock);

	if (thread_cur != NULL)
		free(thread_cur);
	return NULL;
}

/* What each thread is doing
*
* In principle this is an endless loop. The only time this loop gets interrupted is once
* thpool_destroy() is invoked or the program exits.
*
* @param  thread        thread that will run this function
* @return nothing
*/
void qos_thread_do(void *arg){

	/* Set thread name for profiling and debugging */

	thpool_* thpool_p = (thpool_*)arg;
	int thread_id = thread_cur->pthread_index;
	int ret = -1;
	/* Assure all threads have been created before starting serving */

	void (*func_buff)(void*);
	void*  arg_buff;
	bool have_job = false;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRIC thread_id = %d,  .", __func__, thread_id);
	while (thpool_p->threads_keepalive) {
		//bsem_wait(thpool_p->jobqueues[thread_id].has_jobs);

		if (thpool_p->threads_keepalive) {
			job* job_p = NULL;
			have_job = false;
			job_p = jobqueue_pull(&thpool_p->reply_jobqueues[thread_id]);
			if (job_p != NULL) {
				//printf("thread_id: %d, got reply job_p\n", thread_id);
				__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRIC qos_thread_do have_job = %d. jobqueues %p , thread_id  = %d", __func__, have_job, thpool_p->reply_jobqueues, thread_id);
				have_job = true;
				func_buff = job_p->function;
				arg_buff  = job_p->arg;

				// run job_func
				//func_buff(arg_buff);

				ret = thpool_add_job_nolock(fab_clas->work_thpool, func_buff, arg_buff);
				free(job_p);
			}
			if (!have_job) {
				struct timespec ts = {
					.tv_sec = 0,
					.tv_nsec = 5,
				};
				nanosleep(&ts, NULL);
			}

		}
	}
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC ret = %d.", __func__, ret);
	return ;
}

/* Frees a thread  */
static void thread_destroy (thread* thread_p){
	free(thread_p);
}





/* ============================ JOB QUEUE =========================== */


/* Initialize queue */
static int jobqueue_init(jobqueue* jobqueue_p){
	jobqueue_p->len = 0;
	jobqueue_p->front = NULL;
	jobqueue_p->rear  = NULL;

	//jobqueue_p->has_jobs = (struct bsem*)malloc(sizeof(struct bsem));
	//if (jobqueue_p->has_jobs == NULL){
	//	return -1;
	//}

	pthread_spin_init(&(jobqueue_p->rwlock), PTHREAD_PROCESS_PRIVATE);
	//bsem_init(jobqueue_p->has_jobs, 0);

	return 0;
}


/* Clear the queue */
static void jobqueue_clear(jobqueue* jobqueue_p){

	while(jobqueue_p->len){
		free(jobqueue_pull(jobqueue_p));
	}

	jobqueue_p->front = NULL;
	jobqueue_p->rear  = NULL;
	//bsem_reset(jobqueue_p->has_jobs);
	jobqueue_p->len = 0;

}


/* Add (allocated) job to queue
 */
static void jobqueue_push(jobqueue* jobqueue_p, struct job* newjob){

	pthread_spin_lock(&jobqueue_p->rwlock);
	newjob->prev = NULL;

	switch(jobqueue_p->len){

		case 0:  /* if no jobs in queue */
					jobqueue_p->front = newjob;
					jobqueue_p->rear  = newjob;
					break;

		default: /* if jobs in queue */
					jobqueue_p->rear->prev = newjob;
					jobqueue_p->rear = newjob;

	}
	jobqueue_p->len++;

	//bsem_post(jobqueue_p->has_jobs);
	pthread_spin_unlock(&jobqueue_p->rwlock);
}


/* Get first job from queue(removes it from queue)
 * Notice: Caller MUST hold a mutex
 */
static struct job* jobqueue_pull(jobqueue* jobqueue_p){

	pthread_spin_lock(&jobqueue_p->rwlock);
	job* job_p = jobqueue_p->front;

	switch(jobqueue_p->len){

		case 0:  /* if no jobs in queue */
		  			break;

		case 1:  /* if one job in queue */
					jobqueue_p->front = NULL;
					jobqueue_p->rear  = NULL;
					jobqueue_p->len = 0;
					break;

		default: /* if >1 jobs in queue */
					jobqueue_p->front = job_p->prev;
					jobqueue_p->len--;
					/* more than one job in queue -> post it */
					//bsem_post(jobqueue_p->has_jobs);

	}

	pthread_spin_unlock(&jobqueue_p->rwlock);
	return job_p;
}


/* Free all queue resources back to the system */
static void jobqueue_destroy(jobqueue* jobqueue_p){
	jobqueue_clear(jobqueue_p);
	//free(jobqueue_p->has_jobs);
}


bool thread_queue_pop_nolock(thpool_* thpool_p, int thr_curid) {
	int i = 0;
	void (*func_buff)(void*);
	void*  arg_buff;
	bool have_e = false;
	for (i=0; i<thpool_p->producer_thr_max; i++) {
		if ((i >= thpool_p->self_thr_index_min) && (i <= thpool_p->self_thr_index_max))
			continue;
		/* Read job from queue and execute it */
		job* job_p = job_queue_pop(thpool_p->jobqueues_nolock[i][thr_curid], 0);
		if (job_p != NULL) {
#if __DEBUG_SUBMIT_NOLOCK
			printf("thread_id: %d, got job_p\n", thr_curid);
#endif
			func_buff = job_p->function;
			arg_buff  = job_p->arg;

			// run job_func
			func_buff(arg_buff);

			free(job_p);
			have_e = true;
		} else {
#if __DEBUG_SUBMIT_NOLOCK
			printf("thread_id: %d, jobqueues_nolock[%d][%d], no job\n", thread_id, i, thr_curid);
#endif
		}
	}
	return have_e;
}
int thpool_get_curthreadid() {
	return 	(thread_cur != NULL) ? thread_cur->pthread_index : 0;
}
/* ======================== SYNCHRONISATION ========================= */


///* Init semaphore to 1 or 0 */
//static void bsem_init(bsem *bsem_p, int value) {
//	if (value < 0 || value > 1) {
//		err("bsem_init(): Binary semaphore can take only values 1 or 0");
//		exit(1);
//	}
//	pthread_mutex_init(&(bsem_p->mutex), NULL);
//	pthread_cond_init(&(bsem_p->cond), NULL);
//	bsem_p->v = value;
//}
//
//
///* Reset semaphore to 0 */
//static void bsem_reset(bsem *bsem_p) {
//	pthread_mutex_destroy(&(bsem_p->mutex));
//	pthread_cond_destroy(&(bsem_p->cond));
//	bsem_init(bsem_p, 0);
//}
//
//
///* Post to at least one thread */
//static void bsem_post(bsem *bsem_p) {
//	pthread_mutex_lock(&bsem_p->mutex);
//	bsem_p->v = 1;
//	pthread_cond_signal(&bsem_p->cond);
//	pthread_mutex_unlock(&bsem_p->mutex);
//}
//
//
///* Post to all threads */
//static void bsem_post_all(bsem *bsem_p) {
//	pthread_mutex_lock(&bsem_p->mutex);
//	bsem_p->v = 1;
//	pthread_cond_broadcast(&bsem_p->cond);
//	pthread_mutex_unlock(&bsem_p->mutex);
//}
//
//
///* Wait on semaphore until semaphore has value 0 */
//static void bsem_wait(bsem* bsem_p) {
//	pthread_mutex_lock(&bsem_p->mutex);
//	while (bsem_p->v != 1) {
//		pthread_cond_wait(&bsem_p->cond, &bsem_p->mutex);
//	}
//	bsem_p->v = 0;
//	pthread_mutex_unlock(&bsem_p->mutex);
//}
