#include "etcdwatcher.h"
#include <map>
#include <mutex>
#include <memory>
#include <string>
#include "etcdapi.h"

extern "C" {
#include "log.h"
}
// --------------------------------- 内部常量 --------------------------------
#define CHECK_ETCD_INTERVAL 30    // 检查间隔时间（秒）
//#define GET_RESPONSE_TIMEOUT 5    // 超时时间（秒）

// ------------------------------ 内部类型/变量 -------------------------------
struct WatcherObject {
    std::shared_ptr<etcd::Watcher> wp;
    etcdwatcher_event_callback     cb;
};

static std::shared_ptr<etcd::Client>         s_client;
static std::map<std::string, WatcherObject>  s_mapWatchers;
static std::recursive_mutex                  s_wmutex;
static bool                                  s_inited = false;

static std::atomic<bool> s_timer_running(false);
static std::thread       s_timer_thread;

// 前置声明
static void etcdwatcher_handle_response(etcd::Response resp);
static int  etcdwatcher_rewatch();


// --------------------------------- 工具函数 --------------------------------
static void wait_for_connection(const std::string &endpoints)
{
	while (true) {
		try {
			std::string cmd = "etcdctl endpoint health --endpoints=" + endpoints + " 2>&1";

			// 使用popen获取命令输出
			FILE* pipe = popen(cmd.c_str(), "r");
			if (!pipe) continue;

			char buffer[2048];
			std::string result;
			while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
				result += buffer;
			}

			pclose(pipe);

			// 检查命令执行成功且输出包含"successfully"
			if ((result.find("successfully") != std::string::npos))
				break;
	    } catch (...) {
			/* ignore */
		}
		::sleep(1);
		LogWarn(COMPONENT_DATABASE, "endpoints not ready, retry...");
	}
	LogWarn(COMPONENT_DATABASE, "endpoints become available.");
}


/**
 * @brief 当 etcd 有变更时，会执行 etcd::Watcher 的 callback。我们统一在这里处理，再去找对应的用户回调。
 */
static void etcdwatcher_handle_response(etcd::Response resp)
{
    if (!resp.is_ok()) {
        // 说明 request 或者 watcher 出了问题
        LogWarn(COMPONENT_DATABASE, "response error.");
        return;
    }

    // 遍历本次事件所有变动
    for (auto const &ev : resp.events()) {
        // prev_kv() 表示变更前的 key-value，kv() 表示变更后的
        std::string prev_key   = ev.prev_kv().key();
        std::string curr_key   = ev.kv().key();
        std::string curr_value = ev.kv().as_string();

        // 有时 etcd 的 watch 事件里 prev_kv.key() 为空，导致找不到 watchers，需要做一些兼容。
        // 若 prev_key 为空，就直接用当前的 key
        if (prev_key.empty()) {
            prev_key = curr_key;
        }

		//会自动锁定传入的互斥锁, 对象超出其作用域时，它会自动解锁对应的互斥锁。
        std::lock_guard<std::recursive_mutex> locker(s_wmutex);

        // 查找这个 key 是否在 s_mapWatchers 里
        auto it = s_mapWatchers.find(prev_key);
        if (it == s_mapWatchers.end()) {
            // 没找到，说明我们没 watch prev_key
            // 但也有可能 curr_key != prev_key, watch map 里存的是 curr_key
            // 可以自行决定要不要再找 curr_key
            continue;
        }

        // 如果找到了对象，就拿到回调函数
        auto cb = it->second.cb;
        if (!cb) {
            // 没有回调可执行？
            continue;
        }

        // 如果这次变动使得 key 改名了(极端情况)
        if (curr_key != prev_key) {
            // 复制 watcher object
            s_mapWatchers[curr_key] = it->second;
            // 删除旧 key
            s_mapWatchers.erase(it);
        }

        // 最终调用回调
        cb(curr_key.c_str(), curr_value.c_str());
    }
}

// ------------------------------- 健康检查逻辑 -------------------------------
static int etcdwatcher_check_connect()
{
	/*bool need_rewatch = false;
	{
		std::lock_guard<std::recursive_mutex> lk(s_wmutex);
		if (!s_client) return -1;          // 未初始化
	}

	try {
		auto start = std::chrono::steady_clock::now();
		auto head  = s_client->head().get(); // ping
		auto dur   = std::chrono::steady_clock::now() - start;
		if (!head.is_ok() || dur > std::chrono::seconds(GET_RESPONSE_TIMEOUT)) {
			need_rewatch = true;
		}
	} catch (const std::exception &e) {
		LogWarn(COMPONENT_DATABASE, "exception pinging etcd");
		need_rewatch = true;
	}*/
	
	
	if(!s_client.get()){
		LogWarn(COMPONENT_DATABASE, "etcdclient is not set!\n");
		return 0;
	}

	bool need_rewatch = false;
	static int last_exit_status = 0;  // 上次命令执行的退出状态

	try {
		std::lock_guard<std::recursive_mutex> lk(s_wmutex);
		//使用system执行一个命令，并且拿到其结果，当结果和上一次check_connect执行的结果不同时，need_rewatch置为true
		const std::string cluster_name = "database";
		std::string auth_stat, etcd_url;
		int ret = get_endpoint_url(cluster_name, etcd_url, auth_stat);
		if(0 != ret) {
			LogWarn(COMPONENT_DATABASE, "can not get endpoint url\n");
			return -1;
		}
		std::string cmd = "etcdctl endpoint health --endpoints=" + etcd_url + " >/dev/null 2>&1";
		int current_status = system(cmd.c_str());
		current_status = (WIFEXITED(current_status) && WEXITSTATUS(current_status) == 0) ? 0 : 1;
		if(current_status != last_exit_status) {
			need_rewatch = true;
			LogWarn(COMPONENT_DATABASE, "Etcd health status changed from %s to %s\n",
				(last_exit_status == 0) ? "healthy" : "unhealthy",
				(current_status == 0) ? "healthy" : "unhealthy");
				last_exit_status = current_status;
			}
		}catch(const std::exception& e) {
		LogWarn(COMPONENT_DATABASE, "Exception occurred: %s, reinitializing watcher.\n", e.what());
		need_rewatch = true;
	}

	if (need_rewatch) {
		LogWarn(COMPONENT_DATABASE, "[etcdwatcher] connection unhealthy, rewatching");
		etcdwatcher_rewatch();
	}
	return 0;
}

// 重新创建 client + watchers
static int etcdwatcher_rewatch()
{
    std::map<std::string, WatcherObject> old_map;
    {
        std::lock_guard<std::recursive_mutex> lk(s_wmutex);
        old_map.swap(s_mapWatchers);
        if (s_client) s_client.reset();
    }

    // 重新获取 endpoint
    std::string etcd_url, auth_stat;
    if (get_endpoint_url("database", etcd_url, auth_stat) != 0 || etcd_url.empty()) {
        LogWarn(COMPONENT_DATABASE, "failed to get endpoint url when rewatching");
        return -1;
    }
    wait_for_connection(etcd_url);

    {
        std::lock_guard<std::recursive_mutex> lk(s_wmutex);
        if (auth_stat == "true") {
            std::string ca, cert, key;
            get_cert_path("database", ca, cert, key);
            s_client = std::make_shared<etcd::Client>(etcd_url, ca, cert, key, "etcd");
        } else {
            s_client = std::make_shared<etcd::Client>(etcd_url);
        }
    }

    // 恢复 watchers
    auto response_cb = [&](etcd::Response resp){ etcdwatcher_handle_response(resp); };
    for (auto &kv : old_map) {
        const std::string &key = kv.first;
        etcdwatcher_event_callback cb = kv.second.cb;
        std::shared_ptr<etcd::Watcher> wp = std::make_shared<etcd::Watcher>(*s_client, key, response_cb, false);
        if (!wp) continue;

        std::lock_guard<std::recursive_mutex> lk(s_wmutex);
        s_mapWatchers[key] = { wp, cb };
    }
    LogWarn(COMPONENT_DATABASE, "rewatch done");
    return 0;
}

// ------------------------------- 外部接口 -------------------------------
int etcdwatcher_init(void)
{
    std::lock_guard<std::recursive_mutex> locker(s_wmutex);
    if (s_inited) {
        return 0; // 已经初始化过
    }

    // 1. 从 etcdapi 里获取 endpoints
    std::string etcd_url, auth_stat;
    int ret = get_endpoint_url("database", etcd_url, auth_stat);
    if (ret != 0) {
        return -1;  // 获取失败
    }
    if (etcd_url.empty()) {
        return -2;  // 未配置
    }

    // 2. 根据是否开启 client-cert-auth，创建 etcd::Client
    if (auth_stat == "true") {
        std::string ca_path, cert_path, key_path;
        get_cert_path("database", ca_path, cert_path, key_path);
        s_client = std::make_shared<etcd::Client>(etcd_url, ca_path, cert_path, key_path, "etcd");
    } else {
        s_client = std::make_shared<etcd::Client>(etcd_url);
    }

    if (!s_client) {
        return -3;  // 创建 client 失败
    }

    s_inited = true;
    return 0;
}

int etcdwatcher_start_watch(const char *key, etcdwatcher_event_callback cb)
{
    if (!key || !*key || !cb) {
        return -1;  // 参数无效
    }
    std::lock_guard<std::recursive_mutex> locker(s_wmutex);

    if (!s_inited || !s_client) {
        return -2;  // 未 init
    }

    // 如果已经在 watch 这个 key，就不重复 watch 了
    auto it = s_mapWatchers.find(key);
    if (it != s_mapWatchers.end()) {
        return 0;   // 已经在 watch
    }

    // 构造 etcd::Watcher，第 4 个参数 true 表示 watch on prefix，如需只 watch 具体 key，可以设 false
    auto response_cb = [&](etcd::Response resp){
        etcdwatcher_handle_response(resp);
    };

    std::shared_ptr<etcd::Watcher> watcher = std::make_shared<etcd::Watcher>(*s_client, key, response_cb, false);
    if (!watcher) {
        return -3;  // 创建 watcher 失败
    }

    // 保存进 map
    WatcherObject wobj;
    wobj.wp = watcher;
    wobj.cb = cb;
    s_mapWatchers[key] = wobj;

	// 启动定时线程
    bool expected = false;
    if (s_timer_running.compare_exchange_strong(expected, true)) {
        s_timer_thread = std::thread([](){
            while (s_timer_running.load()) {
                ::sleep(CHECK_ETCD_INTERVAL);
                etcdwatcher_check_connect();
            }
        });
    }
	
    return 0;
}

int etcdwatcher_destroy(void)
{
    std::lock_guard<std::recursive_mutex> locker(s_wmutex);

    // 取消所有 watch
    for (auto &kv : s_mapWatchers) {
        auto &wp = kv.second.wp;
        if (wp) {
            wp->Cancel();
        }
    }
    s_mapWatchers.clear();

    // 释放 client
    s_client.reset();
    s_inited = false;

	// 停止计时线程
    if (s_timer_running.exchange(false) && s_timer_thread.joinable()) {
        s_timer_thread.join();
    }
    return 0;
}
