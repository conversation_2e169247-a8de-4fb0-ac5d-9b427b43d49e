/*                                                                            */
/* Copyright (C) 2017 International Business Machines                         */
/* All rights reserved.                                                       */
/*                                                                            */
/* Redistribution and use in source and binary forms, with or without         */
/* modification, are permitted provided that the following conditions         */
/* are met:                                                                   */
/*                                                                            */
/*  1. Redistributions of source code must retain the above copyright notice, */
/*     this list of conditions and the following disclaimer.                  */
/*  2. Redistributions in binary form must reproduce the above copyright      */
/*     notice, this list of conditions and the following disclaimer in the    */
/*     documentation and/or other materials provided with the distribution.   */
/*  3. The name of the author may not be used to endorse or promote products  */
/*     derived from this software without specific prior written              */
/*     permission.                                                            */
/*                                                                            */
/* THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR       */
/* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES  */
/* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.    */
/* IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL*/
/* SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO*/
/* PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;*/
/* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,   */
/* WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR    */
/* OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF     */
/* ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.                                 */
/*                                                                            */
/* %Z%%M%       %I%  %W% %G% %U% */

#include "config.h"
#include "gsh_list.h"
#ifdef USE_DBUS
#include "gsh_dbus.h"
#endif
#include <abstract_atomic.h>
#include "fsal.h"
#include "nfs_proto_functions.h"

#define _IDFS_TRUNCATE 40

struct fsal_op_stats idfs_op_stats[_IDFS_TRUNCATE+1];
struct fsal_stats idfs_stats;
__thread struct timespec start_time;
__thread struct timespec stop_time;


#ifdef USE_DBUS
/**
 *  * @brief Return string for gpfs opcode
 *   *
 *    * @param[in] gpfs opcode
 *     */

struct op_name {
	char *name;
};

//add by wangyingjie for stat tool
static const struct op_name idfs_optabv3[] = {
	[IDFS_NULL] = {.name = "NULL", },
	[IDFS_LOOKUP]  = {.name = "ll_lookup", },
	[IDFS_CREATE] = {.name = "ll_create", },
	[IDFS_MKDIR] = {.name = "ll_mkdir", },
	[IDFS_PUT] = {.name = "ll_put", },
	[IDFS_OPENDIR] = {.name = "ll_opendir", },
	[IDFS_READDIRPLUS_R] = {.name = "readdirplus_r", }, //add by zanglinjie
	[IDFS_RELEASEDIR] = {.name = "ll_releasedir", },
	[IDFS_SYMLINK] = {.name = "ll_symlink", },
	[IDFS_READLINK] = {.name = "ll_readlink", },
	[IDFS_GETATTR] = {.name = "ll_getattr", },
	[IDFS_SETATTR] = {.name = "ll_setattr", },
	[IDFS_LINK] = {.name = "ll_link", },
	[IDFS_RENAME] = {.name = "ll_rename", },
	[IDFS_UNLINK] = {.name = "ll_unlink", },
	[IDFS_RMDIR] = {.name = "ll_rmdir", },
	[IDFS_OPEN] = {.name = "ll_open", },
	[IDFS_READ] = {.name = "ll_read", },
	[IDFS_WRITE] = {.name = "ll_write", },
	[IDFS_CLOSE] = {.name = "ll_close", },
	[IDFS_GET_INODE] = {.name = "ll_get_inode", },
	[IDFS_LOOKUP_INODE] = {.name = "ll_lookup_inode", },
	[IDFS_STATE_FS] = {.name = "ll_state_fs", },
	[IDFS_FSYNC] = {.name = "ll_fsync", },
	[IDFS_MKNOD] = {.name = "ll_mknod", },
	[IDFS_SETLK] = {.name = "ll_setlk", },
	[IDFS_GETLK] = {.name = "ll_getlk", },
	[IDFS_SETXATTR] = {.name = "ll_setxattr", },
	[IDFS_GETXATTR] = {.name = "ll_getxattr", },
	[IDFS_REMOVEXATTR] = {.name = "ll_removexattr"},
	[IDFS_FSYNC_INODE] = {.name = "ll_fsync_inode"},
	[IDFS_DELEGATION] = {.name = "ll_delegation"},
	[IDFS_FALLOCATE] = {.name = "ll_fallocate"},
	[IDFS_READV] = {.name = "ll_readv", },
	[IDFS_WRITEV] = {.name = "ll_writev", },
	[IDFS_TRANS_USERMAP] = {.name = "ll_trans_usermap", },
        [IDFS_CHECK_PERMISSIONS] = {.name = "ll_check_permissions", },
	[IDFS_NTFS_ACL_TO_UNIX_ACL] = {.name = "ll_ntfs_acl_to_unix_acl", },
	[IDFS_UID_2_GIDS] = {.name = "ll_uid_2_gids", },
	[IDFS_FREE_GIDS] = {.name = "ll_free_gids", },
	[IDFS_TRUNCATE] = {.name = "ll_truncate", },
};

#endif  /* USE_DBUS */

/** @fn prepare_for_stats(struct fsal_module *fsal_hdl)
 *  *  @brief prepare the structure which will hold the stats
 *   */
void prepare_for_stats(struct fsal_module *fsal_hdl)
{
	idfs_stats.total_ops = _IDFS_TRUNCATE+1;
	idfs_stats.op_stats = idfs_op_stats;
	fsal_hdl->stats = &idfs_stats;
}

#ifdef USE_DBUS
/** @fn fsal_gpfs_extract_stats(struct fsal_module *fsal_hdl, void *iter)
 *  *  @brief Extract the FSAL specific performance counters
 *   */
void fsal_idfs_extract_stats(struct fsal_module *fsal_hdl, void *iter)
{
	DBusMessageIter struct_iter;
	DBusMessageIter *iter1 = (DBusMessageIter *)iter;
	char *message;
	uint64_t receive_total_ops, total_ops, total_resp, min_resp, max_resp, op_counter = 0;
	double res = 0.0;
	int i;
	struct fsal_stats *idfs_stats;

	idfs_stats = fsal_hdl->stats;
	message = "IDFS";
	dbus_message_iter_append_basic(iter1, DBUS_TYPE_STRING, &message);

	dbus_message_iter_open_container(iter1, DBUS_TYPE_STRUCT, NULL,
					 &struct_iter);
	for (i = 0; i <= IDFS_TRUNCATE; i++) {
		
		receive_total_ops = atomic_fetch_uint64_t(
				&idfs_stats->op_stats[i].receive_num_ops);

		total_ops = atomic_fetch_uint64_t(
				&idfs_stats->op_stats[i].num_ops);
		if (receive_total_ops == 0)
			continue;

		total_resp = atomic_fetch_uint64_t(
				&idfs_stats->op_stats[i].resp_time);
		min_resp = atomic_fetch_uint64_t(
				&idfs_stats->op_stats[i].resp_time_min);
		max_resp = atomic_fetch_uint64_t(
				&idfs_stats->op_stats[i].resp_time_max);
		/* We have valid stats, send it across */
		message = idfs_optabv3[i].name;
		dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_STRING, &message);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &receive_total_ops);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &total_ops);
		res = (double) total_resp * 0.000001 / total_ops;
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		res = (double) min_resp * 0.000001;
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		res = (double) max_resp * 0.000001;
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		op_counter += total_ops;
	}
	{
		receive_total_ops = atomic_fetch_uint64_t(
				&idfs_stats->op_total_stats.receive_num_ops);

		total_ops = atomic_fetch_uint64_t(
				&idfs_stats->op_total_stats.num_ops);
		if (total_ops != 0){
			total_resp = atomic_fetch_uint64_t(
					&idfs_stats->op_total_stats.resp_time);
			min_resp = atomic_fetch_uint64_t(
					&idfs_stats->op_total_stats.resp_time_min);
			max_resp = atomic_fetch_uint64_t(
					&idfs_stats->op_total_stats.resp_time_max);
			/* We have valid stats, send it across */
			message = "Total";
			dbus_message_iter_append_basic(&struct_iter,
					DBUS_TYPE_STRING, &message);
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_UINT64, &receive_total_ops);
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_UINT64, &total_ops);
			res = (double) total_resp * 0.000001 / total_ops;
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_DOUBLE, &res);
			res = (double) min_resp * 0.000001;
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_DOUBLE, &res);
			res = (double) max_resp * 0.000001;
			dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_DOUBLE, &res);
		}
	}
	if (op_counter == 0) {
		message = "None";
		/* insert dummy stats to avoid dbus crash */
		dbus_message_iter_append_basic(&struct_iter,
				DBUS_TYPE_STRING, &message);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &receive_total_ops);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_UINT64, &total_ops);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
		dbus_message_iter_append_basic(&struct_iter,
			DBUS_TYPE_DOUBLE, &res);
	} else {
		message = "OK";
	}
	dbus_message_iter_close_container(iter1, &struct_iter);
	dbus_message_iter_append_basic(iter, DBUS_TYPE_STRING, &message);
}
#endif   /* USE_DBUS */

void fsal_idfs_reset_stats(struct fsal_module *fsal_hdl)
{
	int i;
	struct fsal_stats *idfs_stats;

	idfs_stats = fsal_hdl->stats;

	/* reset all the counters */
	for (i = 0; i <= IDFS_TRUNCATE; i++) {
		atomic_store_uint64_t(
				&idfs_stats->op_stats[i].receive_num_ops, 0);
		atomic_store_uint64_t(
				&idfs_stats->op_stats[i].num_ops, 0);
		atomic_store_uint64_t(
				&idfs_stats->op_stats[i].resp_time, 0);
		atomic_store_uint64_t(
				&idfs_stats->op_stats[i].resp_time_min, 0);
		atomic_store_uint64_t(
				&idfs_stats->op_stats[i].resp_time_max, 0);
	}
	
	atomic_store_uint64_t(
			&idfs_stats->op_total_stats.receive_num_ops, 0);
	atomic_store_uint64_t(
			&idfs_stats->op_total_stats.num_ops, 0);
	atomic_store_uint64_t(
			&idfs_stats->op_total_stats.resp_time, 0);
	atomic_store_uint64_t(
			&idfs_stats->op_total_stats.resp_time_min, 0);
	atomic_store_uint64_t(
			&idfs_stats->op_total_stats.resp_time_max, 0);
}
void stat_idfs_begin(uint32_t idfs_ops)
{
        //if (nfs_param.core_param.enable_FSALSTATS) {
        (void)atomic_inc_uint64_t(&idfs_stats.op_stats[idfs_ops].receive_num_ops);
        (void)atomic_inc_uint64_t(&idfs_stats.op_total_stats.receive_num_ops);
        /* Collect FSAL stats */
        now(&start_time);
        //}
        return;
}
uint64_t  stat_idfs_end(uint32_t idfs_ops)
{
        struct timespec stop_time;
        nsecs_elapsed_t resp_time = 0;
	uint64_t tmptime1 = 0;

        //if (nfs_param.core_param.enable_FSALSTATS) {

        now(&stop_time);
        resp_time = timespec_diff(&start_time, &stop_time);

        /* record FSAL stats */
        (void)atomic_inc_uint64_t(&idfs_stats.op_stats[idfs_ops].num_ops);
        (void)atomic_inc_uint64_t(&idfs_stats.op_total_stats.num_ops);

        (void)atomic_add_uint64_t(&idfs_stats.op_stats[idfs_ops].resp_time,
                resp_time);
        (void)atomic_add_uint64_t(&idfs_stats.op_total_stats.resp_time,
                resp_time);

        if (idfs_stats.op_stats[idfs_ops].resp_time_max < resp_time)
                idfs_stats.op_stats[idfs_ops].resp_time_max = resp_time;
        if (idfs_stats.op_stats[idfs_ops].resp_time_min == 0 ||
                idfs_stats.op_stats[idfs_ops].resp_time_min > resp_time)
                idfs_stats.op_stats[idfs_ops].resp_time_min = resp_time;
        if (idfs_stats.op_total_stats.resp_time_max < resp_time)
                idfs_stats.op_total_stats.resp_time_max = resp_time;
        if (idfs_stats.op_total_stats.resp_time_min == 0 ||
                idfs_stats.op_total_stats.resp_time_min > resp_time)
                idfs_stats.op_total_stats.resp_time_min = resp_time;


        //}
	tmptime1 = resp_time/1000000000;
	return tmptime1;

}

