#ifndef PROBE_stats_H
#define PROBE_stats_H
typedef enum {
	RPC_NULL = 0,
	RPCRDMA_WAIT,
	RPC_fabric_decode,
	RPC_fabric_readv,
	RPC_fabric_r_waitcb,
	RPC_fabric_writev,
	RPC_fabric_w_waitcb,
	RPC_fabric_send,
	RPC_fabric_s_waitcb,
	RPC_fabric_decode_readchunk,
	RPC_fabric_worksubmit,
	RPC_fabric_worksubmit_wait,
	NFS_process,
	RPC_fabric_reply,
	RPC_fabric_done,
	SVC_fabric_request,
	NFS_process_request,
	NFS_dupreq_start,
	NFS_service_function,
	NFS3_write,
	NFS3_access_null,
	NFS3_access_slow,
	NFS3_getattr_fhcache,
	NFS3_write_fhcache,
	NFS3_read_fhcache,
	NFS3_access_fhcache,
	RPC_fabric_r_cq,
	RPC_fabric_w_cq,
	RPC_fabric_s_cq,
	RPC_fabric_r_cq_cb,
	RPC_fabric_w_cq_cb,
	RPC_fabric_s_cq_cb,
	R<PERSON>_fabric_recv_cq_cb,
	RPC_fabric_send_handle,
	RPC_fabric_write_handle,
	RPC_fabric_send_handle_cb,
	RPC_fabric_write_handle_cb,
	PROBE_DELAY_MAX,
} PROBE_DELAY;

struct probe_op_delay_name {
	char *name;
};

/**
 * @brief 
 * implement 
 */
struct probe_op_delay_stats {
	char *op_name;
	uint16_t op_code;
	uint64_t resp_time;
	uint64_t resp_time_max;
	uint64_t resp_time_min;
	uint64_t receive_num_ops;    //request_count
	uint64_t num_ops;            //handled_count
};
struct probe_stats_delay {
	uint16_t total_ops;
	struct probe_op_delay_name probe_name[PROBE_DELAY_MAX];
	struct probe_op_delay_stats op_stats[PROBE_DELAY_MAX];
	struct probe_op_delay_stats op_total_stats;
};

static inline void probe_ops_delay_init(struct probe_stats_delay * probe_stat) {
	struct probe_op_delay_name *p_name = probe_stat->probe_name;

	p_name[RPC_NULL].name = "NULL";
	p_name[RPCRDMA_WAIT].name = "rdma_wait";
	p_name[RPC_fabric_reply].name = "rpc_fabric_reply";
	p_name[RPC_fabric_decode].name = "rpc_fabric_decode";
	p_name[RPC_fabric_send].name = "fabric_send";
	p_name[RPC_fabric_s_waitcb].name = "fabric_send_whole";
	p_name[RPC_fabric_readv].name = "fabric_readv";
	p_name[RPC_fabric_r_waitcb].name = "fabric_readv_whole";
	p_name[RPC_fabric_done].name = "rpc_fabric_done";
	p_name[RPC_fabric_writev].name = "fabric_writev";
	p_name[RPC_fabric_w_waitcb].name = "fabric_write_whole";
	p_name[RPC_fabric_decode_readchunk].name = "rpc_f_decodev";
	p_name[RPC_fabric_worksubmit].name = "rpc_f_worksubmit";
	p_name[RPC_fabric_worksubmit_wait].name = "rpc_f_submit_waitcb";
	p_name[NFS_process].name = "nfs_request";
	p_name[SVC_fabric_request].name = "svc_fabric_request";
	p_name[NFS_process_request].name = "nfs_rpc_process_request";
	p_name[NFS_dupreq_start].name = "nfs_dupreq_start";
	p_name[NFS3_write].name = "nfs3_write";
	p_name[NFS3_access_null].name = "nfs3_access_null";
	p_name[NFS3_access_slow].name = "nfs3_access_slow";
	p_name[NFS_service_function].name = "service_function";
	p_name[NFS3_getattr_fhcache].name = "nfs3_getattr_fhcache";
	p_name[NFS3_write_fhcache].name = "nfs3_write_fhcache";
	p_name[NFS3_read_fhcache].name = "nfs3_read_fhcache";
	p_name[NFS3_access_fhcache].name = "nfs3_access_fhcache";
	p_name[RPC_fabric_r_cq].name = "net_fabric_r_cq";
	p_name[RPC_fabric_w_cq].name = "net_fabric_w_cq";
	p_name[RPC_fabric_s_cq].name = "net_fabric_s_cq";
	p_name[RPC_fabric_r_cq_cb].name = "net_fabric_r_cq_cb";
	p_name[RPC_fabric_w_cq_cb].name = "net_fabric_w_cq_cb";
	p_name[RPC_fabric_s_cq_cb].name = "net_fabric_s_cq_cb";
	p_name[RPC_fabric_recv_cq_cb].name = "net_fabric_recv_cq_cb";
	p_name[RPC_fabric_send_handle].name = "net_fabric_send_handle";
	p_name[RPC_fabric_write_handle].name = "net_fabric_write_handle";
	p_name[RPC_fabric_send_handle_cb].name = "net_fabric_send_handle_cb";
	p_name[RPC_fabric_write_handle_cb].name = "net_fabric_write_handle_cb";
	return ;
}


typedef enum {
	PROBE_COUNT_NULL = 0,
	PROBE_COUNT_RDMA_WORKTHRNUM,
	PROBE_COUNT_RDMA_MEM_IN_ALLOC,
	PROBE_COUNT_RDMA_MEM_IN_USED,
	PROBE_COUNT_RDMA_MEM_IN_UNUSED,
	PROBE_COUNT_RDMA_MEM_OUT_ALLOC,
	PROBE_COUNT_RDMA_MEM_OUT_USED,
	PROBE_COUNT_RDMA_MEM_OUT_UNUSED,
	PROBE_COUNT_RDMA_CBC_ALLOC,
	PROBE_COUNT_RDMA_CBC_USED,
	PROBE_COUNT_RDMA_CBC_UNUSED,
	PROBE_COUNT_NFSREQ_ALLOC,
	PROBE_COUNT_NFSDUPREQ_ALLOC,
	PROBE_COUNT_NFSRES_ALLOC,
	PROBE_COUNT_TIRPC_MALLOC,
	PROBE_COUNT_TIRPC_FREE,
	PROBE_COUNT_TIRPC_ALIGNED,
	PROBE_COUNT_TIRPC_CALLOC,
	PROBE_COUNT_XDR_BYTES_DECODE,
	PROBE_COUNT_XDR_BYTES_FREE,
	PROBE_COUNT_FAB_CTX,
	PROBE_COUNT_FAB_CH,
	PROBE_COUNT_FAB_IOV,
	PROBE_COUNT_NFS3_WRITE_DATA,
	PROBE_COUNT_FAB_CHUNK_COUNT,
	PROBE_COUNT_THR_RX_SEND,
	PROBE_COUNT_THR_TX_SEND,
	PROBE_COUNT_THR_WORK_SEND,
	PROBE_COUNT_EPS,
	PROBE_COUNT_WAITDESTORY,
	PROBE_COUNT_RDMAXPRT,
	PROBE_COUNT_DUP_REQ,
	PROBE_COUNT_MAX,
} PROBE_COUNT;

struct probe_op_cout {
	char *name;
	uint64_t count;
};

struct probe_stats_count {
	uint16_t total_ops;
	struct probe_op_cout op_stats[PROBE_COUNT_MAX];
};

static inline void probe_ops_count_init(struct probe_stats_count * probe_cout) {

	struct probe_op_cout *p_stats = probe_cout->op_stats;

	p_stats[PROBE_COUNT_NULL].name = "NULL";
	p_stats[PROBE_COUNT_RDMA_WORKTHRNUM].name = "rdma_cur_workthreads";
	p_stats[PROBE_COUNT_RDMA_MEM_IN_ALLOC].name = "rdma_inbuf_alloc";
	p_stats[PROBE_COUNT_RDMA_MEM_IN_ALLOC].name = "rdma_inbuf_alloc";
	p_stats[PROBE_COUNT_RDMA_MEM_IN_USED].name = "rdma_inbuf_used";
	p_stats[PROBE_COUNT_RDMA_MEM_IN_UNUSED].name = "rdma_inbuf_unused";
	p_stats[PROBE_COUNT_RDMA_MEM_OUT_ALLOC].name = "rdma_outbuf_alloc";
	p_stats[PROBE_COUNT_RDMA_MEM_OUT_USED].name =  "rdma_outbuf_used";
	p_stats[PROBE_COUNT_RDMA_MEM_OUT_UNUSED].name = "rdma_outbuf_unused";
	p_stats[PROBE_COUNT_RDMA_CBC_ALLOC].name = "rdma_cbc_alloc";
	p_stats[PROBE_COUNT_RDMA_CBC_USED].name = "rdma_cbc_used";
	p_stats[PROBE_COUNT_RDMA_CBC_UNUSED].name = "rdma_cbc_unused";
	p_stats[PROBE_COUNT_NFSREQ_ALLOC].name = "nfs_request_t";
	p_stats[PROBE_COUNT_NFSDUPREQ_ALLOC].name = "dupreq_entry_t";
	p_stats[PROBE_COUNT_NFSRES_ALLOC].name = "nfs_res_t";
	p_stats[PROBE_COUNT_TIRPC_MALLOC].name = "tirpc_malloc";
	p_stats[PROBE_COUNT_TIRPC_FREE].name = "tirpc_free";
	p_stats[PROBE_COUNT_TIRPC_ALIGNED].name = "tirpc_aligned";
	p_stats[PROBE_COUNT_TIRPC_CALLOC].name = "tirpc_calloc";
	p_stats[PROBE_COUNT_XDR_BYTES_DECODE].name = "xdr_bytes_decode";
	p_stats[PROBE_COUNT_XDR_BYTES_FREE].name = "xdr_bytes_free";
	p_stats[PROBE_COUNT_FAB_CTX].name = "generl_context";
	p_stats[PROBE_COUNT_FAB_CH].name = "conncet_handle";
	p_stats[PROBE_COUNT_FAB_IOV].name = "iovec";
	p_stats[PROBE_COUNT_NFS3_WRITE_DATA].name = "nfs3_write_data";
	p_stats[PROBE_COUNT_FAB_CHUNK_COUNT].name = "fabric_chunk_count";
	p_stats[PROBE_COUNT_THR_RX_SEND].name = "thr_submit_rx";
	p_stats[PROBE_COUNT_THR_TX_SEND].name = "thr_submit_tx";
	p_stats[PROBE_COUNT_THR_WORK_SEND].name = "thr_submit_work";
	p_stats[PROBE_COUNT_EPS].name = "endpoint_count";
	p_stats[PROBE_COUNT_WAITDESTORY].name = "waitdestory_ep_count";
	p_stats[PROBE_COUNT_RDMAXPRT].name = "rdma_xprt";
	p_stats[PROBE_COUNT_DUP_REQ].name = "dup_req_count";
	return ;
}
void print_probe_delay_stats(void *iter);
void print_probe_count_stats(void *iter);
void print_probe_rdma_clients(void *iter);
void print_probe_rdma_del_clients(void *iter);
void probe_reset_stats(void);

#endif
