/*
 * Copyright (c) 2013-2015 CohortFS, LLC.
 * Copyright (c) 2013-2018 Red Hat, Inc. and/or its affiliates.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR `AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/**
 * @file work_pool.h
 * <AUTHOR> Allen Simpson <<EMAIL>>
 * @brief Pthreads-based work queue package
 *
 * @section DESCRIPTION
 *
 * This provides simple work queues using pthreads and TAILQ primitives.
 *
 * @note    Loosely based upon previous thrdpool by
 *          Matt Benjamin <<EMAIL>>
 */

#ifndef WORK_POOL_H
#define WORK_POOL_H

#include <rpc/pool_queue.h>

struct work_pool_params {
	int32_t thrd_max;
	int32_t thrd_min;
	/*add by zhangjiali at 2021.5.24 for threads stats */
	bool enable_thrstats;
};

struct work_pool_thread;

/*each thread state WP_TYPE_XXX,
 *WP_STAT_START: thread is in this state
 *WP_STAT_END:   thread is leaving this state*/
#define WP_STAT_START  TRUE
#define WP_STAT_END    FALSE

typedef enum work_pool_type {
	WP_TYPE_OTHER = 0, /*invalid status*/
	WP_TYPE_EPOLLOOP,  /*handing epoll events state*/
	WP_TYPE_XPRTRECV,  /*socket read and process nfs  state*/
	WP_TYPE_XPRTSEND,  /*socket write status*/
	WP_TYPE_CONDWAIT,  /*thread condwait status*/
	WP_TYPE_REPLAY,    /*nfs replay status*/
	WP_TYPE_WORK_QUEUE,    /*nfs work queue */
	WP_TYPE_MAX,
} work_pool_type_t;

struct thread_stats
{
	uint32_t count;
};

struct work_pool_stats
{
	struct thread_stats thr_stats[WP_TYPE_MAX];
};
struct work_pool {
	struct poolq_head pqh;
	TAILQ_HEAD(work_pool_s, work_pool_thread) wptqh;
	char *name;
	pthread_attr_t attr;
	struct work_pool_params params;
	long timeout_ms;
	uint32_t n_threads;
	uint32_t worker_index;
	/*work_pool mgr svc threas process,
	 *the wp_stats record svc threads state*/
	struct work_pool_stats wp_stats;
};

struct work_pool_entry;

struct work_pool_thread {
	struct poolq_entry pqe;		/*** 1st ***/
	TAILQ_ENTRY(work_pool_thread) wptq;
	pthread_cond_t pqcond;

	struct work_pool *pool;
	struct work_pool_entry *work;
	char worker_name[16];
	pthread_t pt;
	uint32_t worker_index;
	bool wakeup;
};

typedef void (*work_pool_fun_t) (struct work_pool_entry *);

struct work_pool_entry {
	struct poolq_entry pqe;		/*** 1st ***/
	struct work_pool_thread *wpt;
	work_pool_fun_t fun;
	work_pool_type_t type;
	void *arg;
};

int work_pool_init(struct work_pool *, const char *, struct work_pool_params *);
int work_pool_submit(struct work_pool *, struct work_pool_entry *);
int work_pool_shutdown(struct work_pool *);
int thpool_qos_nolock_submit(void (*handle_callback)(void *), void *ctx);
void work_pool_set_thrstats(struct work_pool *pool, work_pool_type_t type, bool is_start);
#endif				/* WORK_POOL_H */
