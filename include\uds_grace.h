/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright 2018 Red Hat, Inc. and/or its affiliates.
 * Author: <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */
#ifndef _UDS_GRACE_H
#define _UDS_GRACE_H

#include <stdbool.h>
#include <stdio.h>

#define DEFAULT_UDS_GRACE_POOL		"gnfs"
#define DEFAULT_UDS_GRACE_OID			"grace"

int uds_grace_create(uds_ioctx_t io_ctx, const char *oid);
int uds_grace_dump(uds_ioctx_t io_ctx, const char *oid, FILE *stream);
int uds_grace_epochs(uds_ioctx_t io_ctx, const char *oid,
			uint64_t *cur, uint64_t *rec);
int uds_grace_enforcing_toggle(uds_ioctx_t io_ctx, const char *oid,
		int nodes, const char * const *nodeids, uint64_t *pcur,
		uint64_t *prec, bool start);
int uds_grace_enforcing_check(uds_ioctx_t io_ctx, const char *oid,
				const char *nodeid);
int uds_grace_join_bulk(uds_ioctx_t io_ctx, const char *oid, int nodes,
		const char * const *nodeids, uint64_t *pcur, uint64_t *prec,
		bool start);
int uds_grace_lift_bulk(uds_ioctx_t io_ctx, const char *oid, int nodes,
		const char * const *nodeids, uint64_t *pcur, uint64_t *prec,
		bool remove);
int uds_grace_add(uds_ioctx_t io_ctx, const char *oid, int nodes,
		    const char * const *nodeids);
int uds_grace_member_bulk(uds_ioctx_t io_ctx, const char *oid, int nodes,
			    const char * const *nodeids);

static inline int
uds_grace_enforcing_on(uds_ioctx_t io_ctx, const char *oid,
			const char *nodeid, uint64_t *pcur, uint64_t *prec)
{
	const char *nodeids[1];

	nodeids[0] = nodeid;
	return uds_grace_enforcing_toggle(io_ctx, oid, 1, nodeids, pcur,
						prec, true);
}

static inline int
uds_grace_enforcing_off(uds_ioctx_t io_ctx, const char *oid,
			const char *nodeid, uint64_t *pcur, uint64_t *prec)
{
	const char *nodeids[1];

	nodeids[0] = nodeid;
	return uds_grace_enforcing_toggle(io_ctx, oid, 1, nodeids, pcur,
						prec, false);
}

static inline int
uds_grace_join(uds_ioctx_t io_ctx, const char *oid, const char *nodeid,
		 uint64_t *pcur, uint64_t *prec, bool start)
{
	const char *nodeids[1];

	nodeids[0] = nodeid;
	return uds_grace_join_bulk(io_ctx, oid, 1, nodeids, pcur, prec,
				     start);
}

static inline int
uds_grace_lift(uds_ioctx_t io_ctx, const char *oid, const char *nodeid,
		 uint64_t *pcur, uint64_t *prec)
{
	const char *nodeids[1];

	nodeids[0] = nodeid;
	return uds_grace_lift_bulk(io_ctx, oid, 1, nodeids, pcur, prec,
					false);
}

static inline int
uds_grace_member(uds_ioctx_t io_ctx, const char *oid,
		       const char *nodeid)
{
	const char *nodeids[1];

	nodeids[0] = nodeid;
	return uds_grace_member_bulk(io_ctx, oid, 1, nodeids);
}

#endif /* _UDS_GRACE_H */
