#ifndef DATABASE_H
#define DATABASE_H

#include <dbi/dbi.h>
#include <pthread.h>

struct db_connection{
    dbi_conn conn;
    dbi_inst inst;
    char host[100];
    char username[100];
    char password[100];
    char dbname[100];
    char port[100];
};

void database_masterip_changed(const char *key, const char *val);
int get_host(char *host, size_t host_size);

int init_database_connection();
int database_reconnect();
void close_database_connection();
bool check_network_error();
int db_query_table(const char *query, dbi_result *result);
int get_tenant_name_by_ip(char *ip_str, const char **tenant_name);

int load_all_ns_audit_from_db(void);
int load_ns_audit_for_tenant(const char *tenant_name);
int load_ns_audit_for_tenant_namespace(const char *tenant_name, const char *ns_name);

void remove_all_namespaces_of_tenant(const char *tenant_name);
int reload_tenant_info(const char *op, const char *tenant_name);
int reload_ns_info(const char *op, const char *tenant_name, const char *ns_name);

#endif
