/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright CEA/DAM/DIF  (2008)
 * contributeur : <PERSON>   <EMAIL>
 *                Thomas LEIBOVICI  <EMAIL>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * ---------------------------------------
 */

/**
 * @file    nfs4_op_putrootfh.c
 * @brief   Routines used for managing the NFS4_OP_PUTROOTFH operation.
 *
 * Routines used for managing the NFS4_OP_PUTROOTFH operation.
 */
#include "config.h"
#include "log.h"
#include "fsal.h"
#include "nfs_core.h"
#include "nfs_convert.h"
#include "nfs_exports.h"
#include "nfs_file_handle.h"
#include "export_mgr.h"
#include "nfs_creds.h"
#include "nfs_proto_functions.h"
#include "database.h"

/**
 *
 * @brief The NFS4_OP_PUTROOTFH operation.
 *
 * This functions handles the NFS4_OP_PUTROOTFH operation in
 * NFSv4. This function can be called only from nfs4_Compound.
 *
 * @param[in]     op   Arguments for nfs4_op
 * @param[in,out] data Compound request's data
 * @param[out]    resp Results for nfs4_op
 *
 * @return per RFC5661, p. 371
 *
 * @see CreateROOTFH4
 *
 */

enum nfs_req_result nfs4_op_putrootfh(struct nfs_argop4 *op,
				      compound_data_t *data,
				      struct nfs_resop4 *resp)
{
	fsal_status_t status = {0, 0};
	struct fsal_obj_handle *file_obj;
	const char *tenant_name = NULL;

	PUTROOTFH4res * const res_PUTROOTFH4 = &resp->nfs_resop4_u.opputrootfh;

	/* First of all, set the reply to zero to make sure
	 * it contains no parasite information
	 */
	memset(resp, 0, sizeof(struct nfs_resop4));
	resp->resop = NFS4_OP_PUTROOTFH;

	/* Clear out current entry for now */
	set_current_entry(data, NULL);
	
	if (nfs_param.core_param.mount_sys_tenant_v4) {
		LogDebug(COMPONENT_NFS_V4,"opctx_local=[%s], opctx_remote=[%s]", op_ctx->deststr, op_ctx->srcstr);
			
		// get tenant name
		int rc = get_tenant_name_by_ip(op_ctx->deststr, &tenant_name);
		if (rc != 0 || !tenant_name) {
			LogCrit(COMPONENT_NFS_V4,
				"get_tenant_name_by_ip failed. local IP = [%s]", op_ctx->deststr);
			res_PUTROOTFH4->status = NFS4ERR_NOENT;
			goto err_out;
		}

		LogDebug(COMPONENT_NFS_V4,"Found tenant name: %s", tenant_name);
	}

	/* Get the root export of the Pseudo FS and release any old export
	 * reference
	 */
	set_op_context_export(get_gsh_export_by_pseudo("/", true));
	
	if (op_ctx->ctx_export == NULL) {
		LogCrit(COMPONENT_EXPORT,
			"Could not get export for Pseudo Root");

		res_PUTROOTFH4->status = NFS4ERR_NOENT;
		goto err_out;
	}

	/* Build credentials */
	res_PUTROOTFH4->status = nfs4_export_check_access(data->req);

	/* Test for access error (export should not be visible). */
	if (res_PUTROOTFH4->status == NFS4ERR_ACCESS) {
		/* Client has no access at all */
		LogDebug(COMPONENT_EXPORT,
			 "Client doesn't have access to Pseudo Root");
		goto err_out;
	}

	if (res_PUTROOTFH4->status != NFS4_OK) {
		LogMajor(COMPONENT_EXPORT,
			 "Failed to get FSAL credentials Pseudo Root");
		goto err_out;
	}

	/* Get the Pesudo Root inode of the mounted on export */
	status = nfs_export_get_root_entry(op_ctx->ctx_export, &file_obj); //root_obj: ref + 1
	if (FSAL_IS_ERROR(status)) {
		LogCrit(COMPONENT_EXPORT,
			"Could not get root inode for Pseudo Root");

		res_PUTROOTFH4->status = nfs4_Errno_status(status);
		goto err_out;
	}
	
	if (nfs_param.core_param.mount_sys_tenant_v4 && tenant_name) {
		struct fsal_obj_handle *tenant_obj = NULL;
		LogDebug(COMPONENT_EXPORT, "Looking up tenant dir:%s in root:'/'", tenant_name);

		status = fsal_lookup(file_obj, tenant_name, &tenant_obj, NULL);  //tenant_obj: ref + 1
		if (FSAL_IS_ERROR(status)) {
			LogCrit(COMPONENT_EXPORT, "Could not find tenant dir:%s in root:'/'", tenant_name);
			file_obj->obj_ops->put_ref(file_obj);
			goto err_out;
		}
		
		file_obj->obj_ops->put_ref(file_obj);	//root_obj: ref - 1
		file_obj = tenant_obj;
		LogDebug(COMPONENT_EXPORT, "Successfully find tenant dir:%s", tenant_name);
	}
	
	LogMidDebug(COMPONENT_NFS_V4, " nfs4_op_putrootfh file_id =%lu, fsal->path %s",
		 file_obj->fileid,  file_obj->fsal->path);

	op_ctx->ctx_export->putrootfh_fileid = file_obj->fileid;

	LogMidDebug(COMPONENT_EXPORT,
		    "Root node %p", data->current_obj);

	set_current_entry(data, file_obj);	//ref + 1

	/* Put our ref */
	file_obj->obj_ops->put_ref(file_obj);  //ref - 1

	/* Convert it to a file handle */
	if (!nfs4_FSALToFhandle(data->currentFH.nfs_fh4_val == NULL,
				&data->currentFH,
				data->current_obj,
				op_ctx->ctx_export)) {
		LogCrit(COMPONENT_EXPORT,
			"Could not get handle for %s", nfs_param.core_param.mount_sys_tenant_v4 && tenant_name ? tenant_name : "Pseudo Root");

		res_PUTROOTFH4->status = NFS4ERR_SERVERFAULT;
		goto err_out;
	}

	LogHandleNFS4("NFS4 PUTROOTFH CURRENT FH: ", &data->currentFH);

	if (nfs_param.core_param.mount_sys_tenant_v4 && tenant_name) { 
		LogDebug(COMPONENT_EXPORT, "Successfully set root FH to tenant: %s", tenant_name);
	}
	
	if (tenant_name){
		free((void *)tenant_name);
		tenant_name = NULL;
	}
	
	res_PUTROOTFH4->status = NFS4_OK;
	return NFS_REQ_OK;
	
err_out:
	if (tenant_name){
		free((void *)tenant_name);
		tenant_name = NULL;
	}
	return NFS_REQ_ERROR;
}				/* nfs4_op_putrootfh */

/**
 * @brief Free memory allocated for PUTROOTFH result
 *
 * This function frees any memory allocated for the result of
 * the NFS4_OP_PUTROOTFH function.
 *
 * @param[in,out] resp nfs4_op results
 */
void nfs4_op_putrootfh_Free(nfs_resop4 *resp)
{
	/* Nothing to be done */
}
