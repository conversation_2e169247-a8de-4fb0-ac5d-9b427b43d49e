/*
 * Copyright (c) 2012-2014 CEA
 * contributeur : <PERSON> <<EMAIL>>
 * contributeur : <PERSON> <<EMAIL>>
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 * - Redistributions of source code must retain the above copyright notice,
 *   this list of conditions and the following disclaimer.
 * - Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 * - Neither the name of Sun Microsystems, Inc. nor the names of its
 *   contributors may be used to endorse or promote products derived
 *   from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

/**
 * \file    rpc_rdma.c
 * \brief   rdma helper
 *
 * This was (very) loosely based on the Mooshika library, which in turn
 * was a mix of diod, rping (librdmacm/examples), and Linux kernel's
 * net/9p/trans_rdma.c (dual BSD/GPL license). No vestiges remain.
 */

#if HAVE_CONFIG_H
#  include "config.h"
#endif

#include <stdio.h>	//printf
#include <limits.h>	//INT_MAX
#include <sys/socket.h> //sockaddr
#include <sys/un.h>     //sockaddr_un
#include <pthread.h>	//pthread_* (think it's included by another one)
#include <semaphore.h>  //sem_* (is it a good idea to mix sem and pthread_cond/mutex?)
#include <arpa/inet.h>  //inet_ntop
#include <netinet/in.h> //sock_addr_in
#include <unistd.h>	//fcntl
#include <fcntl.h>	//fcntl
#include <sys/epoll.h>
#include <urcu-bp.h>

#define EPOLL_SIZE (10)
/*^ expected number of fd, must be > 0 */
#define EPOLL_EVENTS (16)
/*^ maximum number of events per poll */
#define EPOLL_WAIT_MS (1000)
/*^ ms check for rpc_rdma_state.run_count (was 100) */
#define IBV_POLL_EVENTS (16)
/*^ maximum number of events per poll */
#define NSEC_IN_SEC (1000*1000*1000)

#include "misc/portable.h"
#include <rdma/rdma_cma.h>
#include <rpc/types.h>
#include <rpc/xdr.h>
#include <rpc/xdr_ioq.h>
#include <rpc/rpc.h>

#include "misc/abstract_atomic.h"

#include "../svc_internal.h"
#include "../rpc_com.h"
#include "misc/city.h"
#include "../svc_internal.h"
#include "../svc_xprt.h"
#include "../rpc_rdma.h"
#include <rpc/svc_rqst.h>
#include <rpc/svc_auth.h>



#include <rdma/fi_errno.h>
#include <rdma/fi_cm.h>
#include <rdma/fi_rma.h>
#include <assert.h>
#include "../time_utils.h"
#include "rdma.h"
#include "../fabric/fabric.h"
struct xp_ops rpc_fabric_ops;

struct rdma_xprt_class_t rdma_xprt_class;
#define SOCK_NAME_MAX 128

static inline void xdr_fabric_inbuf_uio_release(struct xdr_uio *uio, u_int flags)
{
	struct poolq_head *ioqh = uio->uio_p1;
	struct xdr_ioq_uv *data = IOQU(uio);
	struct poolq_entry *have = &(IOQU(uio)->uvq);
	struct fabric_class *fab_clas = rdma_xprt_class.fab_clas;

	uio->uio_references = 1;	/* keeping one */
	if (data->v.vio_base != NULL) {
		//RDMAXPRT *xd = data->u.uio_p2;
		//fabric_endpoint *ch = xd->fd;
		int i;
		for (i = 0; i < fab_clas->domain_count; i++) {
			if (fab_clas->domains[i] && fab_clas->domains[i]->mpool)
				free_chunk(fab_clas->domains[i]->mpool, data->u.uio_p3);
		}
	}
#if __LOCK_IN_MEM
	pthread_mutex_lock(&ioqh->qmutex);
#endif
	/*xdr_ioq_uv_recycle(uio->uio_p1, &IOQU(uio)->uvq);*/
	if (likely(0 <= ioqh->qcount)) {
		/* positive for buffer(s) */
		ioqh->qcount++;
		TAILQ_INSERT_TAIL(&ioqh->qh, have, q);
	} else {
	
#if __LOCK_IN_MEM_COND_WAIT
		/* negative for waiting worker(s) */
		struct xdr_ioq *wait = _IOQ(TAILQ_FIRST(&ioqh->qh));

		/* added directly to the queue.
		 * no need to lock here, the mutex is the pool _head.
		 */
		(wait->ioq_uv.uvqh.qcount)++;
		TAILQ_INSERT_TAIL(&wait->ioq_uv.uvqh.qh, have, q);

		/* Nota Bene: x_handy was decremented count,
		 * will be zero for last one needed,
		 * then will wrap as unsigned.
		 */
		if (0 < wait->xdrs[0].x_handy--) {
			/* not removed */
			ioqh->qcount--;
		} else {
			TAILQ_REMOVE(&ioqh->qh, &wait->ioq_s, q);
			pthread_cond_signal(&wait->ioq_cond);
		}
#else
		//assert(0);
#endif
	}
#if __LOCK_IN_MEM
	pthread_mutex_unlock(&ioqh->qmutex);
#endif
	return ;

}
static inline void xdr_fabric_outbuf_uio_release(struct xdr_uio *uio, u_int flags)
{
	struct poolq_head *ioqh = uio->uio_p1;
	//struct xdr_ioq_uv *data = IOQU(uio);
	struct poolq_entry *have = &(IOQU(uio)->uvq);

	uio->uio_references = 1;	/* keeping one */

#if __LOCK_OUT_MEM
	pthread_mutex_lock(&ioqh->qmutex);
#endif
	/*xdr_ioq_uv_recycle(uio->uio_p1, &IOQU(uio)->uvq);*/
	if (likely(0 <= ioqh->qcount)) {
		/* positive for buffer(s) */
		ioqh->qcount++;
		TAILQ_INSERT_TAIL(&ioqh->qh, have, q);
	} else {
#if __LOCK_OUT_MEM_COND_WAIT
		/* negative for waiting worker(s) */
		struct xdr_ioq *wait = _IOQ(TAILQ_FIRST(&ioqh->qh));

		/* added directly to the queue.
		 * no need to lock here, the mutex is the pool _head.
		 */
		(wait->ioq_uv.uvqh.qcount)++;
		TAILQ_INSERT_TAIL(&wait->ioq_uv.uvqh.qh, have, q);

		/* Nota Bene: x_handy was decremented count,
		 * will be zero for last one needed,
		 * then will wrap as unsigned.
		 */
		if (0 < wait->xdrs[0].x_handy--) {
			/* not removed */
			ioqh->qcount--;
		} else {
			TAILQ_REMOVE(&ioqh->qh, &wait->ioq_s, q);
			pthread_cond_signal(&wait->ioq_cond);
		}
#else
	//	assert(0);
#endif
	}
#if __LOCK_OUT_MEM
	pthread_mutex_unlock(&ioqh->qmutex);
#endif
	return ;
}


void
rdma_ioq_uv_release_sendbufq(struct xdr_ioq_uv *uv)
{
	if (!(--uv->u.uio_references)) {
		if (uv->u.uio_release) {
			/* handle both xdr_ioq_uv and vio */
			uv->u.uio_release(&uv->u, UIO_FLAG_NONE);
		} else {
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"%s() memory leak, no release flags (%u)\n",
				__func__, uv->u.uio_flags);
			abort();
		}
	}
}
void
rdma_ioq_uv_release_recvbufq(struct xdr_ioq_uv *uv)
{
	if (!(--uv->u.uio_references)) {
		if (uv->u.uio_release) {
			/* handle both xdr_ioq_uv and vio */
			uv->u.uio_release(&uv->u, UIO_FLAG_NONE);
		} else {
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"%s() memory leak, no release flags (%u)\n",
				__func__, uv->u.uio_flags);
			abort();
		}
	}
}

struct poolq_entry *
rdma_ioq_get_sendbufq(struct xdr_ioq *xioq, struct poolq_head *ioqh,
		 char *comment, u_int count, u_int ioq_flags)
{
	struct poolq_entry *have = NULL;
	//RDMAXPRT *xd = NULL;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		"%s() %u %s, ioqh:%p, ioqh->qcount:%d",
		__func__, count, comment, ioqh, ioqh->qcount);
#if __LOCK_OUT_MEM
	pthread_mutex_lock(&ioqh->qmutex);
#endif
	while (count--) {
		if (likely(0 < ioqh->qcount)) {
			ioqh->qcount--;
			/* positive for buffer(s) */
			have = TAILQ_FIRST(&ioqh->qh);
			TAILQ_REMOVE(&ioqh->qh, have, q);

#if __LOCK_OUT_MEM
			pthread_mutex_lock(&xioq->ioq_uv.uvqh.qmutex);
#endif
			(xioq->ioq_uv.uvqh.qcount)++;
			TAILQ_INSERT_TAIL(&xioq->ioq_uv.uvqh.qh, have, q);

#if __LOCK_OUT_MEM
			pthread_mutex_unlock(&xioq->ioq_uv.uvqh.qmutex);
#endif
			probe_count_inc(PROBE_COUNT_RDMA_MEM_OUT_USED);
		} else {
#if __LOCK_OUT_MEM_COND_WAIT
			u_int saved = xioq->xdrs[0].x_handy;

			/* negative for waiting worker(s):
			 * use the otherwise empty pool to hold them,
			 * simplifying mutex and pointer setup.
			 */
			TAILQ_INSERT_TAIL(&ioqh->qh, &xioq->ioq_s, q);

			__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"%s() waiting for %u %s",
				__func__, count, comment);

			/* Note: the mutex is the pool _head,
			 * but the condition is per worker,
			 * making the signal efficient!
			 *
			 * Nota Bene: count was already decremented,
			 * will be zero for last one needed,
			 * then will wrap as unsigned.
			 */
			xioq->xdrs[0].x_handy = count;
			pthread_cond_wait(&xioq->ioq_cond, &ioqh->qmutex);
			xioq->xdrs[0].x_handy = saved;

			/* entry was already added directly to the queue */
			have = TAILQ_LAST(&xioq->ioq_uv.uvqh.qh, poolq_head_s);
#else
			have = NULL;
			__warnx(TIRPC_DEBUG_FLAG_ERROR,"%s() %u %s, ioqh:%p, ioqh->qcount:%d",__func__, count, comment, ioqh, ioqh->qcount);
#endif
		}
	}
#if __LOCK_OUT_MEM
	pthread_mutex_unlock(&ioqh->qmutex);
#endif

	return have;
}
struct poolq_entry *
rdma_ioq_get_recvbufq(struct xdr_ioq *xioq, struct poolq_head *ioqh,
		 char *comment, u_int count, u_int ioq_flags)
{
	struct poolq_entry *have = NULL;

	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		"%s() %u %s",
		__func__, count, comment);
#if __LOCK_IN_MEM
	pthread_mutex_lock(&ioqh->qmutex);
#endif
	while (count--) {
		if (likely(0 < ioqh->qcount)) {
			ioqh->qcount--;
			/* positive for buffer(s) */
			have = TAILQ_FIRST(&ioqh->qh);
			//__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() have %p.",__func__, have);
			TAILQ_REMOVE(&ioqh->qh, have, q);

#if __LOCK_IN_MEM
			pthread_mutex_lock(&xioq->ioq_uv.uvqh.qmutex);
#endif
			(xioq->ioq_uv.uvqh.qcount)++;
			TAILQ_INSERT_TAIL(&xioq->ioq_uv.uvqh.qh, have, q);
#if __LOCK_IN_MEM
			pthread_mutex_unlock(&xioq->ioq_uv.uvqh.qmutex);
#endif
			probe_count_inc(PROBE_COUNT_RDMA_MEM_IN_USED);
		} else {
#if __LOCK_IN_MEM_COND_WAIT
			u_int saved = xioq->xdrs[0].x_handy;

			/* negative for waiting worker(s):
			 * use the otherwise empty pool to hold them,
			 * simplifying mutex and pointer setup.
			 */
			TAILQ_INSERT_TAIL(&ioqh->qh, &xioq->ioq_s, q);

			__warnx(TIRPC_DEBUG_FLAG_XDR,
				"%s() waiting for %u %s",
				__func__, count, comment);

			/* Note: the mutex is the pool _head,
			 * but the condition is per worker,
			 * making the signal efficient!
			 *
			 * Nota Bene: count was already decremented,
			 * will be zero for last one needed,
			 * then will wrap as unsigned.
			 */
			xioq->xdrs[0].x_handy = count;
			pthread_cond_wait(&xioq->ioq_cond, &ioqh->qmutex);
			xioq->xdrs[0].x_handy = saved;

			/* entry was already added directly to the queue */
			have = TAILQ_LAST(&xioq->ioq_uv.uvqh.qh, poolq_head_s);
#else
			have = NULL;
			__warnx(TIRPC_DEBUG_FLAG_ERROR,"%s() %u %s, ioqh:%p, ioqh->qcount:%d",__func__, count, comment, ioqh, ioqh->qcount);
#endif
		}
	}
#if __LOCK_IN_MEM
	pthread_mutex_unlock(&ioqh->qmutex);
#endif
	return have;
}


void
rdma_ioq_put_sendbufq(struct poolq_head *ioqh)
{
	struct poolq_entry *have = TAILQ_FIRST(&ioqh->qh);

	/* release queued buffers */
	while (have) {
		struct poolq_entry *next = TAILQ_NEXT(have, q);

		TAILQ_REMOVE(&ioqh->qh, have, q);
		(ioqh->qcount)--;
		rdma_ioq_uv_release_sendbufq(IOQ_(have));
		have = next;
		probe_count_dec(PROBE_COUNT_RDMA_MEM_OUT_USED);
	}
	assert(ioqh->qcount == 0);
}
void
rdma_ioq_put_recvbufq(struct poolq_head *ioqh)
{
	struct poolq_entry *have = TAILQ_FIRST(&ioqh->qh);
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() ioqh %p, have %p, ioqh->qcount:%lu", __func__, ioqh, have, ioqh->qcount);
	/* release queued buffers */
	while (have) {
		struct poolq_entry *next = TAILQ_NEXT(have, q);

		TAILQ_REMOVE(&ioqh->qh, have, q);
		(ioqh->qcount)--;
		rdma_ioq_uv_release_recvbufq(IOQ_(have));
		have = next;
		probe_count_dec(PROBE_COUNT_RDMA_MEM_IN_USED);
	}
	assert(ioqh->qcount == 0);
}


uint32_t
rdma_ioq_get_sendbufq_chunk(struct xdr_ioq *xioq, struct poolq_head *ioqh,
		     char *comment, u_int length, u_int sized, u_int max_sge,
		     void (*setup)(struct poolq_entry *, u_int, u_int, u_int))
{
	struct poolq_entry *have;
	uint32_t k = length / sized;
	uint32_t m = length % sized;

	if (m) {
		/* need fractional buffer */
		k++;
	} else {
		/* have full-sized buffer */
		m = sized;
	}

	/* ensure never asking for more buffers than allowed */
	if (k > max_sge) {
		__warnx(TIRPC_DEBUG_FLAG_XDR,
			"%s() requested chunk %" PRIu32
			" is too long (%" PRIu32 ">%" PRIu32 ")",
			__func__, length, k, max_sge);
		k = max_sge;
		m = sized;
	}

	/* ensure we can get all of our buffers without deadlock
	 * (wait for them all to be appended)
	 */
	have = rdma_ioq_get_sendbufq(xioq, ioqh, comment, k, IOQ_FLAG_NONE);
	if(have == NULL){
		return -1;
	}
	(*setup)(have, k, m, sized);
	return k;
}
struct poolq_entry *
rdma_ioq_uv_fetch_nothing(struct xdr_ioq *xioq, struct poolq_head *ioqh,
			 char *comment, u_int count, u_int ioq_flags)
{
	assert(0);
	return NULL;
}

struct poolq_entry *
rdma_ioq_get_cbc(struct xdr_ioq *xioq, struct poolq_head *ioqh,
		 char *comment, u_int count, u_int ioq_flags)
{
	struct poolq_entry *have = NULL;

	__warnx(TIRPC_DEBUG_FLAG_XDR,
		"%s() %u %s",
		__func__, count, comment);
#if __LOCK_CBC_MEM
	pthread_mutex_lock(&ioqh->qmutex);
#endif
	while (count--) {
		if (likely(0 < ioqh->qcount--)) {
			/* positive for buffer(s) */
			have = TAILQ_FIRST(&ioqh->qh);
			//__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() have %p.",__func__, have);
			TAILQ_REMOVE(&ioqh->qh, have, q);
			probe_count_inc(PROBE_COUNT_RDMA_CBC_USED);
			/* added directly to the queue.
			 * this lock is needed for context header queues,
			 * but is not a burden on uncontested data queues.
			 */
#if __LOCK_CBC_MEM
			pthread_mutex_lock(&xioq->ioq_uv.uvqh.qmutex);
#endif
			(xioq->ioq_uv.uvqh.qcount)++;
			TAILQ_INSERT_TAIL(&xioq->ioq_uv.uvqh.qh, have, q);
#if __LOCK_CBC_MEM
			pthread_mutex_unlock(&xioq->ioq_uv.uvqh.qmutex);
#endif
		} else {
#if __LOCK_CBC_MEM
			u_int saved = xioq->xdrs[0].x_handy;

			/* negative for waiting worker(s):
			 * use the otherwise empty pool to hold them,
			 * simplifying mutex and pointer setup.
			 */
			TAILQ_INSERT_TAIL(&ioqh->qh, &xioq->ioq_s, q);

			__warnx(TIRPC_DEBUG_FLAG_XDR,
				"%s() waiting for %u %s",
				__func__, count, comment);

			/* Note: the mutex is the pool _head,
			 * but the condition is per worker,
			 * making the signal efficient!
			 *
			 * Nota Bene: count was already decremented,
			 * will be zero for last one needed,
			 * then will wrap as unsigned.
			 */
			xioq->xdrs[0].x_handy = count;
			pthread_cond_wait(&xioq->ioq_cond, &ioqh->qmutex);
			xioq->xdrs[0].x_handy = saved;

			/* entry was already added directly to the queue */
			have = TAILQ_LAST(&xioq->ioq_uv.uvqh.qh, poolq_head_s);
#else
		have = NULL;
#endif
		}
	}
#if __LOCK_CBC_MEM
	pthread_mutex_unlock(&ioqh->qmutex);
#endif
	return have;
}
		 static inline void
rdma_ioq_cbc_recycle(struct poolq_head *ioqh, struct poolq_entry *have)
{
#if __LOCK_CBC_MEM
	pthread_mutex_lock(&ioqh->qmutex);
#endif
	if (likely(0 <= ioqh->qcount++)) {
		/* positive for buffer(s) */
		TAILQ_INSERT_TAIL(&ioqh->qh, have, q);
		probe_count_dec(PROBE_COUNT_RDMA_CBC_USED);
	} else {
#if __LOCK_CBC_MEM
		/* negative for waiting worker(s) */
		struct xdr_ioq *wait = _IOQ(TAILQ_FIRST(&ioqh->qh));

		/* added directly to the queue.
		 * no need to lock here, the mutex is the pool _head.
		 */
		(wait->ioq_uv.uvqh.qcount)++;
		TAILQ_INSERT_TAIL(&wait->ioq_uv.uvqh.qh, have, q);

		/* Nota Bene: x_handy was decremented count,
		 * will be zero for last one needed,
		 * then will wrap as unsigned.
		 */
		if (0 < wait->xdrs[0].x_handy--) {
			/* not removed */
			ioqh->qcount--;
		} else {
			TAILQ_REMOVE(&ioqh->qh, &wait->ioq_s, q);
			pthread_cond_signal(&wait->ioq_cond);
		}
#endif
	}
#if __LOCK_CBC_MEM
	pthread_mutex_unlock(&ioqh->qmutex);
#endif
}
void
rdma_ioq_put_cbc(struct xdr_ioq *xioq, size_t qsize)
{
	__warnx(TIRPC_DEBUG_FLAG_XDR,
		"%s() xioq %p",
		__func__, xioq);
	xioq->xdrs[0].xp_type = XPRT_UNKNOWN;

	if (xioq->ioq_pool) {
		rdma_ioq_cbc_recycle(xioq->ioq_pool, &xioq->ioq_s);
		return;
	}
}


static inline
void *socket_addr(struct sockaddr_storage *addr)
{
	switch (addr->ss_family) {
	case AF_INET:
		return &(((struct sockaddr_in *)addr)->sin_addr);
	case AF_INET6:
		return &(((struct sockaddr_in6 *)addr)->sin6_addr);
#ifdef RPC_VSOCK
	case AF_VSOCK:
		return &(((struct sockaddr_vm *)addr)->svm_cid);
#endif /* VSOCK */
	default:
		return addr;
	}
}
/**
 * @brief Convert sockaddr_storage to a "ip:port" formatted string
 *
 * @param addr Input address structure
 * @param buf Output buffer
 * @param buflen Buffer length (at least INET6_ADDRSTRLEN)
 * @return 0 on success, -1 on failure
 */
//static inline
int sockaddr_to_ip_port(struct sockaddr_storage *addr, char *buf, size_t buflen) {
	if (!addr || !buf || buflen < INET6_ADDRSTRLEN) {
		return -1;
	}

	char ipstr[INET6_ADDRSTRLEN];
	int port = 0;

	switch (addr->ss_family) {
		case AF_INET: {
			struct sockaddr_in *sin = (struct sockaddr_in *)addr;
			if (!inet_ntop(AF_INET, &sin->sin_addr, ipstr, sizeof(ipstr))) {
				return -1;
			}
			port = ntohs(sin->sin_port);
			break;
		}
		case AF_INET6: {
			struct sockaddr_in6 *sin6 = (struct sockaddr_in6 *)addr;
			if (!inet_ntop(AF_INET6, &sin6->sin6_addr, ipstr, sizeof(ipstr))) {
				return -1;
			}
			port = ntohs(sin6->sin6_port);

			// For IPv6 addresses, [] are typically required
			if (strchr(ipstr, ':')) {
				if (snprintf(buf, buflen, "[%s]:%d", ipstr, port) >= (int)buflen) {
					return -1;
				}
				return 0;
			}
			break;
		}
		default:
			return -1;
	}

	// For IPv4 or IPv6 addresses without :
	if (snprintf(buf, buflen, "%s:%d", ipstr, port) >= (int)buflen) {
		return -1;
	}

	return 0;
}
static inline
bool sprint_sockip(struct sockaddr_storage *addr, char *buf, int len)
{
#ifdef RPC_VSOCK
	if (addr->ss_family == AF_VSOCK) {
		int rc = snprintf(buf, len, "%d",
				  ((struct sockaddr_vm *)addr)->svm_cid));
		return rc >= 0 && rc < len;
	}
#endif /* VSOCK */

	if (addr->ss_family != AF_INET && addr->ss_family != AF_INET6)
		return false;

	return inet_ntop(addr->ss_family, socket_addr(addr), buf, len) != NULL;
}

static void
svc_fabric_worker_callback(struct work_pool_entry *wpe)
{
	struct rpc_rdma_cbc *cbc =
		opr_containerof(wpe, struct rpc_rdma_cbc, wpe);
	RDMAXPRT *xprt = (RDMAXPRT *)wpe->arg;

	if (cbc->positive_cb) {
		cbc->positive_cb(cbc, xprt);
	}
	return ;
}



extern struct fabric_class *fab_clas;
/**
 * svc_rdma_fabric_ncreatef: initialize rdma transport structures
 *
 * @param[IN] xa		parameters
 * @param[IN] sendsize;		max send size
 * @param[IN] recvsize;		max recv size
 * @param[IN] flags; 		unused
 *
 * @return xprt on success, NULL on failure
 */
SVCXPRT *
svc_rdma_fabric_ncreatef(const struct rpc_rdma_attr *xa,
		  const u_int sendsize, const u_int recvsize,
		  const uint32_t flags)
{
	RDMAXPRT *xd = NULL;
	struct fabric_class *fabric = NULL;

	xd = mem_zalloc(sizeof(*xd));

	xd->sm_dr.xprt.xp_type = XPRT_RDMA;
	xd->sm_dr.xprt.xp_refcnt = 1;
	xd->sm_dr.xprt.xp_ops = &rpc_fabric_ops;

	xd->xa = xa;
	
	if (!xd) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s:%u ERROR (return)",
			__func__, __LINE__);
		return NULL;
	}
	xd->server = xa->backlog; /* convenient number > 0 */


	pthread_mutex_lock(&svc_work_pool.pqh.qmutex);
	if (!svc_work_pool.params.thrd_max) {
		pthread_mutex_unlock(&svc_work_pool.pqh.qmutex);

		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s() svc_work_pool already shutdown",
			__func__);
		goto failure;
	}
	pthread_mutex_unlock(&svc_work_pool.pqh.qmutex);

	/* buffer sizes MUST be page sized */
	xd->sm_dr.pagesz = sysconf(_SC_PAGESIZE);
	if (recvsize) {
		xd->sm_dr.recvsz = recvsize;
	} else {
		/* default */
		xd->sm_dr.recvsz = xd->sm_dr.pagesz;
	}
	if (sendsize) {
		xd->sm_dr.sendsz = sendsize;
	} else {
		/* default */
		xd->sm_dr.sendsz = xd->sm_dr.pagesz;
	}
	
	memset(&rdma_xprt_class, 0, sizeof(rdma_xprt_class));
	rdma_xprt_class.rdmaxprt = xd;
	rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_time_begin = xa->probe_time_begin;
	rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_time_end   = xa->probe_time_end;
	rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_count_inc  = xa->probe_count_inc;
	rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_count_dec  = xa->probe_count_dec;
	rdma_xprt_class.rdmaxprt->sm_dr.xprt.work_sleep_time  = xa->work_sleep_time;
	rdma_xprt_class.rdmaxprt->sm_dr.xprt.get_gsh_client  = xa->get_gsh_client;
	rdma_xprt_class.rdmaxprt->sm_dr.xprt.remove_gsh_client  = xa->remove_gsh_client;
	/*stat xprt*/
	poolq_head_setup(&rdma_xprt_class.rdma_xprt_list);
	//xa->gane_chunk_size = xd->sm_dr.sendsz;
	//xa->gane_pool_size = xd->xa->sq_depth;
	xd->buffer_total = xd->sm_dr.sendsz * xd->xa->uv_sq_depth; 
	
	fabric = fabric_init((char *)xa->bindaddr, xa->nfsrdma_port, xa, xd);

	if (fabric == NULL) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s()  NFS/FABRIC  fabric init failed",
			__func__);
		goto failure;
	}else {
		__warnx(TIRPC_DEBUG_FLAG_XDR,
			"%s()  NFS/FABRIC  fabric init success!",
			__func__);
	}

	rdma_xprt_class.fab_clas = fab_clas;
	/* memery aligned*/

	/*xd->buffer_total = xd->sm_dr.recvsz * xd->xa->rq_depth
			 + xd->sm_dr.sendsz * xd->xa->sq_depth; */
	
	

	//xd->buffer_aligned = mem_aligned(xd->sm_dr.pagesz, xd->buffer_total);
	//xd->buffer_aligned = alloc_registed_memory(xd->sm_dr.pagesz, xd->buffer_total);
	//assert(xd->buffer_aligned != NULL);
	
	poolq_head_setup(&xd->inbufs.uvqh);
	xd->inbufs.min_bsize = xd->sm_dr.pagesz;
	xd->inbufs.max_bsize = xd->sm_dr.recvsz;

	poolq_head_setup(&xd->outbufs.uvqh);
	xd->outbufs.min_bsize = xd->sm_dr.pagesz;
	xd->outbufs.max_bsize = xd->sm_dr.sendsz;

	/* Each pre-allocated buffer has a corresponding xdr_ioq_uv,
	 * stored on the pool queues.
	 */
	//b = xd->buffer_aligned;

	pthread_mutex_lock(&xd->inbufs.uvqh.qmutex);

	for (xd->inbufs.uvqh.qcount = 0;
		 xd->inbufs.uvqh.qcount < xd->xa->uv_rq_depth;
		 xd->inbufs.uvqh.qcount++) {
		struct xdr_ioq_uv *data = xdr_ioq_uv_create(0, UIO_FLAG_BUFQ);
		/*UIO_FLAG_BUFQ , xdr_ioq_uv_recycle( u.uio_p1)*/
		data->v.vio_base =
		data->v.vio_head =
		data->v.vio_tail = 
		data->v.vio_wrap = NULL;
		data->u.uio_p1 = &xd->inbufs.uvqh;
		//data->u.uio_p2 = xd;
		data->u.uio_release = xdr_fabric_inbuf_uio_release;
		TAILQ_INSERT_TAIL(&xd->inbufs.uvqh.qh, &data->uvq, q);
		//b += xd->sm_dr.recvsz;
		probe_count_inc(PROBE_COUNT_RDMA_MEM_IN_ALLOC);
	}
	pthread_mutex_unlock(&xd->inbufs.uvqh.qmutex);

	uint8_t *b;
	b = xd->buffer_aligned;
	pthread_mutex_lock(&xd->outbufs.uvqh.qmutex);
	for (xd->outbufs.uvqh.qcount = 0;
		 xd->outbufs.uvqh.qcount < xd->xa->uv_sq_depth;
		 xd->outbufs.uvqh.qcount++) {
		struct xdr_ioq_uv *data = xdr_ioq_uv_create(0, UIO_FLAG_BUFQ);

		data->v.vio_base =
		data->v.vio_head =
		data->v.vio_tail = b;
		data->v.vio_wrap = (char *)b + xd->sm_dr.sendsz;
		data->u.uio_p1 = &xd->outbufs.uvqh;
		//data->u.uio_p2 = xd;
		data->u.uio_release = xdr_fabric_outbuf_uio_release;

		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "NFS/FABRIC %s: poolq_entry %p. vio_base %p.", __func__, &data->uvq,  data->v.vio_base);

		TAILQ_INSERT_TAIL(&xd->outbufs.uvqh.qh, &data->uvq, q);
		b += xd->sm_dr.sendsz;
		probe_count_inc(PROBE_COUNT_RDMA_MEM_OUT_ALLOC);
	}
	pthread_mutex_unlock(&xd->outbufs.uvqh.qmutex);



	struct poolq_head *ioqh = &xd->cbqh;
	
	u_int depth = xd->xa->uv_rq_depth + xd->xa->uv_sq_depth;
	poolq_head_setup(ioqh);


	/* individual entries is less efficient than big array -- but uses
	 * "standard" IOQ operations, xdr_ioq_destroy_pool(), and
	 * debugging memory bounds checking of trailing ibv_sge array.
	 */
	while (depth--) {
		struct rpc_rdma_cbc *cbc;
		cbc = mem_zalloc(sizeof(struct rpc_rdma_cbc));

		xdr_ioq_setup(&cbc->recvbufq);
		xdr_ioq_setup(&cbc->sendbufq);
		poolq_head_setup(&cbc->writebufuvq.uvqh);

		cbc->sendbufq.ioq_uv.uvq_fetch = rdma_ioq_uv_fetch_nothing;
		cbc->recvbufq.ioq_uv.uvq_fetch = rdma_ioq_uv_fetch_nothing;
		
		cbc->sendbufq.xdrs[0].x_ops =
		cbc->recvbufq.xdrs[0].x_ops = &xdr_ioq_rdma_ops;
		cbc->sendbufq.xdrs[0].x_op =
		cbc->recvbufq.xdrs[0].x_op = XDR_FREE; /* catch setup errors */
		cbc->sendbufq.xdrs[0].x_lib[0] =
		cbc->recvbufq.xdrs[0].x_lib[0] = cbc;
		cbc->sendbufq.xdrs[0].x_lib[1] =
		cbc->recvbufq.xdrs[0].x_lib[1] = xd;
		cbc->recvbufq.ioq_pool = ioqh;
		cbc->wpe.fun = svc_fabric_worker_callback;
		pthread_mutex_lock(&ioqh->qmutex);
		(ioqh->qcount)++;
		TAILQ_INSERT_TAIL(&ioqh->qh, &cbc->recvbufq.ioq_s, q);
		pthread_mutex_unlock(&ioqh->qmutex);
		//(void)atomic_inc_uint64_t(&(xd->xprt_stat.cbc_cnt));
		probe_count_inc(PROBE_COUNT_RDMA_CBC_ALLOC);
	}
	__warnx(TIRPC_DEBUG_FLAG_ERROR,
	"%s() NFS/FABRIC xprt create !!! xd %p, at %p, buffer_total %ld,  recvsz %d, sendsz %d, depth %d, rq_depth %d, sq_depth %d, xd->xa->credits %d. fabric_endpoint %p.",
		__func__, xd,
		xd->buffer_aligned, 
		xd->buffer_total, 
		xd->sm_dr.recvsz,
		xd->sm_dr.sendsz,		
		xd->xa->uv_rq_depth + xd->xa->uv_sq_depth, 
		xd->xa->uv_rq_depth,
		xd->xa->uv_sq_depth,
		xd->xa->credits,
		xd->fd);
	rdma_xprt_class.g_rdma_xprt_init = true;
	return (&xd->sm_dr.xprt);

failure:
	free(xd);
	rdma_xprt_class.rdmaxprt = NULL;
	return NULL;
}

int svc_fabric_nadd_virtual_ipf(const char *vip) {
	return  fabric_add_virtual_ip(vip);
}
int svc_fabric_ndelete_virtual_ipf(const char *vip) {
	return  fabric_delete_virtual_ip(vip);
}
void svc_fabric_nget_listen_ipsf(char **ips, int max_nums, int *ip_nums) {
	return fabric_get_listen_ips(ips, max_nums, ip_nums);
}

SVCXPRT *
svc_rdma_fabric_ndestoryf(void) {

	//assert(rdma_xprt_list.qcount == 0);
	RDMAXPRT *xd = rdma_xprt_class.rdmaxprt;
	if (xd == NULL) {
		return NULL;
	}
	
	if(rdma_xprt_class.fab_clas)
		fabric_free(rdma_xprt_class.fab_clas);

	if (!TAILQ_EMPTY(&xd->cbqh.qh)) {
		struct rpc_rdma_cbc *cbc = NULL;
		struct poolq_head *ioqh = &xd->cbqh;
		struct poolq_entry *have = TAILQ_FIRST(&ioqh->qh);
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, 
				"NFS/FABRIC  %s() cbqh %p, ioqh->qcount %d, have %p\n", 
				__func__, ioqh, ioqh->qcount, have);
		while (have) {
			struct poolq_entry *next = TAILQ_NEXT(have, q);
			TAILQ_REMOVE(&ioqh->qh, have, q);
			(ioqh->qcount)--;

			cbc = (opr_containerof((_IOQ(have)), struct rpc_rdma_cbc, recvbufq));
			cbc->recvbufq.ioq_pool = NULL;
			xdr_ioq_destroy(&(cbc->recvbufq), have->qsize);
			xdr_ioq_destroy(&(cbc->sendbufq), have->qsize);
			poolq_head_destroy(&cbc->writebufuvq.uvqh);
			mem_free(cbc, sizeof(*cbc));
			probe_count_dec(PROBE_COUNT_RDMA_CBC_ALLOC);
			have = next;
		}
		assert(ioqh->qcount == 0);
		poolq_head_destroy(ioqh);
	}

	/* must be after queues, xdr_ioq_destroy() moves them here */
	struct xdr_ioq *in_ioq = (struct xdr_ioq *)_IOQ(TAILQ_FIRST(&xd->inbufs.uvqh.qh));	
	in_ioq->ioq_pool = NULL;
	//xdr_ioq_release_force(&xd->inbufs.uvqh);
	//xdr_ioq_release_uv(&xd->inbufs.uvqh);
	{
		struct poolq_head *ioqh = &xd->inbufs.uvqh;
		struct poolq_entry *have = TAILQ_FIRST(&ioqh->qh);
		/*release queued buffers*/
		while (have) {
			struct poolq_entry *next = TAILQ_NEXT(have, q);
			TAILQ_REMOVE(&ioqh->qh, have, q);
			(ioqh->qcount)--;
			struct xdr_ioq_uv *uv = IOQ_(have);
			probe_count_dec(PROBE_COUNT_RDMA_MEM_IN_ALLOC);
			mem_free(uv, sizeof(*uv));
			have = next;
		}
		assert(ioqh->qcount == 0);	
	}
	poolq_head_destroy(&xd->inbufs.uvqh);

	struct xdr_ioq *out_ioq = (struct xdr_ioq *)_IOQ(TAILQ_FIRST(&xd->outbufs.uvqh.qh));	
	out_ioq->ioq_pool = NULL;
	//xdr_ioq_release_force(&xd->outbufs.uvqh);
	//xdr_ioq_release_uv(&xd->outbufs.uvqh);
	{
		struct poolq_head *ioqh = &xd->outbufs.uvqh;
		struct poolq_entry *have = TAILQ_FIRST(&ioqh->qh);
		/*release queued buffers*/
		while (have) {
			struct poolq_entry *next = TAILQ_NEXT(have, q);
			TAILQ_REMOVE(&ioqh->qh, have, q);
			(ioqh->qcount)--;
			struct xdr_ioq_uv *uv = IOQ_(have);
			probe_count_dec(PROBE_COUNT_RDMA_MEM_OUT_ALLOC);
			mem_free(uv, sizeof(*uv));
			have = next;
		}
		assert(ioqh->qcount == 0);	
	}
	poolq_head_destroy(&xd->outbufs.uvqh);

	/* must be after pools */
	if (xd->buffer_aligned) {
		mem_free(xd->buffer_aligned, xd->buffer_total);
        //munmap(xd->buffer_aligned, xd->buffer_total);
		xd->buffer_aligned = NULL;
	}
	poolq_head_destroy(&rdma_xprt_class.rdma_xprt_list);

	return NULL;
}

void put_gsh_rdma_client(struct gsh_client *client)
{
	int64_t new_refcnt;
	new_refcnt = atomic_dec_int64_t(&client->refcnt);
	assert(new_refcnt >= 0);
}

/*
 * svc_rdma_rendezvous: waits for connection request
 */
RDMAXPRT *svc_rdma_fabric_xprt_create(void *fabric_ep)
{
	char local_ipstring[SOCK_NAME_MAX];
	char peer_ipstring[SOCK_NAME_MAX];
	local_ipstring[SOCK_NAME_MAX - 1] = '\0';
	peer_ipstring[SOCK_NAME_MAX - 1] = '\0';
	struct gsh_client *client = NULL;
	fabric_endpoint *ep = fabric_ep;
	while (rdma_xprt_class.g_rdma_xprt_init == false) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s:%u FABRIC g rdma xprt not init",
			__func__, __LINE__);
		sleep(1);
	}
	RDMAXPRT *xd = mem_zalloc(sizeof(*xd));
	if (!xd) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s:%u ERROR (return)",
			__func__, __LINE__);
		return NULL;
	}

	xd->sm_dr.xprt.xp_type = XPRT_RDMA;
	xd->sm_dr.xprt.xp_refcnt = 1;
	xd->sm_dr.xprt.xp_ops = &rpc_fabric_ops;

	xd->sm_dr.maxrec = rdma_xprt_class.rdmaxprt->sm_dr.maxrec;
	xd->sm_dr.pagesz = rdma_xprt_class.rdmaxprt->sm_dr.pagesz;
	xd->sm_dr.recvsz = rdma_xprt_class.rdmaxprt->sm_dr.recvsz;
	xd->sm_dr.sendsz = rdma_xprt_class.rdmaxprt->sm_dr.sendsz;

	xd->sm_dr.xprt.probe_time_begin = rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_time_begin;
	xd->sm_dr.xprt.probe_time_end =   rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_time_end;
	xd->sm_dr.xprt.probe_count_inc =  rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_count_inc;
	xd->sm_dr.xprt.probe_count_dec =  rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_count_dec;

	xd->sm_dr.xprt.xp_dispatch.process_cb = rdma_xprt_class.rdmaxprt->sm_dr.xprt.xp_dispatch.process_cb;
	xd->xa = rdma_xprt_class.rdmaxprt->xa;
	xd->conn_type = RDMA_PS_TCP;
	xd->fd = fabric_ep;
	/* initialize locking first, will be destroyed last (above).
	 */
	xdr_ioq_setup(&xd->sm_dr.ioq);
	rpc_dplx_rec_init(&xd->sm_dr);
	
	//TAILQ_INSERT_TAIL(&rdma_xprt_list.qh, &xd->sm_dr.ioq.ioq_s, q);
	//rdma_xprt_list.qcount++

	pthread_mutex_lock(&svc_work_pool.pqh.qmutex);
	if (!svc_work_pool.params.thrd_max) {
		pthread_mutex_unlock(&svc_work_pool.pqh.qmutex);

		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s() svc_work_pool already shutdown",
			__func__);
		goto failure;
	}
	pthread_mutex_unlock(&svc_work_pool.pqh.qmutex);


	xd->sm_dr.xprt.xp_flags = SVC_XPRT_FLAG_CLOSE
				| SVC_XPRT_FLAG_INITIAL
				| SVC_XPRT_FLAG_INITIALIZED;
	struct sockaddr_storage *si = NULL;
	struct rpc_address *rpca = NULL;
	si = &ep->local_addr;
	rpca = (struct rpc_address *)(&xd->sm_dr.xprt.xp_local);
	rpca->nb.buf = &rpca->ss;
	rpca->nb.len = rpca->nb.maxlen = sizeof(struct sockaddr_in);
	memcpy(&xd->sm_dr.xprt.xp_local.ss, si, sizeof(*si));

	sprint_sockip(&ep->local_addr, local_ipstring, sizeof(local_ipstring));	

	si = (struct sockaddr_storage *)(&ep->peer_addr);
	rpca = (struct rpc_address *)(&xd->sm_dr.xprt.xp_remote);
	rpca->nb.buf = &rpca->ss;
	rpca->nb.len = rpca->nb.maxlen = sizeof(struct sockaddr_in);
	memcpy(&xd->sm_dr.xprt.xp_remote.ss, si, sizeof(*si));

	sprint_sockip(&ep->peer_addr, peer_ipstring, sizeof(peer_ipstring));
	xd->sm_dr.xprt.xp_fd = -1;
	__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC xprt create success! xprt:%p. local-ip: %s, peer-ip:%s.", __func__, xd, local_ipstring, peer_ipstring);

	client =  rdma_xprt_class.rdmaxprt->sm_dr.xprt.get_gsh_client((struct sockaddr_storage *)&xd->sm_dr.xprt.xp_remote.ss, false);
	if (client == NULL){
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC client == NULL", __func__);
	}
	else{
		client->rdma_client = true;
		put_gsh_rdma_client(client);
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC client != NULL, client->rdma_client :%d, client->refcnt:%lu", __func__, client->rdma_client, client->refcnt);
	}
	probe_count_inc(PROBE_COUNT_RDMAXPRT);
	return xd;
failure:
	if (!xd)
		svc_rdma_fabric_xprt_destory(xd);
	return NULL;
	
}

static void
rpc_fabric_destroy_it_null(SVCXPRT *xprt, u_int flags, const char *tag, const int line)
{
	//RDMAXPRT *xd = RDMA_DR(REC_XPRT(xprt));
	//__warnx(TIRPC_DEBUG_FLAG_RPC_RDMA,
	//"%s() NFS/FABRIC xprt destory !!! xd %p.",
	//	__func__,
	//	xd);
	return;
 }
 static void
 rpc_fabric_destroy_it(SVCXPRT *xprt, u_int flags, const char *tag, const int line)
 {
	RDMAXPRT *xd = RDMA_DR(REC_XPRT(xprt));
#if 0
	__warnx(TIRPC_DEBUG_FLAG_REFCNT,
		"%s() NFS/FABRIC %p  NFS/FABRICxp_refcnt %" PRId32
		" should actually destroy things @ %s:%d (%d)",
		__func__, xprt, xprt->xp_refcnt, tag, line, flags);
#endif
	__warnx(TIRPC_DEBUG_FLAG_ERROR,
	"%s() NFS/FABRIC xprt destory !!! xd %p. waiting ...",
		__func__,
		xd);


	if ( (xprt->xp_ops != NULL) && (xprt->xp_ops->xp_free_user_data != NULL) ) {
		/* call free hook */
		xprt->xp_ops->xp_free_user_data(xprt);
	}

	if (!TAILQ_EMPTY(&xd->sm_dr.ioq.ioq_uv.uvqh.qh)) {
		assert(0);
		//xdr_ioq_destroy_pool(&xd->sm_dr.ioq.ioq_uv.uvqh);
	}
	xdr_ioq_destroy(XIOQ(&xd->sm_dr.ioq.xdrs), sizeof(struct xdr_ioq));  /*xdr_ioq_destroy_internal*/
	rpc_dplx_rec_destroy(&xd->sm_dr);

	xd->sm_dr.ioq.xdrs[0].x_lib[0] = NULL;
	xd->sm_dr.ioq.xdrs[0].x_lib[1] = NULL;

	fabric_endpoint *ep = xd->fd;	
	char peer_ipstring[SOCK_NAME_MAX];
	sprint_sockip((struct sockaddr_storage *)&ep->peer_addr, peer_ipstring, sizeof(peer_ipstring));	
	int remove_status = -1;	
	remove_status = rdma_xprt_class.rdmaxprt->sm_dr.xprt.remove_gsh_client((struct sockaddr_storage *)&xd->sm_dr.xprt.xp_remote.ss);

	__warnx(TIRPC_DEBUG_FLAG_ERROR,
	"%s() NFS/FABRIC xprt destory success !!! xd %p. fabric_endpoint %p, peer-ip:%s,  rdma client remove status:%d.",
		__func__, xd, xd->fd, peer_ipstring, remove_status);

	destory_fab_ep(xd->fd);
	mem_free(xd, sizeof(*xd));
	probe_count_dec(PROBE_COUNT_RDMAXPRT);
	return;
 }

void
svc_rdma_fabric_xprt_destory(RDMAXPRT *xd) {
	if (xd != NULL) {
		rpc_fabric_destroy_it(&xd->sm_dr.xprt, SVC_RELEASE_FLAG_NONE, __func__, __LINE__);
	} else {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC xprt destory error !!! xd is null.", __func__);
	}
return ;
}

static void
svc_rdma_fabric_request(void *arg)
{
	//int rc = 0;
	struct rpc_rdma_cbc *cbc = arg;
	enum xprt_stat stat = XPRT_IDLE;
	XDR *xdrs = &cbc->recvbufq.xdrs[0];
	RDMAXPRT *xprt = x_xprt(cbc->recvbufq.xdrs);
	struct timespec time_start;

	probe_count_inc(PROBE_COUNT_RDMA_WORKTHRNUM);
	probe_time_end(xprt, cbc, RPC_fabric_worksubmit_wait);
	probe_time_begin(xprt, cbc, NFS_process);

	xprt->sm_dr.xprt.probe_time_begin(&(time_start), SVC_fabric_request);
	struct svc_req *req = __svc_params->alloc_cb(&xprt->sm_dr.xprt, xdrs); /* alloc_nfs_request() */
	struct rpc_dplx_rec *rpc_dplx_rec = REC_XPRT(xprt);
	/* Track the request we are processing */
	rpc_dplx_rec->svc_req = req;
	xdrs->x_op = XDR_DECODE;
	xdrs->xp_type = xprt->sm_dr.xprt.xp_type;

	SVC_REF(&xprt->sm_dr.xprt, SVC_REF_FLAG_NONE);

	now(&xdrs->recv_end);
	//xdrs->recv_start = xprt->sm_dr.xprt.recv_start;	
	xdrs->decode_start = xdrs->recv_end;

	struct poolq_entry *holdhave =  TAILQ_FIRST(&(XIOQ(xdrs)->ioq_uv.uvqh.qh));;
	cbc->recv_rpc_head = (void *)holdhave;
	struct fabric_endpoint *fe = (struct fabric_endpoint* )cbc->ch;
	(void)atomic_inc_uint64_t(&fe->ref_count);
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
				"%s NFS/FABRIC nfs request, cbc %p, headhave %p.", __func__, cbc, holdhave);
#if 0
	{
		struct poolq_entry *have;
		uint32_t totalsize = 0;
		int iov_cnt = 0;

		TAILQ_FOREACH(have, &(XIOQ(xdrs)->ioq_uv.uvqh.qh), q) {
			uint32_t length = ioquv_length(IOQ_(have));
			void *addr = (void *)(IOQ_(have)->v.vio_head);
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
				"%s NFS/FABRIC cbc %p, [IOV]cbc->hold iov[%d]: addr %p, len %d.", __func__,  cbc, iov_cnt, addr, length);
			totalsize += length;		
			iov_cnt ++;
		}
		if(!have) {
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
				"%s NFS/FABRIC cbc %p, [IOV] cbc->hold  totalsize %d. qcount %d.", __func__,  cbc, totalsize, iov_cnt);
		}
	}
#endif

	rpc_msg_init(&req->rq_msg);
	int process_stat = 0;
	if (xdr_dplx_decode(xdrs, &req->rq_msg)){
		/* the checksum */
		req->rq_cksum = 0;
		req->rq_xprt->xp_parent = req->rq_xprt;
		req->rq_xprt->xp_ops = &rpc_fabric_ops;
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
			"NFS/FABRIC %s: xdr_dplx_decode success, req->rq_xprt->xp_ops %p, &rpc_fabric_ops %p, fe:%p, fe->ref_count:%d",
			__func__, req->rq_xprt->xp_ops, &rpc_fabric_ops, fe, fe->ref_count);
		/*nfs_rpc_valid_NFS*/
		process_stat = req->rq_xprt->xp_dispatch.process_cb(req);


	} else {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"NFS/FABRIC %s: xdr_dplx_decode failed",
			__func__);
	}
	/*if process_stat = XPRT_SUSPEND, qos is asyn callback, resource are not released.*/
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "NFS/FABRIC nfs request  %s: xp_dispatch.process_cb success, process_stat:%d, cbc %p, headhave %p.", __func__, process_stat, cbc, holdhave);

	if (process_stat != XPRT_SUSPEND){
		if (req->rq_auth)
			SVCAUTH_RELEASE(req);
		/*zercpy write ,memory data taken over by lib, 
		  *gnfs no longer responsible for release
		  set uio flag keep the memory*/
		//if (req->mem_taken == true){
		//	XDR_UPDATEIOV(req->rq_xdrs);
		//}
		//xdr_ioq_uv_release(IOQ_(holdhave));	
		//XDR_DESTROY(req->rq_xdrs); /*xdr_ioq_destroy_internal(xdrs)*/

		__svc_params->free_cb(req, stat); /* free_nfs_request */
		SVC_RELEASE(&xprt->sm_dr.xprt, SVC_REF_FLAG_NONE);
	}
	xprt->sm_dr.xprt.probe_time_end(&(time_start), SVC_fabric_request);
	(void)atomic_dec_uint64_t(&fe->ref_count);
	probe_count_dec(PROBE_COUNT_RDMA_WORKTHRNUM);
	//return rc;
	return ;
}

static int
svc_rdma_positive_request(struct rpc_rdma_cbc *cbc, RDMAXPRT *xd)
{
	(void)svc_rdma_fabric_request(cbc);
	return 0;
}

int
rpc_worksubmit(struct rpc_rdma_cbc *cbc)
{
	int ret = 0;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
				"%s NFS/FABRIC submit work thread, cbc %p.", __func__, cbc);
	RDMAXPRT *xd = x_xprt(cbc->recvbufq.xdrs);
	probe_time_begin(xd, cbc, RPC_fabric_worksubmit_wait);
	if (xd->xa->thr_nolock ==  true) {
		ret = fabric_workpool_submit(rdma_xprt_class.fab_clas, svc_rdma_fabric_request, cbc);
	} else {
		cbc->positive_cb = svc_rdma_positive_request;
		ret = work_pool_submit(&svc_work_pool, &cbc->wpe);
	}

	return ret;
}


static 
enum xprt_stat
svc_rdma_fabric_reply(struct svc_req *req)
{
	XDR *xdrs = req->rq_xdrs;
	struct rpc_rdma_cbc *cbc =
		opr_containerof(XIOQ(xdrs), struct rpc_rdma_cbc, recvbufq);
	struct xdr_ioq *sendq = &cbc->sendbufq;

	xdrs = &(sendq->xdrs[0]);


	bool ret = false;
	//RDMAXPRT *xd = x_xprt(xdrs);
	/* reply start time */

	xdrs->rq_proc = req->rq_msg.cb_proc;
	xdrs->rq_vers = req->rq_msg.cb_vers;
	xdrs->rq_prog = req->rq_msg.cb_prog;	
	xdrs->xp_type = XPRT_RDMA;
	now(&req->reply_start);

	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		"%s() NFS/FABRIC xprt %p req %p cbc %p send xdr %p fabric %p , cbc->recvbufq.ioq_uv.uvqh :%p\n",
		__func__, req->rq_xprt, req, cbc, xdrs, cbc->ch, cbc->recvbufq.ioq_uv.uvqh);
	xdrs->x_op = XDR_ENCODE;

	if (!xdr_reply_encode(xdrs, &req->rq_msg)) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s():  NFS/FABRIC xdr_reply_encode failed (will set dead) cbc %p, cbc->recvbufq.ioq_uv.uvqh :%p",
			__func__, cbc, cbc->recvbufq.ioq_uv.uvqh);
		return (XPRT_DIED);
	}
	xdr_tail_update(xdrs);

	if (req->rq_msg.rm_reply.rp_stat == MSG_ACCEPTED
	 && req->rq_msg.rm_reply.rp_acpt.ar_stat == SUCCESS
	 && req->rq_auth
	 && !SVCAUTH_WRAP(req, xdrs)) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s():  NFS/FABRIC SVCAUTH_WRAP failed (will set dead), cbc %p, cbc->recvbufq.ioq_uv.uvqh :%p",
			__func__, cbc, cbc->recvbufq.ioq_uv.uvqh);
		return (XPRT_DIED);
	}
	xdr_tail_update(xdrs);

	xdrs->x_lib[1] = (void *)req->rq_xprt;
	xdrs->reply_start = req->reply_start;
	xdrs->recv_start = req->rq_xdrs->recv_start;
	xdrs->nfs_gst_svc = req->nfs_gst_svc;
	xdrs->rq_proc = req->rq_msg.cb_proc;
	xdrs->rq_vers = req->rq_msg.cb_vers;
	xdrs->rq_prog = req->rq_msg.cb_prog;	
	cbc->rq_proc = req->rq_msg.cb_proc;
	cbc->rq_vers = req->rq_msg.cb_vers;
	cbc->rq_prog = req->rq_msg.cb_prog;	

	ret = rpc_fabric_reply(cbc);
	if (ret != 0){
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
			"%s(): NFS/FABRIC flushout failed (will set dead), cbc %p, cbc->recvbufq.ioq_uv.uvqh :%p",
			__func__, cbc, cbc->recvbufq.ioq_uv.uvqh);
		return (XPRT_DIED);
	}

	return (XPRT_IDLE);
}

static void
rpc_fabric_unlink_it(SVCXPRT *xprt, u_int flags, const char *tag, const int line)
{
	return;
}

static bool
/*ARGSUSED*/
rpc_fabric_control(SVCXPRT *xprt, const u_int rq, void *in)
{
	return (TRUE);
}

enum xprt_stat
svc_fabric_stat(SVCXPRT *xprt)
{
	if (!xprt)
		return (XPRT_IDLE);

	if (xprt->xp_flags & SVC_XPRT_FLAG_DESTROYED)
		return (XPRT_DESTROYED);

	return (XPRT_IDLE);
}


extern mutex_t ops_lock;
struct xp_ops rpc_fabric_ops = {
	.xp_recv = NULL,
	.xp_stat = svc_fabric_stat,
	.xp_reply = svc_rdma_fabric_reply,
	.xp_decode = NULL,
	.xp_checksum = NULL,		/* not used */
	.xp_unlink = rpc_fabric_unlink_it,
	.xp_destroy = rpc_fabric_destroy_it_null,
	.xp_control = rpc_fabric_control,
	.xp_free_user_data = NULL,	/* no default */
};
void 
probe_count_inc(int op) {
	if(rdma_xprt_class.rdmaxprt != NULL)
		rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_count_inc(op);
}
void 
probe_count_dec(int op) {
	if(rdma_xprt_class.rdmaxprt != NULL)
		rdma_xprt_class.rdmaxprt->sm_dr.xprt.probe_count_dec(op);
}
