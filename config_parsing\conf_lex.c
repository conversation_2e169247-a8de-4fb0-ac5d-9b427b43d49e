#line 2 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.c"

#line 4 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.c"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 1
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an unsigned
 * integer for use as an array index.  If the signed char is negative,
 * we want to instead treat it as an 8-bit unsigned char, hence the
 * double cast.
 */
#define YY_SC_TO_UI(c) ((unsigned int) (unsigned char) c)

/* An opaque pointer. */
#ifndef YY_TYPEDEF_YY_SCANNER_T
#define YY_TYPEDEF_YY_SCANNER_T
typedef void* yyscan_t;
#endif

/* For convenience, these vars (plus the bison vars far below)
   are macros in the reentrant scanner. */
#define yyin yyg->yyin_r
#define yyout yyg->yyout_r
#define yyextra yyg->yyextra_r
#define yyleng yyg->yyleng_r
#define yytext yyg->yytext_r
#define yylineno (YY_CURRENT_BUFFER_LVALUE->yy_bs_lineno)
#define yycolumn (YY_CURRENT_BUFFER_LVALUE->yy_bs_column)
#define yy_flex_debug yyg->yy_flex_debug_r

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN yyg->yy_start = 1 + 2 *

/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START ((yyg->yy_start - 1) / 2)
#define YYSTATE YY_START

/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)

/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE ganeshun_yyrestart(yyin ,yyscanner )

#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2

    /* Note: We specifically omit the test for yy_rule_can_match_eol because it requires
     *       access to the local variable yy_act. Since yyless() is a macro, it would break
     *       existing scanners that call yyless() from OUTSIDE ganeshun_yylex. 
     *       One obvious solution it to make yy_act a global. I tried that, and saw
     *       a 5% performance hit in a non-yylineno scanner, because yy_act is
     *       normally declared as a register variable-- so it is not worth it.
     */
    #define  YY_LESS_LINENO(n) \
            do { \
                yy_size_t yyl;\
                for ( yyl = n; yyl < yyleng; ++yyl )\
                    if ( yytext[yyl] == '\n' )\
                        --yylineno;\
            }while(0)
    #define YY_LINENO_REWIND_TO(dst) \
            do {\
                const char *p;\
                for ( p = yy_cp-1; p >= (dst); --p)\
                    if ( *p == '\n' )\
                        --yylineno;\
            }while(0)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        yy_size_t yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = yyg->yy_hold_char; \
		YY_RESTORE_YY_MORE_OFFSET \
		yyg->yy_c_buf_p = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )

#define unput(c) yyunput( c, yyg->yytext_ptr , yyscanner )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */
    
	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via ganeshun_yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( yyg->yy_buffer_stack \
                          ? yyg->yy_buffer_stack[yyg->yy_buffer_stack_top] \
                          : NULL)

/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE yyg->yy_buffer_stack[yyg->yy_buffer_stack_top]

void ganeshun_yyrestart (FILE *input_file ,yyscan_t yyscanner );
void ganeshun_yy_switch_to_buffer (YY_BUFFER_STATE new_buffer ,yyscan_t yyscanner );
YY_BUFFER_STATE ganeshun_yy_create_buffer (FILE *file,int size ,yyscan_t yyscanner );
void ganeshun_yy_delete_buffer (YY_BUFFER_STATE b ,yyscan_t yyscanner );
void ganeshun_yy_flush_buffer (YY_BUFFER_STATE b ,yyscan_t yyscanner );
void ganeshun_yypush_buffer_state (YY_BUFFER_STATE new_buffer ,yyscan_t yyscanner );
void ganeshun_yypop_buffer_state (yyscan_t yyscanner );

static void ganeshun_yyensure_buffer_stack (yyscan_t yyscanner );
static void ganeshun_yy_load_buffer_state (yyscan_t yyscanner );
static void ganeshun_yy_init_buffer (YY_BUFFER_STATE b,FILE *file ,yyscan_t yyscanner );

#define YY_FLUSH_BUFFER ganeshun_yy_flush_buffer(YY_CURRENT_BUFFER ,yyscanner)

YY_BUFFER_STATE ganeshun_yy_scan_buffer (char *base,yy_size_t size ,yyscan_t yyscanner );
YY_BUFFER_STATE ganeshun_yy_scan_string (yyconst char *yy_str ,yyscan_t yyscanner );
YY_BUFFER_STATE ganeshun_yy_scan_bytes (yyconst char *bytes,int len ,yyscan_t yyscanner );

void *ganeshun_yyalloc (yy_size_t ,yyscan_t yyscanner );
void *ganeshun_yyrealloc (void *,yy_size_t ,yyscan_t yyscanner );
void ganeshun_yyfree (void * ,yyscan_t yyscanner );

#define yy_new_buffer ganeshun_yy_create_buffer

#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        ganeshun_yyensure_buffer_stack (yyscanner); \
		YY_CURRENT_BUFFER_LVALUE =    \
            ganeshun_yy_create_buffer(yyin,YY_BUF_SIZE ,yyscanner); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}

#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        ganeshun_yyensure_buffer_stack (yyscanner); \
		YY_CURRENT_BUFFER_LVALUE =    \
            ganeshun_yy_create_buffer(yyin,YY_BUF_SIZE ,yyscanner); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}

#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

typedef unsigned char YY_CHAR;

typedef int yy_state_type;

#define yytext_ptr yytext_r

static yy_state_type yy_get_previous_state (yyscan_t yyscanner );
static yy_state_type yy_try_NUL_trans (yy_state_type current_state  ,yyscan_t yyscanner);
static int yy_get_next_buffer (yyscan_t yyscanner );
static void yynoreturn yy_fatal_error (yyconst char* msg ,yyscan_t yyscanner );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	yyg->yytext_ptr = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	yyg->yy_hold_char = *yy_cp; \
	*yy_cp = '\0'; \
	yyg->yy_c_buf_p = yy_cp;

#define YY_NUM_RULES 35
#define YY_END_OF_BUFFER 36
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static yyconst flex_int16_t yy_accept[517] =
    {   0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,   36,   34,   32,   33,   34,   12,   13,    9,
       10,   11,   34,    7,    8,   34,   34,   24,   18,   34,
       28,   23,   23,   23,   34,   29,   34,   29,   29,   29,
       29,   29,   29,   29,   34,   18,   34,    2,   34,   34,
       34,    0,   31,    0,    0,    7,    8,    0,   14,    0,
        0,   15,   30,   30,    0,    0,    0,   28,   28,   28,
       28,    0,   21,    0,    0,    0,    0,    0,   23,   19,
       26,   29,   27,   29,   29,   29,   17,   29,   16,   29,
       29,    0,    0,    0,    0,    2,    0,    0,    0,    0,

        0,    0,   14,    0,   28,   28,   28,   20,   20,   21,
        0,    0,    0,    0,   26,   22,   20,   23,    0,   26,
       26,   27,   29,   29,   29,    3,    0,    2,    0,    0,
        0,    4,   28,   28,   28,    0,   20,    0,    0,    0,
       20,   21,    0,    0,    0,    0,   26,   26,    0,   23,
       26,   26,   26,    0,   26,    0,   26,   29,   29,    0,
        0,    0,    0,   28,    0,    0,   20,    0,   20,   20,
       21,    0,    0,   26,   26,    0,   26,   20,   20,   23,
       26,   26,    0,   26,   26,   26,   26,    0,    0,    0,
        0,    0,    0,   20,   20,    0,    0,    0,   26,   26,

       26,   26,   26,   26,   20,    0,    0,   26,    0,   26,
        0,   26,    0,    0,    0,   24,   25,    0,    0,    0,
       26,   26,    0,   26,   26,   26,    0,   26,    0,    0,
        0,   26,   26,   26,   26,    0,    5,    1,    0,   25,
        0,    0,    0,   26,   26,   26,   26,   26,   26,   26,
       26,   26,   26,    0,    0,    0,    0,   26,    0,   26,
        0,   26,    0,   25,   25,   25,   25,    0,    0,   26,
       26,    0,   26,   26,   26,    0,   26,   26,   26,    0,
       26,   26,    0,    0,    0,    0,   26,   26,   26,   26,
        6,   25,   25,    0,    0,    0,   26,   26,   26,   26,

       26,   26,   26,   26,   26,   26,   26,   26,   26,   26,
       26,    0,    0,    0,    0,   26,    0,   26,    0,   26,
        0,    0,   26,   26,    0,   26,   26,   26,    0,   26,
       26,   26,    0,   26,   26,   26,    0,   26,   26,   26,
        0,    0,    0,    0,   26,   26,   26,   26,    0,    0,
        0,   26,   26,   26,   26,   26,   26,   26,   26,   26,
       26,   26,   26,   26,   26,   26,   26,   26,   26,   26,
        0,    0,    0,    0,   26,    0,   26,    0,   26,    0,
        0,   26,    0,   26,    0,   26,   26,   26,    0,   26,
       26,   26,    0,   26,   26,   26,    0,   26,   26,   26,

        0,   26,   26,   26,    0,    0,    0,    0,   26,   26,
       26,   26,    0,    0,    0,    0,    0,   26,   26,   26,
       26,   26,   26,   26,   26,   26,   26,   26,   26,   26,
       26,   26,   26,   26,    0,    0,    0,    0,   26,    0,
       26,    0,   26,    0,    0,    0,   26,   26,   26,   26,
       26,   26,   26,   26,   26,   26,   26,   26,   26,   26,
       26,    0,    0,    0,    0,   26,   26,   26,    0,    0,
        0,   26,   26,   26,   26,   26,   26,   26,   26,    0,
        0,    0,    0,   26,   26,    0,    0,   26,   26,   26,
       26,   26,   26,   26,   26,   26,    0,    0,    0,   26,

        0,    0,   26,    0,    0,   26,   26,    0,   26,   26,
        0,   26,   26,   26,   26,    0
    } ;

static yyconst YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    2,    2,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    4,    5,    6,    7,    8,    9,   10,   11,   12,
       13,   14,   15,   16,   17,   18,   19,   20,   21,   22,
       23,   23,   23,   23,   23,   24,   25,   26,   27,   28,
       29,   30,   31,   32,   33,   34,   34,   34,   35,   36,
       37,   37,   37,   37,   37,   38,   37,   39,   40,   41,
       37,   42,   43,   44,   45,   37,   37,   46,   47,   37,
       48,   49,   50,   51,   37,   52,   33,   34,   53,   54,

       55,   36,   37,   37,   56,   37,   37,   57,   37,   58,
       40,   41,   37,   59,   60,   44,   61,   37,   37,   46,
       47,   37,   62,   63,   64,   65,    1,   66,   66,   66,
       66,   66,   66,   66,   66,   66,   66,   66,   66,   66,
       66,   66,   66,   66,   66,   66,   66,   66,   66,   66,
       66,   66,   66,   66,   66,   66,   66,   66,   66,   66,
       66,   66,   66,   66,   66,   66,   66,   66,   66,   66,
       66,   66,   66,   66,   66,   66,   66,   66,   66,   66,
       66,   66,   66,   66,   66,   66,   66,   66,   66,   66,
       66,    1,    1,   67,   67,   67,   67,   67,   67,   67,

       67,   67,   67,   67,   67,   67,   67,   67,   67,   67,
       67,   67,   67,   67,   67,   67,   67,   67,   67,   67,
       67,   67,   67,   68,   68,   68,   68,   68,   68,   68,
       68,   68,   68,   68,   68,   68,   68,   68,   68,   69,
       69,   69,   69,   69,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static yyconst YY_CHAR yy_meta[70] =
    {   0,
        1,    1,    1,    1,    2,    1,    1,    1,    1,    3,
        1,    1,    1,    4,    1,    1,    5,    6,    7,    8,
        8,    8,    8,    8,    8,    9,    1,    1,    3,    1,
        4,    1,   10,   10,   10,   10,   11,   11,   11,   11,
       11,   11,   11,   11,   11,   11,   11,    4,    1,    1,
        1,    1,   10,   10,   10,   11,   11,   11,   11,   11,
       11,    1,    1,    1,    1,    1,    1,    1,    1
    } ;

static yyconst flex_uint16_t yy_base[612] =
    {   0,
        0,    6,   69,    0,  125,    0,  186,    0,  250,    0,
      310,  373, 1210, 3947, 3947, 3947, 1206, 3947, 3947, 3947,
     3947, 3947,    0,    0,    0,    0, 1197,    0,    5,    6,
      434,  490,   20, 1157, 1163,    3,    0,  538, 1145,  580,
     1135,   11,    4,    4, 1169, 3947, 1154, 1141, 1140, 1055,
     1054, 1099, 3947, 1028, 1026,    0,    0,    6, 3947,   71,
     1073, 3947,   44,   51, 1061,   69,    0,  619, 1046, 1043,
        0,  125,  661,  120, 1035,  697,  733, 1040,  237,   70,
      770,  105,    0,    0,  806,   22,    0, 1008,    0,   36,
       92, 1031,   82,    9,  987,  973,    0,  935,  927,  933,

      918,   46,   88,  273,  947,    0,  845,  307,  320,  887,
      328,  923,  334,  935,  960,  937,  915,  340,  361,  998,
      370,    0, 1034,  114,  120, 3947,    0,  906,  859,  871,
      839, 3947,    0,  876,  875,  383,  391,  399, 1070,  405,
      599,  680,  589,  689,  867,  851, 1108,  607,  625,  667,
     3947,  704,  716,  722, 1146, 1182,  752,  823,  243,   61,
      812,  816,  773,    0,  760,  789,  825,  832,    0,  797,
      807,  806,  790, 1219, 1257, 1293,  906,  942,  739,  914,
      776,  346,  949,  818,  838, 1331,  979,  789,  783,  725,
      851,  987,  893,  929, 1005,  745,  739,  723, 1369, 1017,

      972, 1045, 1407, 1025, 1052, 1058, 1089,    4, 1076, 1445,
     1481, 1097,  729,    0,  679, 1126, 1133, 1165,  687,  682,
     1518, 1556, 1592, 1172,   67, 1630, 1666, 1201, 1209,  681,
     1238, 1158, 1194, 1704, 1246,    0,    0, 3947, 1115, 1226,
      667,  645,  644, 1742, 1276, 1269, 1304, 1780, 1284, 1306,
     1315, 1818, 1350, 1315, 1358, 1338, 1388, 1381, 1394, 1856,
     1892, 1426,  663,  649, 1433,  243, 1463,  641,  625, 1929,
     1967, 2003, 1471, 1419, 2041, 2077, 1500, 1471, 2115, 2151,
     1508, 1488,  620, 1537,  608, 1545, 1530, 1567, 2189, 1576,
     3947,  260,  298,  592,  582,  564, 2227, 1611, 1569, 1604,

     2265, 1619, 1641, 1643, 2303, 1650, 1677, 1679, 2341, 1686,
     1583, 1452, 1723, 1656, 1731, 1716, 1692, 2379, 2415, 1761,
      563,  562, 2452, 2490, 2526, 1769, 1754, 2564, 2600, 1799,
     1792, 2638, 2674, 1807, 1829, 2712, 2748, 1838,  568, 1845,
      552, 1875,  550, 1883, 1831, 1868, 2786, 1911,  541,  540,
     2822, 2860, 1919, 1904, 1940, 2898, 1949, 1942, 1978, 2936,
     1987, 1980, 2014, 2974, 2023, 2016, 2052, 3012, 2061, 1956,
     1993, 2096, 2029, 2104, 2054, 2067, 3050, 3086, 2134,  539,
     3124,  535, 2142, 3162,    0, 2170, 2089, 3200,    0, 2178,
     2127, 3238,    0, 2208, 2163, 3276,    0, 2216, 2201, 3314,

        0, 2246,  534, 2253,  524, 2284,  523, 2292, 2239, 2277,
     3352, 2322,  514, 2310, 3390,  513, 2330,  503, 2352, 2354,
      501, 2363, 2365,  500, 2367, 2390,  499, 2392, 2401,  498,
     2403, 2405,  487, 2422, 2432, 2440, 2458, 2471, 2440, 2477,
     3428,    0, 2509, 2517,    0,  392,  486,  442,  417, 2502,
      414, 2537,  413, 2538,  412, 2539,  382, 2547,  380,  379,
     2582,  379, 2590,  378, 2619, 2549, 2551,  368, 2571, 2627,
      352,  354,  333,  328,  317,  316,  315,  314, 2607, 2645,
     2657, 2663, 2693, 2552,  305, 2701,  303,  296,  283,  282,
      281,  280,  258,  247,  158, 2681,  158, 2731,  136,  132,

     2719, 2739, 2755, 2765, 2773,  102, 2804,   73,   71, 2811,
       39, 2840, 2847,   13,    2, 3947, 3483, 3494, 3501, 3508,
     3519, 3530, 3538, 3546, 3554, 3556, 3566, 3573, 3580, 3591,
     3598, 3602, 3608, 3615, 3619, 3625, 3629, 3632, 3635, 3638,
     3643, 3646, 3649, 3654, 3662, 3666, 3669, 3674, 3682, 3689,
     3693, 3696, 3701, 3706, 3709, 3712, 3717, 3720, 3723, 3728,
     3733, 3738, 3741, 3744, 3749, 3752, 3757, 3762, 3767, 3772,
     3775, 3779, 3782, 3785, 3788, 3791, 3794, 3799, 3802, 3806,
     3811, 3815, 3820, 3824, 3829, 3833, 3838, 3842, 3847, 3851,
     3854, 3859, 3863, 3867, 3871, 3875, 3879, 3883, 3887, 3892,

     3896, 3900, 3904, 3908, 3912, 3916, 3920, 3924, 3928, 3932,
     3936
    } ;

static yyconst flex_int16_t yy_def[612] =
    {   0,
      517,  517,  517,    3,    3,    5,  516,    7,    5,    9,
      517,  517,  516,  516,  516,  516,  518,  516,  516,  516,
      516,  516,  516,  519,  520,  521,  522,  523,  524,  524,
      516,  516,   32,   33,  516,  525,  526,  516,   38,   38,
       40,   40,   40,   40,  527,  516,  528,  529,  529,  516,
      516,  518,  516,  516,  516,  519,  520,  521,  516,  530,
      522,  516,  523,  523,  527,  524,  531,   31,   31,   31,
       68,  524,   33,   73,  532,   72,   72,   72,   73,  524,
      516,  525,  533,   40,   38,   85,   40,   40,   40,   40,
       40,  527,  527,  528,  528,  529,  534,  516,  516,  516,

      516,  521,  521,  530,  531,   68,   68,   72,   72,   73,
      110,   76,   72,  535,  516,   77,  108,  110,  516,  516,
      120,  533,   85,   40,   40,  516,  536,  534,  516,  516,
      516,  516,  531,  107,  107,   72,   72,   72,   72,   72,
       72,   72,  142,  108,  537,  538,  516,  147,   72,  142,
      516,  516,  516,  516,  516,  516,  155,   40,   40,  536,
      516,  516,  516,  107,   72,   72,  142,  142,  139,   72,
      142,  539,  540,  516,  516,  516,  175,  142,   72,   72,
      516,  516,  516,  541,  541,  516,  186,  516,  516,  516,
       72,   72,   72,   72,   72,  516,  542,  543,  516,  199,

      544,  544,  516,  203,   72,  516,  516,  516,  516,  516,
      516,  210,  516,  545,  516,   72,   72,  142,  546,  547,
      516,  516,  516,  222,  516,  516,  516,  226,  516,  516,
      516,  548,  548,  516,  234,  549,  545,  516,  550,   72,
      516,  551,  552,  516,  244,  553,  553,  516,  248,  554,
      554,  516,  252,  516,  516,  516,  516,  516,  516,  516,
      516,  260,  549,  550,  550,  265,   72,  555,  556,  516,
      516,  516,  271,  516,  516,  516,  275,  516,  516,  516,
      279,  516,  516,  516,  516,  516,  557,  557,  516,  289,
      516,  265,  265,  516,  558,  559,  516,  297,  560,  560,

      516,  301,  561,  561,  516,  305,  562,  562,  516,  309,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  318,
      563,  564,  516,  516,  516,  324,  516,  516,  516,  328,
      516,  516,  516,  332,  516,  516,  516,  336,  516,  516,
      516,  516,  516,  516,  565,  565,  516,  347,  516,  566,
      516,  516,  352,  567,  567,  516,  356,  568,  568,  516,
      360,  569,  569,  516,  364,  570,  570,  516,  368,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  377,  571,
      516,  572,  381,  516,  573,  384,  516,  516,  574,  388,
      516,  516,  575,  392,  516,  516,  576,  396,  516,  516,

      577,  400,  516,  516,  516,  516,  516,  516,  578,  578,
      516,  411,  516,  516,  516,  579,  415,  580,  581,  581,
      582,  583,  583,  584,  585,  585,  586,  587,  587,  588,
      589,  589,  590,  516,  516,  516,  516,  516,  516,  516,
      516,  591,  441,  516,  592,  592,  593,  516,  594,  516,
      595,  516,  596,  516,  597,  516,  598,  516,  599,  516,
      516,  516,  516,  516,  516,  600,  600,  601,  516,  516,
      516,  602,  603,  604,  605,  606,  607,  608,  516,  516,
      516,  516,  516,  516,  609,  516,  516,  610,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  611,

      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,    0,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,

      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516
    } ;

static yyconst flex_uint16_t yy_nxt[4017] =
    {   0,
      516,   15,   16,   15,  516,   59,   17,   15,   16,   15,
      516,   59,   17,   63,  126,   18,   63,  414,   63,   63,
      119,   18,  119,   67,   67,  416,   19,  127,   20,  156,
       63,  119,   19,   63,   20,   63,   63,   78,   91,   79,
       79,   79,   79,   79,   79,   90,   88,   65,   60,   89,
       65,   59,   65,   65,   60,   54,  504,   63,   91,  124,
       55,   21,   90,   22,   63,   66,  126,   21,   89,   22,
       15,   16,   15,   58,   63,   17,  103,   23,  124,  127,
      125,   63,   63,   63,   18,  119,  516,   67,   67,  119,
      501,   65,  176,   59,   60,   19,  125,   20,   65,   63,

       63,   24,   24,   24,   24,   24,   24,   24,   24,   24,
       24,   24,   24,   24,   24,   24,   65,   65,   63,  104,
      119,   24,   24,   24,   24,   24,   24,   24,   24,   24,
       21,   63,   22,   14,   89,   63,   60,  113,   63,  111,
      111,  111,  111,   67,  108,  109,  109,  109,  109,  109,
      119,   89,   65,  482,   89,   63,  159,   25,   25,   25,
       25,   25,   25,   25,   25,   25,   25,   25,   25,   25,
       25,   25,   65,  159,   89,  480,  119,   25,   25,   25,
       25,   25,   25,   25,   25,   25,   14,   15,   16,   15,
       14,   26,   17,   14,   14,   14,   27,   14,   14,   28,

       14,   18,   29,   30,   31,   32,   33,   33,   33,   33,
       34,   35,   19,   14,   20,   14,   36,   37,   38,   38,
       38,   39,   40,   40,   41,   42,   40,   40,   40,   43,
       40,   40,   44,   45,   14,   14,   14,   14,   38,   38,
       38,   40,   40,   41,   40,   40,   40,   21,   14,   22,
       46,   14,   14,   14,   14,   47,  118,  118,  118,  118,
      118,  118,  264,  264,  264,  119,   48,   48,   49,   48,
       48,   48,   48,   48,   48,   58,  119,   87,  103,  264,
      264,  264,   48,   48,   48,   48,   48,   48,   48,   48,
       48,   48,   48,   48,   48,   48,   48,   87,  119,  119,

      119,  119,   48,   48,   48,   48,   48,   48,   48,   48,
       48,   15,   16,   15,  119,   50,   17,  264,  264,  264,
      469,  104,  105,  119,  136,   18,  137,  137,  137,  137,
      138,  138,  119,  119,  119,  119,   19,  140,   20,  141,
      141,  141,  141,  141,  141,  113,  119,  143,  143,  143,
      143,  119,  139,  144,  144,  144,  144,  144,  144,  150,
      150,  150,  150,  150,  150,  151,  151,  151,  151,  151,
       51,   21,  119,   22,   15,   16,   15,  416,   50,   17,
      151,  152,  153,  153,  153,  153,  119,  516,   18,  157,
      157,  157,  157,  157,  157,  437,  435,  119,  119,   19,

      119,   20,  165,  166,  166,  166,  166,  166,  140,  516,
      167,  167,  167,  167,  168,  168,  140,  416,  168,  168,
      168,  168,  168,  168,  166,  166,  166,  166,  166,  166,
      119,  119,  119,   51,   21,  119,   22,   68,   68,   68,
       68,   68,   68,   68,   68,   68,   68,   68,   68,   68,
       69,   69,   68,   70,   70,   70,   70,   70,   70,   68,
      119,   68,   68,   68,   68,   68,   70,   70,   70,   70,
       70,   70,   70,   70,   70,   70,   70,   70,   70,   70,
       70,   68,   68,   68,   68,   68,   70,   70,   70,   70,
       70,   70,   70,   70,   70,   68,   68,   68,   68,   71,

       68,   68,   68,   63,  119,  119,   66,   72,   67,   73,
       73,   73,   73,   74,   74,   75,  119,  119,  119,  119,
       63,  119,   76,   76,   76,   76,   66,   66,   66,   66,
       66,   66,   66,   66,   66,   77,   66,   65,  448,  351,
      373,  371,   76,   76,   76,   66,   66,   66,   66,   66,
       66,   63,  119,  119,   84,   84,   67,   85,   85,   85,
       85,   85,   85,   75,  351,  351,  296,  314,   63,  312,
       85,   85,   85,   85,   84,   84,   84,   84,   84,   84,
       84,   84,   84,   84,   84,   65,  119,  351,  296,  323,
       85,   85,   85,   84,   84,   84,   84,   84,   84,   84,

       84,   84,   84,   84,   84,  516,   66,  296,   66,   66,
       66,   66,   84,   84,   84,   84,  140,  243,  170,  170,
      170,  170,  170,  170,  516,  256,  177,  177,  177,  177,
      177,  177,   84,   84,   84,  106,  106,  254,   68,   68,
       68,   68,   68,   68,  178,  179,  179,  179,  179,  179,
      296,   68,   68,   68,   68,   68,   68,   68,   68,   68,
       68,   68,   68,   68,   68,   68,  243,  133,  291,  270,
      243,   68,   68,   68,   68,   68,   68,   68,   68,   68,
      110,  110,  110,  110,  111,  111,  180,  180,  180,  180,
      180,  180,  198,  112,  112,  112,  112,  149,  206,  171,

      171,  171,  171,   66,   66,   75,  140,  243,  138,  138,
      138,  138,  198,  112,  112,  112,  112,  112,  112,  112,
      112,  112,   75,  181,  181,  182,  151,  151,  151,  112,
      112,  112,  112,  238,   66,  151,  151,  151,  151,  151,
      151,  183,  183,  183,  183,  183,  183,  236,  221,  112,
      112,  112,  116,  116,  116,  116,  116,  116,  195,  195,
      195,  195,  195,  195,  198,  116,  116,  116,  116,  516,
      146,  185,  185,  185,  185,  185,  185,  191,  215,  192,
      192,  192,  192,  192,  192,  116,  116,  116,  119,  120,
      120,  120,  120,  120,  120,  151,  151,  151,  151,  151,

      151,  214,  121,  121,  121,  121,  193,  213,  192,  192,
      192,  192,  192,  192,  140,  198,  195,  195,  195,  195,
      195,  195,  121,  121,  121,  123,  123,  123,  123,  123,
      123,  146,  516,  190,  189,  154,  119,  188,  123,  123,
      123,  123,  140,  156,  194,  194,  194,  194,   75,  140,
      516,   66,   66,   66,   66,  516,  119,  516,  123,  123,
      123,  134,  134,  156,  135,  135,  135,  135,  135,  135,
      216,  217,  217,  217,  217,  217,  174,  135,  135,  135,
      135,  135,  135,  135,  135,  135,  135,  135,  135,  135,
      135,  135,  146,  164,  164,  163,  162,  135,  135,  135,

      135,  135,  135,  135,  135,  135,  142,  142,  142,  142,
      143,  143,  217,  217,  217,  217,  217,  217,  161,  143,
      143,  143,  143,  516,   97,  202,  202,  202,  202,  202,
      202,  149,  140,  180,  180,  180,  180,  180,  180,  143,
      143,  143,  143,  143,  143,  143,  143,  143,  194,  194,
      194,  194,   66,   66,  149,  143,  143,  143,  143,   66,
      146,  205,  205,  205,  205,  133,  206,  516,  207,  207,
      207,  207,  207,  207,  132,  143,  143,  143,  119,  147,
      147,  147,  147,  147,  147,  131,  130,  139,  129,  154,
      119,   97,  148,  148,  148,  148,  516,  176,  212,  212,

      212,  212,  212,  212,  193,  516,  218,  218,  218,  218,
      218,  218,  148,  148,  148,  154,  119,  155,  155,  155,
      155,  155,  155,  156,  195,  195,  195,  195,  195,  195,
      157,  157,  157,  157,  516,  516,  224,  224,  224,  224,
      224,  224,  516,   87,  228,  228,  228,  228,  228,  228,
      157,  157,  157,  158,  158,  158,  158,  158,  158,  117,
      115,  107,  516,  119,  107,   92,  158,  158,  158,  158,
      176,  194,  194,  194,  194,   66,   66,  229,  229,  229,
      229,  229,  229,   62,  101,  100,  158,  158,  158,  169,
      169,  169,  169,  169,  169,  231,  231,  231,  231,  231,

      231,   53,  169,  169,  169,  169,  206,   99,  230,  230,
      230,  230,  230,  230,  516,   98,  233,  233,  233,  233,
      233,  233,  169,  169,  169,  154,  119,  175,  175,  175,
      175,  175,  175,  176,  264,  265,  266,  266,  266,  266,
      177,  177,  177,  177,  239,  240,  240,  240,  240,  240,
      240,  239,  240,  240,  240,  240,  240,  240,  516,   97,
      177,  177,  177,  154,  119,  184,  184,  184,  184,  184,
      184,  156,   95,   92,   87,  209,  119,   86,  185,  185,
      185,  185,  193,  211,   66,   66,   66,   66,   81,  516,
      516,  247,  247,  247,  247,  247,  247,   80,  185,  185,

      185,  186,  186,  186,  186,  186,  186,   62,   53,  516,
      516,  516,  119,  516,  187,  187,  187,  187,  516,  211,
      251,  251,  251,  251,  251,  251,  254,  516,  255,  255,
      255,  255,  255,  255,  187,  187,  187,  119,  199,  199,
      199,  199,  199,  199,  239,  267,  267,  267,  267,  267,
      267,  200,  200,  200,  200,  256,  516,  257,  257,  257,
      257,  257,  257,  516,  516,  262,  262,  262,  262,  262,
      262,  200,  200,  200,  154,  119,  201,  201,  201,  201,
      201,  201,  176,  516,  516,  516,  154,  119,  516,  202,
      202,  202,  202,  516,  223,  273,  273,  273,  273,  273,

      273,  516,  516,  277,  277,  277,  277,  277,  277,  202,
      202,  202,  203,  203,  203,  203,  203,  203,  516,  516,
      516,  516,  119,  209,  119,  204,  204,  204,  204,  223,
      516,  227,  516,  119,  282,  282,  282,  282,  282,  282,
      227,  516,  516,  516,  516,  204,  204,  204,  209,  119,
      210,  210,  210,  210,  210,  210,  211,  284,  284,  284,
      284,  284,  284,  212,  212,  212,  212,  516,  516,  281,
      281,  281,  281,  281,  281,  254,  516,  283,  283,  283,
      283,  283,  283,  212,  212,  212,  154,  119,  222,  222,
      222,  222,  222,  222,  223,  516,  516,  516,  516,  119,

      516,  224,  224,  224,  224,  256,  211,  285,  285,  285,
      285,  285,  285,  286,  286,  286,  286,  286,  286,  516,
      516,  224,  224,  224,  209,  119,  226,  226,  226,  226,
      226,  226,  227,  516,  516,  516,  516,  119,  516,  228,
      228,  228,  228,  516,  223,  288,  288,  288,  288,  288,
      288,  133,  292,  292,  293,  264,  264,  264,  516,  228,
      228,  228,  209,  119,  232,  232,  232,  232,  232,  232,
      211,  340,  340,  340,  340,  340,  340,  233,  233,  233,
      233,  239,   66,   66,   66,   66,   66,   66,  516,  119,
      300,  300,  300,  300,  300,  300,  227,  233,  233,  233,

      234,  234,  234,  234,  234,  234,  119,  311,  311,  311,
      311,  311,  311,  235,  235,  235,  235,  516,  516,  304,
      304,  304,  304,  304,  304,  516,  516,  308,  308,  308,
      308,  308,  308,  235,  235,  235,  119,  244,  244,  244,
      244,  244,  244,  516,  516,  516,  516,  259,  119,  516,
      245,  245,  245,  245,  312,  261,  313,  313,  313,  313,
      313,  313,  314,  516,  315,  315,  315,  315,  315,  315,
      245,  245,  245,  154,  119,  246,  246,  246,  246,  246,
      246,  223,  516,  516,  516,  119,  154,  119,  247,  247,
      247,  247,  261,  516,  272,  320,  320,  320,  320,  320,

      320,  119,  339,  339,  339,  339,  339,  339,  247,  247,
      247,  248,  248,  248,  248,  248,  248,  516,  516,  516,
      516,  516,  119,  516,  249,  249,  249,  249,  516,  272,
      326,  326,  326,  326,  326,  326,  516,  516,  330,  330,
      330,  330,  330,  330,  249,  249,  249,  209,  119,  250,
      250,  250,  250,  250,  250,  227,  516,  516,  209,  119,
      516,  119,  251,  251,  251,  251,  276,  516,  276,  334,
      334,  334,  334,  334,  334,  342,  342,  342,  342,  342,
      342,  516,  251,  251,  251,  252,  252,  252,  252,  252,
      252,  516,  516,  516,  259,  119,  516,  119,  253,  253,

      253,  253,  280,  516,  280,  338,  338,  338,  338,  338,
      338,  344,  344,  344,  344,  344,  344,  516,  253,  253,
      253,  259,  119,  260,  260,  260,  260,  260,  260,  261,
      516,  516,  516,  516,  119,  516,  262,  262,  262,  262,
      312,  261,  341,  341,  341,  341,  341,  341,  314,  516,
      343,  343,  343,  343,  343,  343,  262,  262,  262,  154,
      119,  271,  271,  271,  271,  271,  271,  272,  516,  516,
      516,  516,  119,  516,  273,  273,  273,  273,  516,  272,
      346,  346,  346,  346,  346,  346,  516,  516,  355,  355,
      355,  355,  355,  355,  273,  273,  273,  209,  119,  275,

      275,  275,  275,  275,  275,  276,  516,  516,  516,  516,
      119,  516,  277,  277,  277,  277,  516,  276,  359,  359,
      359,  359,  359,  359,  516,  516,  363,  363,  363,  363,
      363,  363,  277,  277,  277,  259,  119,  279,  279,  279,
      279,  279,  279,  280,  516,  516,  516,  119,  317,  119,
      281,  281,  281,  281,  280,  516,  319,  367,  367,  367,
      367,  367,  367,  119,  370,  370,  370,  370,  370,  370,
      281,  281,  281,  259,  119,  287,  287,  287,  287,  287,
      287,  261,  516,  516,  516,  516,  119,  516,  288,  288,
      288,  288,  371,  319,  372,  372,  372,  372,  372,  372,

      373,  516,  374,  374,  374,  374,  374,  374,  288,  288,
      288,  289,  289,  289,  289,  289,  289,  516,  516,  516,
      516,  154,  119,  516,  290,  290,  290,  290,  516,  325,
      379,  379,  379,  379,  379,  379,  516,  516,  386,  386,
      386,  386,  386,  386,  290,  290,  290,  119,  297,  297,
      297,  297,  297,  297,  516,  516,  516,  516,  119,  209,
      119,  298,  298,  298,  298,  325,  516,  329,  390,  390,
      390,  390,  390,  390,  119,  403,  403,  403,  403,  403,
      403,  298,  298,  298,  154,  119,  299,  299,  299,  299,
      299,  299,  272,  516,  516,  516,  119,  259,  119,  300,

      300,  300,  300,  329,  516,  333,  394,  394,  394,  394,
      394,  394,  404,  404,  404,  404,  404,  404,  516,  300,
      300,  300,  301,  301,  301,  301,  301,  301,  516,  516,
      516,  516,  119,  317,  119,  302,  302,  302,  302,  333,
      516,  337,  398,  398,  398,  398,  398,  398,  406,  406,
      406,  406,  406,  406,  516,  302,  302,  302,  209,  119,
      303,  303,  303,  303,  303,  303,  276,  516,  516,  516,
      119,  516,  119,  304,  304,  304,  304,  337,  516,  319,
      402,  402,  402,  402,  402,  402,  408,  408,  408,  408,
      408,  408,  516,  304,  304,  304,  305,  305,  305,  305,

      305,  305,  516,  516,  516,  516,  516,  119,  516,  306,
      306,  306,  306,  371,  325,  405,  405,  405,  405,  405,
      405,  373,  516,  407,  407,  407,  407,  407,  407,  306,
      306,  306,  259,  119,  307,  307,  307,  307,  307,  307,
      280,  516,  516,  516,  516,  119,  516,  308,  308,  308,
      308,  516,  329,  410,  410,  410,  410,  410,  410,  516,
      516,  417,  417,  417,  417,  417,  417,  308,  308,  308,
      309,  309,  309,  309,  309,  309,  516,  516,  516,  516,
      516,  119,  516,  310,  310,  310,  310,  516,  333,  420,
      420,  420,  420,  420,  420,  516,  516,  423,  423,  423,

      423,  423,  423,  310,  310,  310,  317,  119,  318,  318,
      318,  318,  318,  318,  319,  516,  516,  516,  516,  119,
      516,  320,  320,  320,  320,  516,  337,  426,  426,  426,
      426,  426,  426,  516,  516,  429,  429,  429,  429,  429,
      429,  320,  320,  320,  154,  119,  324,  324,  324,  324,
      324,  324,  325,  516,  516,  516,  376,  119,  516,  326,
      326,  326,  326,  516,  378,  432,  432,  432,  432,  432,
      432,  119,  434,  434,  434,  434,  434,  434,  516,  326,
      326,  326,  209,  119,  328,  328,  328,  328,  328,  328,
      329,  516,  516,  516,  516,  119,  516,  330,  330,  330,

      330,  435,  378,  436,  436,  436,  436,  436,  436,  437,
      516,  438,  438,  438,  438,  438,  438,  330,  330,  330,
      259,  119,  332,  332,  332,  332,  332,  332,  333,  444,
      444,  444,  444,  444,  444,  334,  334,  334,  334,  516,
      516,  443,  443,  443,  443,  443,  443,  516,  516,  446,
      446,  446,  446,  446,  446,  334,  334,  334,  317,  119,
      336,  336,  336,  336,  336,  336,  337,  516,  516,  154,
      119,  516,  119,  338,  338,  338,  338,  385,  516,  385,
      209,  119,  516,  119,  259,  119,  516,  516,  389,  516,
      389,  516,  393,  338,  338,  338,  317,  119,  345,  345,

      345,  345,  345,  345,  319,  516,  516,  516,  119,  317,
      119,  346,  346,  346,  346,  393,  516,  397,  516,  119,
      376,  119,  516,  119,  516,  516,  397,  516,  401,  516,
      401,  346,  346,  346,  347,  347,  347,  347,  347,  347,
      119,  460,  460,  460,  460,  460,  460,  348,  348,  348,
      348,  461,  461,  461,  461,  461,  461,  435,  119,  462,
      462,  462,  462,  462,  462,  378,  516,  348,  348,  348,
      119,  352,  352,  352,  352,  352,  352,  463,  463,  463,
      463,  463,  463,  516,  353,  353,  353,  353,  437,  516,
      464,  464,  464,  464,  464,  464,  465,  465,  465,  465,

      465,  465,  516,  516,  353,  353,  353,  154,  119,  354,
      354,  354,  354,  354,  354,  325,  516,  516,  516,  516,
      119,  516,  355,  355,  355,  355,  516,  385,  467,  467,
      467,  467,  467,  467,  469,  516,  470,  470,  470,  470,
      470,  470,  355,  355,  355,  356,  356,  356,  356,  356,
      356,  516,  516,  516,  516,  119,  119,  119,  357,  357,
      357,  357,  389,  393,  397,  119,  440,  119,  516,  119,
      119,  516,  401,  516,  442,  516,  442,  442,  357,  357,
      357,  209,  119,  358,  358,  358,  358,  358,  358,  329,
      486,  486,  486,  486,  486,  486,  359,  359,  359,  359,

      119,  479,  479,  479,  479,  479,  479,  480,  516,  481,
      481,  481,  481,  481,  481,  516,  359,  359,  359,  360,
      360,  360,  360,  360,  360,  119,  495,  495,  495,  495,
      495,  495,  361,  361,  361,  361,  482,  516,  483,  483,
      483,  483,  483,  483,  469,  516,  487,  487,  487,  487,
      487,  487,  361,  361,  361,  259,  119,  362,  362,  362,
      362,  362,  362,  333,  496,  496,  496,  496,  496,  496,
      363,  363,  363,  363,  480,  516,  497,  497,  497,  497,
      497,  497,  498,  498,  498,  498,  498,  498,  516,  516,
      363,  363,  363,  364,  364,  364,  364,  364,  364,  119,

      503,  503,  503,  503,  503,  503,  365,  365,  365,  365,
      482,  516,  499,  499,  499,  499,  499,  499,  501,  516,
      502,  502,  502,  502,  502,  502,  365,  365,  365,  317,
      119,  366,  366,  366,  366,  366,  366,  337,  507,  507,
      507,  507,  507,  507,  367,  367,  367,  367,  504,  516,
      505,  505,  505,  505,  505,  505,  501,  516,  508,  508,
      508,  508,  508,  508,  367,  367,  367,  368,  368,  368,
      368,  368,  368,  119,  509,  509,  509,  509,  509,  509,
      369,  369,  369,  369,  510,  510,  510,  510,  510,  510,
      504,  516,  511,  511,  511,  511,  511,  511,  516,  516,

      369,  369,  369,  376,  119,  377,  377,  377,  377,  377,
      377,  378,  516,  516,  516,  516,  516,  516,  379,  379,
      379,  379,  119,  512,  512,  512,  512,  512,  512,  119,
      513,  513,  513,  513,  513,  513,  516,  516,  379,  379,
      379,  381,  381,  381,  381,  381,  381,  382,  516,  516,
      516,  516,  516,  516,  383,  383,  383,  383,  119,  514,
      514,  514,  514,  514,  514,  119,  515,  515,  515,  515,
      515,  515,  516,  516,  383,  383,  383,  154,  119,  384,
      384,  384,  384,  384,  384,  385,  516,  516,  516,  516,
      516,  516,  386,  386,  386,  386,  516,  516,  516,  516,

      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  386,  386,  386,  209,  119,  388,  388,  388,
      388,  388,  388,  389,  516,  516,  516,  516,  516,  516,
      390,  390,  390,  390,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      390,  390,  390,  259,  119,  392,  392,  392,  392,  392,
      392,  393,  516,  516,  516,  516,  516,  516,  394,  394,
      394,  394,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  394,  394,
      394,  317,  119,  396,  396,  396,  396,  396,  396,  397,

      516,  516,  516,  516,  516,  516,  398,  398,  398,  398,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  398,  398,  398,  376,
      119,  400,  400,  400,  400,  400,  400,  401,  516,  516,
      516,  516,  516,  516,  402,  402,  402,  402,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  402,  402,  402,  376,  119,  409,
      409,  409,  409,  409,  409,  378,  516,  516,  516,  516,
      516,  516,  410,  410,  410,  410,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,

      516,  516,  410,  410,  410,  411,  411,  411,  411,  411,
      411,  516,  516,  516,  516,  516,  516,  516,  412,  412,
      412,  412,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  412,  412,
      412,  414,  516,  415,  415,  415,  415,  415,  415,  416,
      516,  516,  516,  516,  516,  516,  417,  417,  417,  417,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  417,  417,  417,  154,
      119,  419,  419,  419,  419,  419,  419,  385,  516,  516,
      516,  516,  516,  516,  420,  420,  420,  420,  516,  516,

      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  420,  420,  420,  209,  119,  422,
      422,  422,  422,  422,  422,  389,  516,  516,  516,  516,
      516,  516,  423,  423,  423,  423,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  423,  423,  423,  259,  119,  425,  425,  425,
      425,  425,  425,  393,  516,  516,  516,  516,  516,  516,
      426,  426,  426,  426,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      426,  426,  426,  317,  119,  428,  428,  428,  428,  428,

      428,  397,  516,  516,  516,  516,  516,  516,  429,  429,
      429,  429,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  429,  429,
      429,  376,  119,  431,  431,  431,  431,  431,  431,  401,
      516,  516,  516,  516,  516,  516,  432,  432,  432,  432,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  432,  432,  432,  440,
      119,  441,  441,  441,  441,  441,  441,  442,  516,  516,
      516,  516,  516,  516,  443,  443,  443,  443,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,

      516,  516,  516,  516,  443,  443,  443,  414,  516,  445,
      445,  445,  445,  445,  445,  416,  516,  516,  516,  516,
      516,  516,  446,  446,  446,  446,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  446,  446,  446,  440,  119,  466,  466,  466,
      466,  466,  466,  442,  516,  516,  516,  516,  516,  516,
      467,  467,  467,  467,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      467,  467,  467,   14,   14,   14,   14,   14,   14,   14,
       14,   14,   14,   14,   52,   52,   52,   52,   52,   52,

       52,   52,   52,   52,   52,   56,  516,  516,   56,  516,
       56,   56,   57,  516,  516,   57,  516,   57,   57,   58,
       58,   58,   58,   58,   58,   58,   58,   58,   58,   58,
       61,   61,   61,   61,   61,   61,   61,   61,   61,   61,
       61,   64,   64,   64,  516,   64,  516,   64,   64,   66,
       66,   66,   66,   66,  516,   66,   66,   82,   82,   82,
      516,   82,  516,   82,   82,   83,   83,   93,  516,  516,
       93,   93,  516,   93,  516,   93,   93,   94,   94,   94,
       94,  516,   94,   94,   96,   96,   96,   96,  516,   96,
       96,  102,  102,  102,  102,  102,  102,  102,  102,  102,

      102,  102,  105,  105,  516,  105,  516,  105,  105,  114,
      114,  114,  122,  122,  516,  122,  516,  122,  122,  128,
      128,  516,  128,  516,  128,  128,  145,  145,  145,  160,
      160,  516,  160,  516,  160,  160,  172,  172,  172,  173,
      173,  173,  196,  196,  196,  197,  197,  197,  208,  208,
      208,  208,  208,  219,  219,  219,  220,  220,  220,  225,
      225,  225,  225,  225,  237,  516,  237,  237,  237,  237,
      516,  237,  237,  241,  241,  241,  242,  242,  242,  258,
      258,  258,  258,  258,  263,  516,  263,  263,  263,  263,
      516,  263,  263,  105,  105,  516,  105,  516,  105,  105,

      268,  268,  268,  269,  269,  269,  274,  274,  274,  274,
      274,  278,  278,  278,  278,  278,  294,  294,  294,  295,
      295,  295,  316,  316,  316,  316,  316,  321,  321,  321,
      322,  322,  322,  327,  327,  327,  327,  327,  331,  331,
      331,  331,  331,  335,  335,  335,  335,  335,  349,  349,
      349,  350,  350,  350,  375,  375,  375,  375,  375,  380,
      380,  380,  387,  387,  387,  387,  387,  391,  391,  391,
      391,  391,  395,  395,  395,  395,  395,  399,  399,  399,
      399,  399,  413,  413,  413,  418,  418,  516,  418,  421,
      516,  421,  424,  516,  424,  427,  516,  427,  430,  516,

      430,  433,  516,  433,  439,  439,  439,  439,  439,  447,
      447,  447,  449,  449,  516,  449,  450,  450,  450,  450,
      450,  451,  451,  516,  451,  452,  452,  452,  452,  452,
      453,  453,  516,  453,  454,  454,  454,  454,  454,  455,
      455,  516,  455,  456,  456,  456,  456,  456,  457,  457,
      516,  457,  458,  458,  458,  458,  458,  459,  459,  516,
      459,  468,  516,  468,  471,  516,  471,  471,  471,  472,
      472,  516,  472,  473,  473,  516,  473,  474,  474,  516,
      474,  475,  475,  516,  475,  476,  476,  516,  476,  477,
      477,  516,  477,  478,  478,  516,  478,  484,  484,  484,

      484,  484,  485,  485,  516,  485,  488,  488,  516,  488,
      489,  489,  516,  489,  490,  490,  516,  490,  491,  491,
      516,  491,  492,  492,  516,  492,  493,  493,  516,  493,
      494,  494,  516,  494,  500,  500,  516,  500,  448,  448,
      516,  448,  506,  506,  516,  506,   13,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,

      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516
    } ;

static yyconst flex_int16_t yy_chk[4017] =
    {   0,
        0,    1,    1,    1,    0,   26,    1,    2,    2,    2,
        0,   58,    2,   28,   94,    1,   36,  445,   29,   30,
      515,    2,  208,   29,   30,  445,    1,   94,    1,  208,
       28,  514,    2,   36,    2,   29,   30,   33,   44,   33,
       33,   33,   33,   33,   33,   43,   42,   28,   26,   42,
       36,  102,   29,   30,   58,   23,  511,   63,   44,   86,
       23,    1,   43,    1,   64,   33,  160,    2,   42,    2,
        3,    3,    3,   60,   63,    3,   60,    3,   86,  160,
       90,   64,   66,   80,    3,  225,   93,   66,   80,  509,
      508,   63,  225,  103,  102,    3,   90,    3,   64,   66,

       80,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,    3,    3,    3,    3,    3,   66,   80,   82,   60,
      506,    3,    3,    3,    3,    3,    3,    3,    3,    3,
        3,   93,    3,    5,   91,   82,  103,   74,   72,   74,
       74,   74,   74,   72,   72,   72,   72,   72,   72,   72,
      500,   91,   82,  499,  125,   72,  124,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    5,    5,    5,    5,
        5,    5,   72,  124,  125,  497,  495,    5,    5,    5,
        5,    5,    5,    5,    5,    5,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,

        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    7,    7,    7,    7,    7,
        7,    7,    7,    7,    7,    9,   79,   79,   79,   79,
       79,   79,  266,  266,  266,  494,    9,    9,    9,    9,
        9,    9,    9,    9,    9,  104,  493,  159,  104,  292,
      292,  292,    9,    9,    9,    9,    9,    9,    9,    9,
        9,    9,    9,    9,    9,    9,    9,  159,  492,  491,

      490,  489,    9,    9,    9,    9,    9,    9,    9,    9,
        9,   11,   11,   11,  488,   11,   11,  293,  293,  293,
      487,  104,  293,  485,  108,   11,  108,  108,  108,  108,
      108,  108,  478,  477,  476,  475,   11,  109,   11,  109,
      109,  109,  109,  109,  109,  111,  474,  111,  111,  111,
      111,  473,  108,  113,  113,  113,  113,  113,  113,  118,
      118,  118,  118,  118,  118,  182,  182,  182,  182,  182,
       11,   11,  472,   11,   12,   12,   12,  471,   12,   12,
      119,  119,  119,  119,  119,  119,  468,  121,   12,  121,
      121,  121,  121,  121,  121,  464,  462,  460,  459,   12,

      457,   12,  136,  136,  136,  136,  136,  136,  137,  446,
      137,  137,  137,  137,  137,  137,  138,  446,  138,  138,
      138,  138,  138,  138,  140,  140,  140,  140,  140,  140,
      455,  453,  451,   12,   12,  449,   12,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,
      448,   31,   31,   31,   31,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,
       31,   31,   31,   31,   31,   31,   31,   31,   31,   31,

       31,   31,   31,   32,  447,  433,   32,   32,   32,   32,
       32,   32,   32,   32,   32,   32,  430,  427,  424,  421,
       32,  418,   32,   32,   32,   32,   32,   32,   32,   32,
       32,   32,   32,   32,   32,   32,   32,   32,  416,  413,
      407,  405,   32,   32,   32,   32,   32,   32,   32,   32,
       32,   38,  403,  382,   38,   38,   38,   38,   38,   38,
       38,   38,   38,   38,  380,  350,  349,  343,   38,  341,
       38,   38,   38,   38,   38,   38,   38,   38,   38,   38,
       38,   38,   38,   38,   38,   38,  339,  322,  321,  296,
       38,   38,   38,   38,   38,   38,   38,   38,   38,   40,

       40,   40,   40,   40,   40,   40,  143,  295,  143,  143,
      143,  143,   40,   40,   40,   40,  141,  294,  141,  141,
      141,  141,  141,  141,  148,  285,  148,  148,  148,  148,
      148,  148,   40,   40,   40,   68,   68,  283,   68,   68,
       68,   68,   68,   68,  149,  149,  149,  149,  149,  149,
      269,   68,   68,   68,   68,   68,   68,   68,   68,   68,
       68,   68,   68,   68,   68,   68,  268,  264,  263,  243,
      242,   68,   68,   68,   68,   68,   68,   68,   68,   68,
       73,   73,   73,   73,   73,   73,  150,  150,  150,  150,
      150,  150,  241,   73,   73,   73,   73,  142,  230,  142,

      142,  142,  142,  142,  142,  142,  144,  220,  144,  144,
      144,  144,  219,   73,   73,   73,   76,   76,   76,   76,
       76,   76,   76,  152,  152,  152,  152,  152,  152,   76,
       76,   76,   76,  215,  144,  153,  153,  153,  153,  153,
      153,  154,  154,  154,  154,  154,  154,  213,  198,   76,
       76,   76,   77,   77,   77,   77,   77,   77,  179,  179,
      179,  179,  179,  179,  197,   77,   77,   77,   77,  157,
      196,  157,  157,  157,  157,  157,  157,  165,  190,  165,
      165,  165,  165,  165,  165,   77,   77,   77,   81,   81,
       81,   81,   81,   81,   81,  181,  181,  181,  181,  181,

      181,  189,   81,   81,   81,   81,  166,  188,  166,  166,
      166,  166,  166,  166,  170,  173,  170,  170,  170,  170,
      170,  170,   81,   81,   81,   85,   85,   85,   85,   85,
       85,  172,  171,  163,  162,  184,  184,  161,   85,   85,
       85,   85,  167,  184,  167,  167,  167,  167,  158,  168,
      167,  168,  168,  168,  168,  185,  185,  168,   85,   85,
       85,  107,  107,  185,  107,  107,  107,  107,  107,  107,
      191,  191,  191,  191,  191,  191,  146,  107,  107,  107,
      107,  107,  107,  107,  107,  107,  107,  107,  107,  107,
      107,  107,  145,  135,  134,  131,  130,  107,  107,  107,

      107,  107,  107,  107,  107,  107,  110,  110,  110,  110,
      110,  110,  193,  193,  193,  193,  193,  193,  129,  110,
      110,  110,  110,  177,  128,  177,  177,  177,  177,  177,
      177,  180,  117,  180,  180,  180,  180,  180,  180,  110,
      110,  110,  112,  112,  112,  112,  112,  112,  194,  194,
      194,  194,  194,  194,  116,  112,  112,  112,  112,  178,
      114,  178,  178,  178,  178,  105,  183,  178,  183,  183,
      183,  183,  183,  183,  101,  112,  112,  112,  115,  115,
      115,  115,  115,  115,  115,  100,   99,  178,   98,  201,
      201,   96,  115,  115,  115,  115,  187,  201,  187,  187,

      187,  187,  187,  187,  192,   95,  192,  192,  192,  192,
      192,  192,  115,  115,  115,  120,  120,  120,  120,  120,
      120,  120,  120,  120,  195,  195,  195,  195,  195,  195,
      120,  120,  120,  120,  200,   92,  200,  200,  200,  200,
      200,  200,  204,   88,  204,  204,  204,  204,  204,  204,
      120,  120,  120,  123,  123,  123,  123,  123,  123,   78,
       75,   70,  202,  202,   69,   65,  123,  123,  123,  123,
      202,  205,  205,  205,  205,  205,  205,  206,  206,  206,
      206,  206,  206,   61,   55,   54,  123,  123,  123,  139,
      139,  139,  139,  139,  139,  209,  209,  209,  209,  209,

      209,   52,  139,  139,  139,  139,  207,   51,  207,  207,
      207,  207,  207,  207,  212,   50,  212,  212,  212,  212,
      212,  212,  139,  139,  139,  147,  147,  147,  147,  147,
      147,  147,  147,  147,  239,  239,  239,  239,  239,  239,
      147,  147,  147,  147,  216,  216,  216,  216,  216,  216,
      216,  217,  217,  217,  217,  217,  217,  217,   49,   48,
      147,  147,  147,  155,  155,  155,  155,  155,  155,  155,
      155,  155,   47,   45,   41,  232,  232,   39,  155,  155,
      155,  155,  218,  232,  218,  218,  218,  218,   35,  224,
      218,  224,  224,  224,  224,  224,  224,   34,  155,  155,

      155,  156,  156,  156,  156,  156,  156,   27,   17,   13,
        0,  233,  233,    0,  156,  156,  156,  156,  228,  233,
      228,  228,  228,  228,  228,  228,  229,    0,  229,  229,
      229,  229,  229,  229,  156,  156,  156,  174,  174,  174,
      174,  174,  174,  174,  240,  240,  240,  240,  240,  240,
      240,  174,  174,  174,  174,  231,    0,  231,  231,  231,
      231,  231,  231,  235,    0,  235,  235,  235,  235,  235,
      235,  174,  174,  174,  175,  175,  175,  175,  175,  175,
      175,  175,  175,    0,    0,    0,  246,  246,    0,  175,
      175,  175,  175,  245,  246,  245,  245,  245,  245,  245,

      245,  249,    0,  249,  249,  249,  249,  249,  249,  175,
      175,  175,  176,  176,  176,  176,  176,  176,    0,    0,
        0,  247,  247,  250,  250,  176,  176,  176,  176,  247,
        0,  250,  251,  251,  254,  254,  254,  254,  254,  254,
      251,    0,    0,    0,    0,  176,  176,  176,  186,  186,
      186,  186,  186,  186,  186,  186,  186,  256,  256,  256,
      256,  256,  256,  186,  186,  186,  186,  253,    0,  253,
      253,  253,  253,  253,  253,  255,    0,  255,  255,  255,
      255,  255,  255,  186,  186,  186,  199,  199,  199,  199,
      199,  199,  199,  199,  199,    0,    0,    0,    0,  258,

        0,  199,  199,  199,  199,  257,  258,  257,  257,  257,
      257,  257,  257,  259,  259,  259,  259,  259,  259,    0,
        0,  199,  199,  199,  203,  203,  203,  203,  203,  203,
      203,  203,  203,    0,    0,    0,    0,  274,    0,  203,
      203,  203,  203,  262,  274,  262,  262,  262,  262,  262,
      262,  265,  265,  265,  265,  265,  265,  265,    0,  203,
      203,  203,  210,  210,  210,  210,  210,  210,  210,  210,
      210,  312,  312,  312,  312,  312,  312,  210,  210,  210,
      210,  267,  267,  267,  267,  267,  267,  267,  273,  278,
      273,  273,  273,  273,  273,  273,  278,  210,  210,  210,

      211,  211,  211,  211,  211,  211,  282,  282,  282,  282,
      282,  282,  282,  211,  211,  211,  211,  277,    0,  277,
      277,  277,  277,  277,  277,  281,    0,  281,  281,  281,
      281,  281,  281,  211,  211,  211,  221,  221,  221,  221,
      221,  221,  221,    0,    0,    0,    0,  287,  287,    0,
      221,  221,  221,  221,  284,  287,  284,  284,  284,  284,
      284,  284,  286,    0,  286,  286,  286,  286,  286,  286,
      221,  221,  221,  222,  222,  222,  222,  222,  222,  222,
      222,  222,    0,    0,  288,  288,  299,  299,  222,  222,
      222,  222,  288,  290,  299,  290,  290,  290,  290,  290,

      290,  311,  311,  311,  311,  311,  311,  311,  222,  222,
      222,  223,  223,  223,  223,  223,  223,    0,    0,    0,
        0,  300,  300,    0,  223,  223,  223,  223,  298,  300,
      298,  298,  298,  298,  298,  298,  302,    0,  302,  302,
      302,  302,  302,  302,  223,  223,  223,  226,  226,  226,
      226,  226,  226,  226,  226,  226,    0,    0,  303,  303,
      304,  304,  226,  226,  226,  226,  303,  306,  304,  306,
      306,  306,  306,  306,  306,  314,  314,  314,  314,  314,
      314,    0,  226,  226,  226,  227,  227,  227,  227,  227,
      227,    0,    0,    0,  307,  307,  308,  308,  227,  227,

      227,  227,  307,  310,  308,  310,  310,  310,  310,  310,
      310,  317,  317,  317,  317,  317,  317,    0,  227,  227,
      227,  234,  234,  234,  234,  234,  234,  234,  234,  234,
        0,    0,    0,    0,  316,    0,  234,  234,  234,  234,
      313,  316,  313,  313,  313,  313,  313,  313,  315,    0,
      315,  315,  315,  315,  315,  315,  234,  234,  234,  244,
      244,  244,  244,  244,  244,  244,  244,  244,    0,    0,
        0,    0,  327,    0,  244,  244,  244,  244,  320,  327,
      320,  320,  320,  320,  320,  320,  326,    0,  326,  326,
      326,  326,  326,  326,  244,  244,  244,  248,  248,  248,

      248,  248,  248,  248,  248,  248,    0,    0,    0,    0,
      331,    0,  248,  248,  248,  248,  330,  331,  330,  330,
      330,  330,  330,  330,  334,    0,  334,  334,  334,  334,
      334,  334,  248,  248,  248,  252,  252,  252,  252,  252,
      252,  252,  252,  252,    0,    0,    0,  335,  345,  345,
      252,  252,  252,  252,  335,  338,  345,  338,  338,  338,
      338,  338,  338,  340,  340,  340,  340,  340,  340,  340,
      252,  252,  252,  260,  260,  260,  260,  260,  260,  260,
      260,  260,    0,    0,    0,  346,  346,    0,  260,  260,
      260,  260,  342,  346,  342,  342,  342,  342,  342,  342,

      344,    0,  344,  344,  344,  344,  344,  344,  260,  260,
      260,  261,  261,  261,  261,  261,  261,    0,    0,    0,
        0,  354,  354,    0,  261,  261,  261,  261,  348,  354,
      348,  348,  348,  348,  348,  348,  353,    0,  353,  353,
      353,  353,  353,  353,  261,  261,  261,  270,  270,  270,
      270,  270,  270,  270,    0,    0,    0,  355,  355,  358,
      358,  270,  270,  270,  270,  355,  357,  358,  357,  357,
      357,  357,  357,  357,  370,  370,  370,  370,  370,  370,
      370,  270,  270,  270,  271,  271,  271,  271,  271,  271,
      271,  271,  271,    0,    0,  359,  359,  362,  362,  271,

      271,  271,  271,  359,  361,  362,  361,  361,  361,  361,
      361,  361,  371,  371,  371,  371,  371,  371,    0,  271,
      271,  271,  272,  272,  272,  272,  272,  272,    0,    0,
        0,  363,  363,  366,  366,  272,  272,  272,  272,  363,
      365,  366,  365,  365,  365,  365,  365,  365,  373,  373,
      373,  373,  373,  373,    0,  272,  272,  272,  275,  275,
      275,  275,  275,  275,  275,  275,  275,    0,    0,  367,
      367,    0,  375,  275,  275,  275,  275,  367,  369,  375,
      369,  369,  369,  369,  369,  369,  376,  376,  376,  376,
      376,  376,    0,  275,  275,  275,  276,  276,  276,  276,

      276,  276,    0,    0,    0,    0,    0,  387,    0,  276,
      276,  276,  276,  372,  387,  372,  372,  372,  372,  372,
      372,  374,    0,  374,  374,  374,  374,  374,  374,  276,
      276,  276,  279,  279,  279,  279,  279,  279,  279,  279,
      279,    0,    0,    0,    0,  391,    0,  279,  279,  279,
      279,  379,  391,  379,  379,  379,  379,  379,  379,  383,
        0,  383,  383,  383,  383,  383,  383,  279,  279,  279,
      280,  280,  280,  280,  280,  280,    0,    0,    0,    0,
        0,  395,    0,  280,  280,  280,  280,  386,  395,  386,
      386,  386,  386,  386,  386,  390,    0,  390,  390,  390,

      390,  390,  390,  280,  280,  280,  289,  289,  289,  289,
      289,  289,  289,  289,  289,    0,    0,    0,    0,  399,
        0,  289,  289,  289,  289,  394,  399,  394,  394,  394,
      394,  394,  394,  398,    0,  398,  398,  398,  398,  398,
      398,  289,  289,  289,  297,  297,  297,  297,  297,  297,
      297,  297,  297,    0,    0,    0,  409,  409,    0,  297,
      297,  297,  297,  402,  409,  402,  402,  402,  402,  402,
      402,  404,  404,  404,  404,  404,  404,  404,    0,  297,
      297,  297,  301,  301,  301,  301,  301,  301,  301,  301,
      301,    0,    0,    0,  410,  410,    0,  301,  301,  301,

      301,  406,  410,  406,  406,  406,  406,  406,  406,  408,
        0,  408,  408,  408,  408,  408,  408,  301,  301,  301,
      305,  305,  305,  305,  305,  305,  305,  305,  305,  414,
      414,  414,  414,  414,  414,  305,  305,  305,  305,  412,
        0,  412,  412,  412,  412,  412,  412,  417,    0,  417,
      417,  417,  417,  417,  417,  305,  305,  305,  309,  309,
      309,  309,  309,  309,  309,  309,  309,    0,    0,  419,
      419,  420,  420,  309,  309,  309,  309,  419,    0,  420,
      422,  422,  423,  423,  425,  425,    0,    0,  422,    0,
      423,    0,  425,  309,  309,  309,  318,  318,  318,  318,

      318,  318,  318,  318,  318,    0,    0,  426,  426,  428,
      428,  318,  318,  318,  318,  426,    0,  428,  429,  429,
      431,  431,  432,  432,    0,    0,  429,    0,  431,    0,
      432,  318,  318,  318,  319,  319,  319,  319,  319,  319,
      434,  434,  434,  434,  434,  434,  434,  319,  319,  319,
      319,  435,  435,  435,  435,  435,  435,  436,  439,  436,
      436,  436,  436,  436,  436,  439,    0,  319,  319,  319,
      323,  323,  323,  323,  323,  323,  323,  437,  437,  437,
      437,  437,  437,    0,  323,  323,  323,  323,  438,    0,
      438,  438,  438,  438,  438,  438,  440,  440,  440,  440,

      440,  440,    0,    0,  323,  323,  323,  324,  324,  324,
      324,  324,  324,  324,  324,  324,    0,    0,    0,    0,
      450,    0,  324,  324,  324,  324,  443,  450,  443,  443,
      443,  443,  443,  443,  444,    0,  444,  444,  444,  444,
      444,  444,  324,  324,  324,  325,  325,  325,  325,  325,
      325,    0,    0,    0,    0,  452,  454,  456,  325,  325,
      325,  325,  452,  454,  456,  458,  466,  466,  467,  467,
      484,    0,  458,    0,  466,    0,  467,  484,  325,  325,
      325,  328,  328,  328,  328,  328,  328,  328,  328,  328,
      469,  469,  469,  469,  469,  469,  328,  328,  328,  328,

      461,  461,  461,  461,  461,  461,  461,  463,    0,  463,
      463,  463,  463,  463,  463,    0,  328,  328,  328,  329,
      329,  329,  329,  329,  329,  479,  479,  479,  479,  479,
      479,  479,  329,  329,  329,  329,  465,    0,  465,  465,
      465,  465,  465,  465,  470,    0,  470,  470,  470,  470,
      470,  470,  329,  329,  329,  332,  332,  332,  332,  332,
      332,  332,  332,  332,  480,  480,  480,  480,  480,  480,
      332,  332,  332,  332,  481,    0,  481,  481,  481,  481,
      481,  481,  482,  482,  482,  482,  482,  482,    0,    0,
      332,  332,  332,  333,  333,  333,  333,  333,  333,  496,

      496,  496,  496,  496,  496,  496,  333,  333,  333,  333,
      483,    0,  483,  483,  483,  483,  483,  483,  486,    0,
      486,  486,  486,  486,  486,  486,  333,  333,  333,  336,
      336,  336,  336,  336,  336,  336,  336,  336,  501,  501,
      501,  501,  501,  501,  336,  336,  336,  336,  498,    0,
      498,  498,  498,  498,  498,  498,  502,    0,  502,  502,
      502,  502,  502,  502,  336,  336,  336,  337,  337,  337,
      337,  337,  337,  503,  503,  503,  503,  503,  503,  503,
      337,  337,  337,  337,  504,  504,  504,  504,  504,  504,
      505,    0,  505,  505,  505,  505,  505,  505,    0,    0,

      337,  337,  337,  347,  347,  347,  347,  347,  347,  347,
      347,  347,    0,    0,    0,    0,    0,    0,  347,  347,
      347,  347,  507,  507,  507,  507,  507,  507,  507,  510,
      510,  510,  510,  510,  510,  510,    0,    0,  347,  347,
      347,  351,  351,  351,  351,  351,  351,  351,    0,    0,
        0,    0,    0,    0,  351,  351,  351,  351,  512,  512,
      512,  512,  512,  512,  512,  513,  513,  513,  513,  513,
      513,  513,    0,    0,  351,  351,  351,  352,  352,  352,
      352,  352,  352,  352,  352,  352,    0,    0,    0,    0,
        0,    0,  352,  352,  352,  352,    0,    0,    0,    0,

        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,  352,  352,  352,  356,  356,  356,  356,  356,
      356,  356,  356,  356,    0,    0,    0,    0,    0,    0,
      356,  356,  356,  356,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
      356,  356,  356,  360,  360,  360,  360,  360,  360,  360,
      360,  360,    0,    0,    0,    0,    0,    0,  360,  360,
      360,  360,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,  360,  360,
      360,  364,  364,  364,  364,  364,  364,  364,  364,  364,

        0,    0,    0,    0,    0,    0,  364,  364,  364,  364,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,  364,  364,  364,  368,
      368,  368,  368,  368,  368,  368,  368,  368,    0,    0,
        0,    0,    0,    0,  368,  368,  368,  368,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,  368,  368,  368,  377,  377,  377,
      377,  377,  377,  377,  377,  377,    0,    0,    0,    0,
        0,    0,  377,  377,  377,  377,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,

        0,    0,  377,  377,  377,  378,  378,  378,  378,  378,
      378,    0,    0,    0,    0,    0,    0,    0,  378,  378,
      378,  378,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,  378,  378,
      378,  381,    0,  381,  381,  381,  381,  381,  381,  381,
        0,    0,    0,    0,    0,    0,  381,  381,  381,  381,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,  381,  381,  381,  384,
      384,  384,  384,  384,  384,  384,  384,  384,    0,    0,
        0,    0,    0,    0,  384,  384,  384,  384,    0,    0,

        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,  384,  384,  384,  388,  388,  388,
      388,  388,  388,  388,  388,  388,    0,    0,    0,    0,
        0,    0,  388,  388,  388,  388,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,  388,  388,  388,  392,  392,  392,  392,  392,
      392,  392,  392,  392,    0,    0,    0,    0,    0,    0,
      392,  392,  392,  392,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
      392,  392,  392,  396,  396,  396,  396,  396,  396,  396,

      396,  396,    0,    0,    0,    0,    0,    0,  396,  396,
      396,  396,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,  396,  396,
      396,  400,  400,  400,  400,  400,  400,  400,  400,  400,
        0,    0,    0,    0,    0,    0,  400,  400,  400,  400,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,  400,  400,  400,  411,
      411,  411,  411,  411,  411,  411,  411,  411,    0,    0,
        0,    0,    0,    0,  411,  411,  411,  411,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,

        0,    0,    0,    0,  411,  411,  411,  415,    0,  415,
      415,  415,  415,  415,  415,  415,    0,    0,    0,    0,
        0,    0,  415,  415,  415,  415,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,  415,  415,  415,  441,  441,  441,  441,  441,
      441,  441,  441,  441,    0,    0,    0,    0,    0,    0,
      441,  441,  441,  441,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
      441,  441,  441,  517,  517,  517,  517,  517,  517,  517,
      517,  517,  517,  517,  518,  518,  518,  518,  518,  518,

      518,  518,  518,  518,  518,  519,    0,    0,  519,    0,
      519,  519,  520,    0,    0,  520,    0,  520,  520,  521,
      521,  521,  521,  521,  521,  521,  521,  521,  521,  521,
      522,  522,  522,  522,  522,  522,  522,  522,  522,  522,
      522,  523,  523,  523,    0,  523,    0,  523,  523,  524,
      524,  524,  524,  524,    0,  524,  524,  525,  525,  525,
        0,  525,    0,  525,  525,  526,  526,  527,    0,    0,
      527,  527,    0,  527,    0,  527,  527,  528,  528,  528,
      528,    0,  528,  528,  529,  529,  529,  529,    0,  529,
      529,  530,  530,  530,  530,  530,  530,  530,  530,  530,

      530,  530,  531,  531,    0,  531,    0,  531,  531,  532,
      532,  532,  533,  533,    0,  533,    0,  533,  533,  534,
      534,    0,  534,    0,  534,  534,  535,  535,  535,  536,
      536,    0,  536,    0,  536,  536,  537,  537,  537,  538,
      538,  538,  539,  539,  539,  540,  540,  540,  541,  541,
      541,  541,  541,  542,  542,  542,  543,  543,  543,  544,
      544,  544,  544,  544,  545,    0,  545,  545,  545,  545,
        0,  545,  545,  546,  546,  546,  547,  547,  547,  548,
      548,  548,  548,  548,  549,    0,  549,  549,  549,  549,
        0,  549,  549,  550,  550,    0,  550,    0,  550,  550,

      551,  551,  551,  552,  552,  552,  553,  553,  553,  553,
      553,  554,  554,  554,  554,  554,  555,  555,  555,  556,
      556,  556,  557,  557,  557,  557,  557,  558,  558,  558,
      559,  559,  559,  560,  560,  560,  560,  560,  561,  561,
      561,  561,  561,  562,  562,  562,  562,  562,  563,  563,
      563,  564,  564,  564,  565,  565,  565,  565,  565,  566,
      566,  566,  567,  567,  567,  567,  567,  568,  568,  568,
      568,  568,  569,  569,  569,  569,  569,  570,  570,  570,
      570,  570,  571,  571,  571,  572,  572,    0,  572,  573,
        0,  573,  574,    0,  574,  575,    0,  575,  576,    0,

      576,  577,    0,  577,  578,  578,  578,  578,  578,  579,
      579,  579,  580,  580,    0,  580,  581,  581,  581,  581,
      581,  582,  582,    0,  582,  583,  583,  583,  583,  583,
      584,  584,    0,  584,  585,  585,  585,  585,  585,  586,
      586,    0,  586,  587,  587,  587,  587,  587,  588,  588,
        0,  588,  589,  589,  589,  589,  589,  590,  590,    0,
      590,  591,    0,  591,  592,    0,  592,  592,  592,  593,
      593,    0,  593,  594,  594,    0,  594,  595,  595,    0,
      595,  596,  596,    0,  596,  597,  597,    0,  597,  598,
      598,    0,  598,  599,  599,    0,  599,  600,  600,  600,

      600,  600,  601,  601,    0,  601,  602,  602,    0,  602,
      603,  603,    0,  603,  604,  604,    0,  604,  605,  605,
        0,  605,  606,  606,    0,  606,  607,  607,    0,  607,
      608,  608,    0,  608,  609,  609,    0,  609,  610,  610,
        0,  610,  611,  611,    0,  611,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,

      516,  516,  516,  516,  516,  516,  516,  516,  516,  516,
      516,  516,  516,  516,  516,  516
    } ;

/* Table of booleans, true if rule could match eol. */
static yyconst flex_int32_t yy_rule_can_match_eol[36] =
    {   0,
0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0,     };

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
#line 1 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
#line 2 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"

#pragma GCC diagnostic ignored "-Wunused-value"
#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-Wunused-function"

#include "config.h"
#include "config_parsing.h"
#include "conf_url.h"
#include "analyse.h"
#include "abstract_mem.h"
#include "conf_yacc.h"
#include "abstract_atomic.h"

#include <stdio.h>
#include <errno.h>
#include <stdlib.h>
#include <libgen.h>
#include "log.h"

#if HAVE_STRING_H
#   include <string.h>
#endif

/* Our versions of parser macros */

#define YY_USER_INIT \
do { \
	BEGIN YY_INIT; \
} while (0);

#define YY_USER_ACTION \
	yylloc->first_line = yylloc->last_line = yylineno; \
	yylloc->first_column = yylloc->last_column = yycolumn + yyleng -1; \
	yycolumn += yyleng; \
	yylloc->filename = stp->current_file;

#ifdef _DEBUG_PARSING
#define DEBUG_LEX   printf
#else
#define DEBUG_LEX(...) (void)0
#endif

#define BS_FLAG_NONE  0
#define BS_FLAG_URL   1

struct bufstack {
	struct bufstack *prev;
	YY_BUFFER_STATE bs;
	int lineno;
	char *filename;
	FILE *f;
	char *fbuf;
	uint32_t flags;
};

static char *filter_string(char *src, int esc);
static int new_file(char *filename, struct parser_state *st);
static int fetch_url(char *name_tok, struct parser_state *st);
static int pop_file(struct parser_state *st);

/* URL types, e.g., (uds|http|ftp) */
/* INCLUDE state is used for picking the name of the include file */

#line 1594 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.c"

#define INITIAL 0
#define YY_INIT 1
#define DEFINITION 2
#define TERM 3
#define INCLUDE 4
#define URL 5

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#define YY_EXTRA_TYPE struct parser_state *

/* Holds the entire state of the reentrant scanner. */
struct yyguts_t
    {

    /* User-defined. Not touched by flex. */
    YY_EXTRA_TYPE yyextra_r;

    /* The rest are the same as the globals declared in the non-reentrant scanner. */
    FILE *yyin_r, *yyout_r;
    size_t yy_buffer_stack_top; /**< index of top of stack. */
    size_t yy_buffer_stack_max; /**< capacity of stack. */
    YY_BUFFER_STATE * yy_buffer_stack; /**< Stack as an array. */
    char yy_hold_char;
    int yy_n_chars;
    int yyleng_r;
    char *yy_c_buf_p;
    int yy_init;
    int yy_start;
    int yy_did_buffer_switch_on_eof;
    int yy_start_stack_ptr;
    int yy_start_stack_depth;
    int *yy_start_stack;
    yy_state_type yy_last_accepting_state;
    char* yy_last_accepting_cpos;

    int yylineno_r;
    int yy_flex_debug_r;

    char *yytext_r;
    int yy_more_flag;
    int yy_more_len;

    YYSTYPE * yylval_r;

    YYLTYPE * yylloc_r;

    }; /* end struct yyguts_t */

static int yy_init_globals (yyscan_t yyscanner );

    /* This must go here because YYSTYPE and YYLTYPE are included
     * from bison output in section 1.*/
    #    define yylval yyg->yylval_r
    
    #    define yylloc yyg->yylloc_r
    
int ganeshun_yylex_init (yyscan_t* scanner);

int ganeshun_yylex_init_extra (YY_EXTRA_TYPE user_defined,yyscan_t* scanner);

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int ganeshun_yylex_destroy (yyscan_t yyscanner );

int ganeshun_yyget_debug (yyscan_t yyscanner );

void ganeshun_yyset_debug (int debug_flag ,yyscan_t yyscanner );

YY_EXTRA_TYPE ganeshun_yyget_extra (yyscan_t yyscanner );

void ganeshun_yyset_extra (YY_EXTRA_TYPE user_defined ,yyscan_t yyscanner );

FILE *ganeshun_yyget_in (yyscan_t yyscanner );

void ganeshun_yyset_in  (FILE * _in_str ,yyscan_t yyscanner );

FILE *ganeshun_yyget_out (yyscan_t yyscanner );

void ganeshun_yyset_out  (FILE * _out_str ,yyscan_t yyscanner );

			int ganeshun_yyget_leng (yyscan_t yyscanner );

char *ganeshun_yyget_text (yyscan_t yyscanner );

int ganeshun_yyget_lineno (yyscan_t yyscanner );

void ganeshun_yyset_lineno (int _line_number ,yyscan_t yyscanner );

int ganeshun_yyget_column  (yyscan_t yyscanner );

void ganeshun_yyset_column (int _column_no ,yyscan_t yyscanner );

YYSTYPE * ganeshun_yyget_lval (yyscan_t yyscanner );

void ganeshun_yyset_lval (YYSTYPE * yylval_param ,yyscan_t yyscanner );

       YYLTYPE *ganeshun_yyget_lloc (yyscan_t yyscanner );
    
        void ganeshun_yyset_lloc (YYLTYPE * yylloc_param ,yyscan_t yyscanner );
    
/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int ganeshun_yywrap (yyscan_t yyscanner );
#else
extern int ganeshun_yywrap (yyscan_t yyscanner );
#endif
#endif

#ifndef YY_NO_UNPUT
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy (char *,yyconst char *,int ,yyscan_t yyscanner);
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (yyconst char * ,yyscan_t yyscanner);
#endif

#ifndef YY_NO_INPUT

#ifdef __cplusplus
static int yyinput (yyscan_t yyscanner );
#else
static int input (yyscan_t yyscanner );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg , yyscanner)
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int ganeshun_yylex \
               (YYSTYPE * yylval_param,YYLTYPE * yylloc_param ,yyscan_t yyscanner);

#define YY_DECL int ganeshun_yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param , yyscan_t yyscanner)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

    yylval = yylval_param;

    yylloc = yylloc_param;

	if ( !yyg->yy_init )
		{
		yyg->yy_init = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! yyg->yy_start )
			yyg->yy_start = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			ganeshun_yyensure_buffer_stack (yyscanner);
			YY_CURRENT_BUFFER_LVALUE =
				ganeshun_yy_create_buffer(yyin,YY_BUF_SIZE ,yyscanner);
		}

		ganeshun_yy_load_buffer_state(yyscanner );
		}

	{
#line 156 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"


	struct parser_state *stp = yyextra;


#line 1886 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.c"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = yyg->yy_c_buf_p;

		/* Support of yytext. */
		*yy_cp = yyg->yy_hold_char;

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = yyg->yy_start;
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				yyg->yy_last_accepting_state = yy_current_state;
				yyg->yy_last_accepting_cpos = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 517 )
					yy_c = yy_meta[(unsigned int) yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + (flex_int16_t) yy_c];
			++yy_cp;
			}
		while ( yy_base[yy_current_state] != 3947 );

yy_find_action:
		yy_act = yy_accept[yy_current_state];
		if ( yy_act == 0 )
			{ /* have to back up */
			yy_cp = yyg->yy_last_accepting_cpos;
			yy_current_state = yyg->yy_last_accepting_state;
			yy_act = yy_accept[yy_current_state];
			}

		YY_DO_BEFORE_ACTION;

		if ( yy_act != YY_END_OF_BUFFER && yy_rule_can_match_eol[yy_act] )
			{
			yy_size_t yyl;
			for ( yyl = 0; yyl < yyleng; ++yyl )
				if ( yytext[yyl] == '\n' )
					   
    do{ yylineno++;
        yycolumn=0;
    }while(0)
;
			}

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = yyg->yy_hold_char;
			yy_cp = yyg->yy_last_accepting_cpos;
			yy_current_state = yyg->yy_last_accepting_state;
			goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 161 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* include file start */
	DEBUG_LEX("INCLUDE\n");
	BEGIN INCLUDE;
	/* not a token, return nothing */
}
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 167 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{

	{
		int c;

		DEBUG_LEX("Calling new_file with unquoted %s\n", yytext);
		c = new_file(yytext, stp);
		if (c == ENOMEM)
			yyterminate();
		BEGIN YY_INIT;
		DEBUG_LEX("done new file\n");
	}
}
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 181 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{
	{
		int c;

		DEBUG_LEX("Calling new_file with quoted %s\n", yytext);
		c = new_file(yytext, stp);
		if (c == ENOMEM)
			yyterminate();
		BEGIN YY_INIT;
		DEBUG_LEX("done new file\n");
	}
}
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 194 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* URL include file start */
	DEBUG_LEX("URL\n");
	BEGIN URL;
	/* not a token, return nothing */
}
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 200 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{

	{
		int c;

		DEBUG_LEX("Calling new_file with unquoted %s\n", yytext);
		c = fetch_url(yytext, stp);
		if (c == ENOMEM)
			yyterminate();
		BEGIN YY_INIT;
		DEBUG_LEX("done new file\n");
	}
}
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 214 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{
	{
		int c;

		DEBUG_LEX("Calling new_file with quoted %s\n", yytext);
		c = fetch_url(yytext, stp);
		if (c == ENOMEM)
			yyterminate();
		BEGIN YY_INIT;
		DEBUG_LEX("done new file\n");
	}
}
	YY_BREAK
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(YY_INIT):
case YY_STATE_EOF(DEFINITION):
case YY_STATE_EOF(TERM):
case YY_STATE_EOF(INCLUDE):
case YY_STATE_EOF(URL):
#line 227 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* end of included file */
	DEBUG_LEX("<EOF>\n");
	if (pop_file(stp) == 0)
		yyterminate();
}
	YY_BREAK
/* Initial State.  We start with a block identifier */
case 7:
YY_RULE_SETUP
#line 235 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* first block */
	/* identifier */
	DEBUG_LEX("[block:%s]\n",yytext);
	yylval->token = save_token(yytext, false, stp);
	BEGIN DEFINITION;
	return IDENTIFIER;
}
	YY_BREAK
case 8:
YY_RULE_SETUP
#line 243 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{
	DEBUG_LEX("[id:%s",yytext);
	yylval->token = save_token(yytext, false, stp);
	return IDENTIFIER;
}
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 249 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{
	DEBUG_LEX(" EQUALS ");
	BEGIN TERM;
	return EQUAL_OP;
}
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 255 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{
	DEBUG_LEX("BEGIN_BLOCK\n");
	BEGIN DEFINITION;
	stp->block_depth++;
	return LCURLY_OP;
}
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 262 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{   /* end of block */
	DEBUG_LEX("END_BLOCK\n");
	stp->block_depth --;
	if (stp->block_depth <= 0)
		BEGIN YY_INIT;
	return RCURLY_OP;
}
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 270 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* another terminal to follow ',' */
	DEBUG_LEX(" ',' ");
	return COMMA_OP;
}
	YY_BREAK
/* End of statement */
case 13:
YY_RULE_SETUP
#line 277 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* end of statement */
	DEBUG_LEX("]\n");
	BEGIN DEFINITION;
	return SEMI_OP;
}
	YY_BREAK
/* Double Quote, allows char escaping */
case 14:
/* rule 14 can match eol */
YY_RULE_SETUP
#line 286 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{  /* start of a double quote string */
	DEBUG_LEX("quote value:<%s>", yytext);
	yylval->token = save_token(yytext, true, stp);
	return DQUOTE;
}
	YY_BREAK
/* Single Quote, single line with no escaping */
case 15:
/* rule 15 can match eol */
YY_RULE_SETUP
#line 294 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* start of a single quote string */
	DEBUG_LEX("lit value:<%s>", yytext);
	yylval->token = save_token(yytext, false, stp);
	return SQUOTE;
}
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 300 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* a boolean TRUE */
	DEBUG_LEX("boolean TRUE:%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_TRUE;
}
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 306 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* a boolean FALSE */
	DEBUG_LEX("boolean FALSE:%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_FALSE;
}
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 312 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* an arithmetic op */
	DEBUG_LEX(" arith op:%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_ARITH_OP;
}
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 318 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* "9P" is here to take precedence over numbers, this is a special */
	DEBUG_LEX("token value:%s",yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOKEN;
}
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 324 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* an FSID */
	DEBUG_LEX(" FSID :%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_FSID;
}
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 330 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* an octal number */
	DEBUG_LEX(" octal number:%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_OCTNUM;
}
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 336 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* a hexidecimal number */
	DEBUG_LEX(" hex number:%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_HEXNUM;
}
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 342 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* a decimal number */
	DEBUG_LEX(" dec number:%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_DECNUM;
}
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 348 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* v4 address wildcard, gnfs only, not IETF */
	DEBUG_LEX(" V4 any:%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_V4_ANY;
}
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 354 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* V4 CIDR */
	DEBUG_LEX(" IPv4 :%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	if (index(yylval->token, '/') == NULL)
		return TOK_V4ADDR;
	else
		return TOK_V4CIDR;
}
	YY_BREAK
/* Mere mortals are not supposed to grok the pattern for IPV6ADDR. */
/* I got it from the Flex manual. */
case 26:
YY_RULE_SETUP
#line 366 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* V6 CIDR */
	DEBUG_LEX(" IPv6 :%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	if (index(yylval->token, '/') == NULL)
		return TOK_V6ADDR;
	else
		return TOK_V6CIDR;
}
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 375 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* a netgroup used for clients */
	DEBUG_LEX(" netgroup :%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_NETGROUP;
}
	YY_BREAK
/* Last resort terminals. PATHAME is here because it can confuse */
/* with a CIDR (precedence) and */
/* TOKEN_CHARS gobbles anything other than white and ";" */
case 28:
YY_RULE_SETUP
#line 385 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* a POSIX pathname */
	DEBUG_LEX("pathname:%s", yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOK_PATH;
}
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 391 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* start of a number or label/tag */
	DEBUG_LEX("token value:%s",yytext);
	yylval->token = save_token(yytext, false, stp);
	return TOKEN;
}
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 397 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* start of a number or label/tag as glob(7) string */
	DEBUG_LEX("token value:%s",yytext);
	yylval->token = save_token(yytext, false, stp);
	return REGEX_TOKEN;
}
	YY_BREAK
/* Skip over stuff we don't send upstairs */
case 31:
*yy_cp = yyg->yy_hold_char; /* undo effects of setting up yytext */
yyg->yy_c_buf_p = yy_cp -= 1;
YY_DO_BEFORE_ACTION; /* set up yytext again */
YY_RULE_SETUP
#line 405 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
;/* ignore */
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 406 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
;/* ignore */
	YY_BREAK
case 33:
/* rule 33 can match eol */
YY_RULE_SETUP
#line 407 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
;/* ignore */
	YY_BREAK
/* Unrecognized chars.  Must do better... */
case 34:
YY_RULE_SETUP
#line 411 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
{ /* ERROR: out of character character */
	DEBUG_LEX("unexpected stuff (%s)\n", yytext);
	config_parse_error(yylloc, stp,
		"Unexpected character (%s)", yytext);
	stp->err_type->scan = true;
	yylval->token = save_token(yytext, false, stp); /* for error rpt */
	return _ERROR_;
}
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 420 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"
ECHO;
	YY_BREAK
#line 2327 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.c"

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - yyg->yytext_ptr) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = yyg->yy_hold_char;
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * ganeshun_yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			yyg->yy_n_chars = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( yyg->yy_c_buf_p <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			yyg->yy_c_buf_p = yyg->yytext_ptr + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state( yyscanner );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state , yyscanner);

			yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++yyg->yy_c_buf_p;
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = yyg->yy_c_buf_p;
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer( yyscanner ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				yyg->yy_did_buffer_switch_on_eof = 0;

				if ( ganeshun_yywrap(yyscanner ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					yyg->yy_c_buf_p = yyg->yytext_ptr + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! yyg->yy_did_buffer_switch_on_eof )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				yyg->yy_c_buf_p =
					yyg->yytext_ptr + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state( yyscanner );

				yy_cp = yyg->yy_c_buf_p;
				yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				yyg->yy_c_buf_p =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars];

				yy_current_state = yy_get_previous_state( yyscanner );

				yy_cp = yyg->yy_c_buf_p;
				yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of ganeshun_yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = yyg->yytext_ptr;
	yy_size_t number_to_move, i;
	int ret_val;

	if ( yyg->yy_c_buf_p > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( yyg->yy_c_buf_p - yyg->yytext_ptr - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (yy_size_t) (yyg->yy_c_buf_p - yyg->yytext_ptr) - 1;

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) (yyg->yy_c_buf_p - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					ganeshun_yyrealloc((void *) b->yy_ch_buf,(yy_size_t) (b->yy_buf_size + 2) ,yyscanner );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			yyg->yy_c_buf_p = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			yyg->yy_n_chars, num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	if ( yyg->yy_n_chars == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			ganeshun_yyrestart(yyin  ,yyscanner);
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if ((int) (yyg->yy_n_chars + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = yyg->yy_n_chars + number_to_move + (yyg->yy_n_chars >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) ganeshun_yyrealloc((void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf,(yy_size_t) new_size ,yyscanner );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
	}

	yyg->yy_n_chars += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars + 1] = YY_END_OF_BUFFER_CHAR;

	yyg->yytext_ptr = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (yyscan_t yyscanner)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	yy_current_state = yyg->yy_start;

	for ( yy_cp = yyg->yytext_ptr + YY_MORE_ADJ; yy_cp < yyg->yy_c_buf_p; ++yy_cp )
		{
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			yyg->yy_last_accepting_state = yy_current_state;
			yyg->yy_last_accepting_cpos = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 517 )
				yy_c = yy_meta[(unsigned int) yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + (flex_int16_t) yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state , yyscan_t yyscanner)
{
	int yy_is_jam;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner; /* This var may be unused depending upon options. */
	char *yy_cp = yyg->yy_c_buf_p;

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		yyg->yy_last_accepting_state = yy_current_state;
		yyg->yy_last_accepting_cpos = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 517 )
			yy_c = yy_meta[(unsigned int) yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + (flex_int16_t) yy_c];
	yy_is_jam = (yy_current_state == 516);

	(void)yyg;
	return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (yyscan_t yyscanner)
#else
    static int input  (yyscan_t yyscanner)
#endif

{
	int c;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	*yyg->yy_c_buf_p = yyg->yy_hold_char;

	if ( *yyg->yy_c_buf_p == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( yyg->yy_c_buf_p < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] )
			/* This was really a NUL. */
			*yyg->yy_c_buf_p = '\0';

		else
			{ /* need more input */
			int offset = yyg->yy_c_buf_p - yyg->yytext_ptr;
			++yyg->yy_c_buf_p;

			switch ( yy_get_next_buffer( yyscanner ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					ganeshun_yyrestart(yyin ,yyscanner);

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( ganeshun_yywrap(yyscanner ) )
						return 0;

					if ( ! yyg->yy_did_buffer_switch_on_eof )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput(yyscanner);
#else
					return input(yyscanner);
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					yyg->yy_c_buf_p = yyg->yytext_ptr + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) yyg->yy_c_buf_p;	/* cast for 8-bit char's */
	*yyg->yy_c_buf_p = '\0';	/* preserve yytext */
	yyg->yy_hold_char = *++yyg->yy_c_buf_p;

	if ( c == '\n' )
		   
    do{ yylineno++;
        yycolumn=0;
    }while(0)
;

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * @param yyscanner The scanner object.
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void ganeshun_yyrestart  (FILE * input_file , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if ( ! YY_CURRENT_BUFFER ){
        ganeshun_yyensure_buffer_stack (yyscanner);
		YY_CURRENT_BUFFER_LVALUE =
            ganeshun_yy_create_buffer(yyin,YY_BUF_SIZE ,yyscanner);
	}

	ganeshun_yy_init_buffer(YY_CURRENT_BUFFER,input_file ,yyscanner);
	ganeshun_yy_load_buffer_state(yyscanner );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * @param yyscanner The scanner object.
 */
    void ganeshun_yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	/* TODO. We should be able to replace this entire function body
	 * with
	 *		ganeshun_yypop_buffer_state();
	 *		ganeshun_yypush_buffer_state(new_buffer);
     */
	ganeshun_yyensure_buffer_stack (yyscanner);
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*yyg->yy_c_buf_p = yyg->yy_hold_char;
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = yyg->yy_c_buf_p;
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	ganeshun_yy_load_buffer_state(yyscanner );

	/* We don't actually know whether we did this switch during
	 * EOF (ganeshun_yywrap()) processing, but the only time this flag
	 * is looked at is after ganeshun_yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	yyg->yy_did_buffer_switch_on_eof = 1;
}

static void ganeshun_yy_load_buffer_state  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	yyg->yy_n_chars = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	yyg->yytext_ptr = yyg->yy_c_buf_p = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	yyg->yy_hold_char = *yyg->yy_c_buf_p;
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * @param yyscanner The scanner object.
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE ganeshun_yy_create_buffer  (FILE * file, int  size , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) ganeshun_yyalloc(sizeof( struct yy_buffer_state ) ,yyscanner );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in ganeshun_yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) ganeshun_yyalloc((yy_size_t) (b->yy_buf_size + 2) ,yyscanner );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in ganeshun_yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	ganeshun_yy_init_buffer(b,file ,yyscanner);

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with ganeshun_yy_create_buffer()
 * @param yyscanner The scanner object.
 */
    void ganeshun_yy_delete_buffer (YY_BUFFER_STATE  b , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		ganeshun_yyfree((void *) b->yy_ch_buf ,yyscanner );

	ganeshun_yyfree((void *) b ,yyscanner );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a ganeshun_yyrestart() or at EOF.
 */
    static void ganeshun_yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file , yyscan_t yyscanner)

{
	int oerrno = errno;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	ganeshun_yy_flush_buffer(b ,yyscanner);

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then ganeshun_yy_init_buffer was _probably_
     * called from ganeshun_yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = file ? (isatty( fileno(file) ) > 0) : 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * @param yyscanner The scanner object.
 */
    void ganeshun_yy_flush_buffer (YY_BUFFER_STATE  b , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		ganeshun_yy_load_buffer_state(yyscanner );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  @param yyscanner The scanner object.
 */
void ganeshun_yypush_buffer_state (YY_BUFFER_STATE new_buffer , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if (new_buffer == NULL)
		return;

	ganeshun_yyensure_buffer_stack(yyscanner);

	/* This block is copied from ganeshun_yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*yyg->yy_c_buf_p = yyg->yy_hold_char;
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = yyg->yy_c_buf_p;
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		yyg->yy_buffer_stack_top++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from ganeshun_yy_switch_to_buffer. */
	ganeshun_yy_load_buffer_state(yyscanner );
	yyg->yy_did_buffer_switch_on_eof = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  @param yyscanner The scanner object.
 */
void ganeshun_yypop_buffer_state (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if (!YY_CURRENT_BUFFER)
		return;

	ganeshun_yy_delete_buffer(YY_CURRENT_BUFFER ,yyscanner);
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if (yyg->yy_buffer_stack_top > 0)
		--yyg->yy_buffer_stack_top;

	if (YY_CURRENT_BUFFER) {
		ganeshun_yy_load_buffer_state(yyscanner );
		yyg->yy_did_buffer_switch_on_eof = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void ganeshun_yyensure_buffer_stack (yyscan_t yyscanner)
{
	int num_to_alloc;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if (!yyg->yy_buffer_stack) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		yyg->yy_buffer_stack = (struct yy_buffer_state**)ganeshun_yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								, yyscanner);
		if ( ! yyg->yy_buffer_stack )
			YY_FATAL_ERROR( "out of dynamic memory in ganeshun_yyensure_buffer_stack()" );
								  
		memset(yyg->yy_buffer_stack, 0, num_to_alloc * sizeof(struct yy_buffer_state*));
				
		yyg->yy_buffer_stack_max = num_to_alloc;
		yyg->yy_buffer_stack_top = 0;
		return;
	}

	if (yyg->yy_buffer_stack_top >= (yyg->yy_buffer_stack_max) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = yyg->yy_buffer_stack_max + grow_size;
		yyg->yy_buffer_stack = (struct yy_buffer_state**)ganeshun_yyrealloc
								(yyg->yy_buffer_stack,
								num_to_alloc * sizeof(struct yy_buffer_state*)
								, yyscanner);
		if ( ! yyg->yy_buffer_stack )
			YY_FATAL_ERROR( "out of dynamic memory in ganeshun_yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset(yyg->yy_buffer_stack + yyg->yy_buffer_stack_max, 0, grow_size * sizeof(struct yy_buffer_state*));
		yyg->yy_buffer_stack_max = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object. 
 */
YY_BUFFER_STATE ganeshun_yy_scan_buffer  (char * base, yy_size_t  size , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) ganeshun_yyalloc(sizeof( struct yy_buffer_state ) ,yyscanner );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in ganeshun_yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	ganeshun_yy_switch_to_buffer(b ,yyscanner );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to ganeshun_yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       ganeshun_yy_scan_bytes() instead.
 */
YY_BUFFER_STATE ganeshun_yy_scan_string (yyconst char * yystr , yyscan_t yyscanner)
{
    
	return ganeshun_yy_scan_bytes(yystr,(int) strlen(yystr) ,yyscanner);
}

/** Setup the input buffer state to scan the given bytes. The next call to ganeshun_yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE ganeshun_yy_scan_bytes  (yyconst char * yybytes, int  _yybytes_len , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) ganeshun_yyalloc(n ,yyscanner );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in ganeshun_yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = ganeshun_yy_scan_buffer(buf,n ,yyscanner);
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in ganeshun_yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (yyconst char* msg , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	(void) fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        yy_size_t yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = yyg->yy_hold_char; \
		yyg->yy_c_buf_p = yytext + yyless_macro_arg; \
		yyg->yy_hold_char = *yyg->yy_c_buf_p; \
		*yyg->yy_c_buf_p = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the user-defined data for this scanner.
 * @param yyscanner The scanner object.
 */
YY_EXTRA_TYPE ganeshun_yyget_extra  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyextra;
}

/** Get the current line number.
 * @param yyscanner The scanner object.
 */
int ganeshun_yyget_lineno  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    
        if (! YY_CURRENT_BUFFER)
            return 0;
    
    return yylineno;
}

/** Get the current column number.
 * @param yyscanner The scanner object.
 */
int ganeshun_yyget_column  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    
        if (! YY_CURRENT_BUFFER)
            return 0;
    
    return yycolumn;
}

/** Get the input stream.
 * @param yyscanner The scanner object.
 */
FILE *ganeshun_yyget_in  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyin;
}

/** Get the output stream.
 * @param yyscanner The scanner object.
 */
FILE *ganeshun_yyget_out  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyout;
}

/** Get the length of the current token.
 * @param yyscanner The scanner object.
 */
int ganeshun_yyget_leng  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyleng;
}

/** Get the current token.
 * @param yyscanner The scanner object.
 */

char *ganeshun_yyget_text  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yytext;
}

/** Set the user-defined data. This data is never touched by the scanner.
 * @param user_defined The data to be associated with this scanner.
 * @param yyscanner The scanner object.
 */
void ganeshun_yyset_extra (YY_EXTRA_TYPE  user_defined , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyextra = user_defined ;
}

/** Set the current line number.
 * @param _line_number line number
 * @param yyscanner The scanner object.
 */
void ganeshun_yyset_lineno (int  _line_number , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        /* lineno is only valid if an input buffer exists. */
        if (! YY_CURRENT_BUFFER )
           YY_FATAL_ERROR( "ganeshun_yyset_lineno called with no buffer" );
    
    yylineno = _line_number;
}

/** Set the current column.
 * @param _column_no column number
 * @param yyscanner The scanner object.
 */
void ganeshun_yyset_column (int  _column_no , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        /* column is only valid if an input buffer exists. */
        if (! YY_CURRENT_BUFFER )
           YY_FATAL_ERROR( "ganeshun_yyset_column called with no buffer" );
    
    yycolumn = _column_no;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * @param yyscanner The scanner object.
 * @see ganeshun_yy_switch_to_buffer
 */
void ganeshun_yyset_in (FILE *  _in_str , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyin = _in_str ;
}

void ganeshun_yyset_out (FILE *  _out_str , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyout = _out_str ;
}

int ganeshun_yyget_debug  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yy_flex_debug;
}

void ganeshun_yyset_debug (int  _bdebug , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yy_flex_debug = _bdebug ;
}

/* Accessor methods for yylval and yylloc */

YYSTYPE * ganeshun_yyget_lval  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yylval;
}

void ganeshun_yyset_lval (YYSTYPE *  yylval_param , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yylval = yylval_param;
}

YYLTYPE *ganeshun_yyget_lloc  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yylloc;
}
    
void ganeshun_yyset_lloc (YYLTYPE *  yylloc_param , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yylloc = yylloc_param;
}
    
/* User-visible API */

/* ganeshun_yylex_init is special because it creates the scanner itself, so it is
 * the ONLY reentrant function that doesn't take the scanner as the last argument.
 * That's why we explicitly handle the declaration, instead of using our macros.
 */

int ganeshun_yylex_init(yyscan_t* ptr_yy_globals)

{
    if (ptr_yy_globals == NULL){
        errno = EINVAL;
        return 1;
    }

    *ptr_yy_globals = (yyscan_t) ganeshun_yyalloc ( sizeof( struct yyguts_t ), NULL );

    if (*ptr_yy_globals == NULL){
        errno = ENOMEM;
        return 1;
    }

    /* By setting to 0xAA, we expose bugs in yy_init_globals. Leave at 0x00 for releases. */
    memset(*ptr_yy_globals,0x00,sizeof(struct yyguts_t));

    return yy_init_globals ( *ptr_yy_globals );
}

/* ganeshun_yylex_init_extra has the same functionality as ganeshun_yylex_init, but follows the
 * convention of taking the scanner as the last argument. Note however, that
 * this is a *pointer* to a scanner, as it will be allocated by this call (and
 * is the reason, too, why this function also must handle its own declaration).
 * The user defined value in the first argument will be available to ganeshun_yyalloc in
 * the yyextra field.
 */

int ganeshun_yylex_init_extra(YY_EXTRA_TYPE yy_user_defined,yyscan_t* ptr_yy_globals )

{
    struct yyguts_t dummy_yyguts;

    ganeshun_yyset_extra (yy_user_defined, &dummy_yyguts);

    if (ptr_yy_globals == NULL){
        errno = EINVAL;
        return 1;
    }
	
    *ptr_yy_globals = (yyscan_t) ganeshun_yyalloc ( sizeof( struct yyguts_t ), &dummy_yyguts );
	
    if (*ptr_yy_globals == NULL){
        errno = ENOMEM;
        return 1;
    }
    
    /* By setting to 0xAA, we expose bugs in
    yy_init_globals. Leave at 0x00 for releases. */
    memset(*ptr_yy_globals,0x00,sizeof(struct yyguts_t));
    
    ganeshun_yyset_extra (yy_user_defined, *ptr_yy_globals);
    
    return yy_init_globals ( *ptr_yy_globals );
}

static int yy_init_globals (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from ganeshun_yylex_destroy(), so don't allocate here.
     */

    yyg->yy_buffer_stack = NULL;
    yyg->yy_buffer_stack_top = 0;
    yyg->yy_buffer_stack_max = 0;
    yyg->yy_c_buf_p = NULL;
    yyg->yy_init = 0;
    yyg->yy_start = 0;

    yyg->yy_start_stack_ptr = 0;
    yyg->yy_start_stack_depth = 0;
    yyg->yy_start_stack =  NULL;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * ganeshun_yylex_init()
     */
    return 0;
}

/* ganeshun_yylex_destroy is for both reentrant and non-reentrant scanners. */
int ganeshun_yylex_destroy  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		ganeshun_yy_delete_buffer(YY_CURRENT_BUFFER ,yyscanner );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		ganeshun_yypop_buffer_state(yyscanner);
	}

	/* Destroy the stack itself. */
	ganeshun_yyfree(yyg->yy_buffer_stack ,yyscanner);
	yyg->yy_buffer_stack = NULL;

    /* Destroy the start condition stack. */
        ganeshun_yyfree(yyg->yy_start_stack ,yyscanner );
        yyg->yy_start_stack = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * ganeshun_yylex() is called, initialization will occur. */
    yy_init_globals( yyscanner);

    /* Destroy the main struct (reentrant only). */
    ganeshun_yyfree ( yyscanner , yyscanner );
    yyscanner = NULL;
    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, yyconst char * s2, int n , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;

	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (yyconst char * s , yyscan_t yyscanner)
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *ganeshun_yyalloc (yy_size_t  size , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	return malloc(size);
}

void *ganeshun_yyrealloc  (void * ptr, yy_size_t  size , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;

	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void ganeshun_yyfree (void * ptr , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	free( (char *) ptr );	/* see ganeshun_yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 420 "/root/rpmbuild/BUILD/gnfs-4.0-*********/config_parsing/conf_lex.l"



int ganeshun_yywrap(void *yyscanner){
    return 1;
}

/*
 * This value represents a unique value for _this_ config_root. By tagging
 * each root with a value, we can propagate that value down to the structures
 * that this parsed tree touches. Then, later we can remove structures that
 * should no longer be present by looking to see if their generation number
 * predates this one.
 */
static uint64_t config_generation;

int ganeshun_yy_init_parser(char *srcfile, struct parser_state *st)
{
	FILE *in_file;
	void *yyscanner = st->scanner;
	/* reentrant scanner macro magic requires this... */
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	struct file_list *flist;
	struct config_root *confroot;
	YY_BUFFER_STATE inbuf;
	int rc = ENOMEM;

	confroot = gsh_calloc(1, sizeof(struct config_root));

	glist_init(&confroot->root.node);
	glist_init(&confroot->root.u.nterm.sub_nodes);
	confroot->root.type = TYPE_ROOT;
	confroot->generation = atomic_inc_uint64_t(&config_generation);
	st->root_node = confroot;
	ganeshun_yylex_init_extra(st, &st->scanner);
	rc = new_file(srcfile, st);
	if (rc == 0)
		confroot->root.filename = gsh_strdup(srcfile);
	return rc;
}

void ganeshun_yy_cleanup_parser(struct parser_state *st)
{
	int rc;

	if (st->curbs != NULL) {
		st->err_type->parse = true;
		while(pop_file(st) != 0);
	}
	ganeshun_yylex_destroy(st->scanner);
}

static int new_file(char *name_tok,
	     struct parser_state *st)
{
	struct bufstack *bs = NULL;
	FILE *in_file;
	YY_BUFFER_STATE inbuf;
	struct file_list *flist = NULL;
	struct file_list *fp;
	void *yyscanner = st->scanner;
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	struct config_root *confroot = st->root_node;
	char *fullpath = NULL;
	int rc = ENOMEM;
	char *filename;

	if (*name_tok == '\"') {
		 /* alloca'd memory freed on exit */
		filename = gsh_strdupa(name_tok + 1);
		filename[strlen(filename) - 1] = '\0';
	} else {
		/* alloca'd memory freed on exit */
		filename = gsh_strdupa(name_tok);
	}
	if (confroot->files == NULL) {
		if (filename[0] == '/') {
			char *path = gsh_strdupa(filename);
			confroot->conf_dir = gsh_strdup(dirname(path));
		} else {
			confroot->conf_dir = gsh_strdup(".");
		}
	}
	if (filename[0] == '/') {
		fullpath = gsh_strdup(filename);
	} else {
		fullpath = gsh_concat_sep(confroot->conf_dir, '/', filename);
	}
	/* loop detection */
	for (fp = confroot->files; fp != NULL; fp = fp->next) {
		if (!strcmp(fp->pathname, fullpath)) {
			config_parse_error(yylloc, st,
				"file (%s)already parsed, ignored",
				fullpath);
			rc = EINVAL;
			goto errout;
		}
	}
	bs = gsh_calloc(1, sizeof(struct bufstack));

	flist = gsh_calloc(1, sizeof(struct file_list));

	in_file = fopen(fullpath, "r" );
	if (in_file == NULL) {
		rc = errno;
		config_parse_error(yylloc, st,
			"new file (%s) open error (%s), ignored",
			fullpath, strerror(rc));
		goto errout;
	}
	bs->bs = ganeshun_yy_create_buffer(in_file,
					 YY_BUF_SIZE,
					 yyscanner);
	if (st->curbs)
		st->curbs->lineno = yylineno;
	bs->prev = st->curbs;
	bs->f = in_file;
	bs->filename = fullpath;
	ganeshun_yy_switch_to_buffer(bs->bs, yyscanner);
	st->current_file = fullpath;
	st->curbs = bs;
	flist->pathname = fullpath;
	flist->next = confroot->files;
	confroot->files = flist;
	return 0;

errout:
	if (rc == ENOMEM)
		st->err_type->resource = true;
	else
		st->err_type->scan = true;

	gsh_free(flist);
	gsh_free(bs);
	gsh_free(fullpath);

	return rc;
}

/* fetch_url */
static int fetch_url(char *name_tok, struct parser_state *st)
{
	struct bufstack *bs = NULL;
	YY_BUFFER_STATE inbuf;
	struct file_list *flist = NULL;
	struct file_list *fp;
	void *yyscanner = st->scanner;
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	struct config_root *confroot = st->root_node;
	char *filename = NULL;
	int rc = ENOMEM;

#ifdef NO_URL_RECURSION
	/* forbid URL chasing */
	if (st->curbs && (st->curbs->flags & BS_FLAG_URL)) {
		config_parse_error(yylloc, st,
			"new url (%s) transitive fetch from (%s), ignored",
			name_tok, st->curbs->filename);
		goto errout;
	}
#endif
	filename = gsh_strdup(name_tok);

	bs = gsh_calloc(1, sizeof(struct bufstack));
	flist = gsh_calloc(1, sizeof(struct file_list));

	rc = config_url_fetch(filename, &bs->f, &bs->fbuf);
	if (bs->f == NULL) {
		config_parse_error(yylloc, st,
			"new url (%s) open error (%s), ignored",
			filename, strerror(rc));
		goto errout;
	}
	bs->bs = ganeshun_yy_create_buffer(bs->f, YY_BUF_SIZE, yyscanner);
	if (st->curbs)
		st->curbs->lineno = yylineno;
	bs->prev = st->curbs;
	bs->filename = filename;
	bs->flags = BS_FLAG_URL;
	ganeshun_yy_switch_to_buffer(bs->bs, yyscanner);
	st->current_file = gsh_strdup(bs->filename);
	st->curbs = bs;
	flist->pathname = gsh_strdup(bs->filename); /* XXX */
	flist->next = confroot->files;
	confroot->files = flist;
	return 0;

errout:
	if (rc == ENOMEM)
		st->err_type->resource = true;
	else
		st->err_type->scan = true;

	gsh_free(flist);
	gsh_free(bs);
	gsh_free(filename);

	return rc;
} /* fetch_url() */

static int pop_file(struct parser_state *st)
{
	struct bufstack *bs = st->curbs;
	struct bufstack *prevbs;
	void *yyscanner = st->scanner;
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if (bs == NULL)
		return 0;
	if (bs->flags & BS_FLAG_URL) {
		config_url_release(bs->f, bs->fbuf);
	} else {
		fclose(bs->f);
	}
	ganeshun_yy_delete_buffer(bs->bs, yyscanner);
	prevbs = bs->prev;
	st->curbs = prevbs;
	gsh_free(bs);
	if (prevbs == NULL)
		return 0;
	ganeshun_yy_switch_to_buffer(prevbs->bs, yyscanner);
	yylineno = st->curbs->lineno;
	st->current_file = st->curbs->filename;
	return 1;
}

