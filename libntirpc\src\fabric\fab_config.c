#include "config.h"
#include "fab_config.h"

config *init_config(const struct rpc_rdma_attr *attr)
{
    config *conf = (config *)malloc(sizeof(config));

    conf->hints = fi_allocinfo();
    if (!conf->hints) {
        perror("fi_allocinfo");
        free(conf);
        return NULL;
    }

    conf->hints->addr_format = FI_SOCKADDR;
    conf->hints->fabric_attr->prov_name = strdup("verbs");
    conf->hints->mode = FI_RX_CQ_DATA | FI_CONTEXT;
    conf->hints->ep_attr->type = FI_EP_MSG;
    conf->hints->caps = FI_MSG | FI_RMA;
    conf->hints->domain_attr->mr_mode = FI_MR_LOCAL | FI_MR_ALLOCATED | FI_MR_PROV_KEY | FI_MR_VIRT_ADDR;
    if (attr->qp_sq_length != 0) {
        conf->hints->tx_attr->size = attr->qp_sq_length;
    } else {
        conf->hints->tx_attr->size = 4000;
    }

    if (!attr->enable_fabric_inline) {
        conf->hints->tx_attr->inject_size = 0;
    }

    if (attr->qp_rq_length != 0) {
        conf->hints->rx_attr->size = attr->qp_rq_length;
    } else {
        conf->hints->rx_attr->size = 1024;
    }

    if (attr->qp_cq_length != 0) {
        conf->cq_attr.size = attr->qp_cq_length;
    } else {
        conf->cq_attr.size = 1024;
    }
    conf->cq_attr.flags = 0;
    conf->cq_attr.format = FI_CQ_FORMAT_MSG;
    conf->cq_attr.wait_obj = FI_WAIT_UNSPEC;
    conf->cq_attr.signaling_vector = 0;
    conf->cq_attr.wait_cond = FI_CQ_COND_NONE;
    conf->cq_attr.wait_set = NULL;

    conf->eq_attr.size = 1024;
    conf->eq_attr.flags = FI_WAIT_NONE;
    conf->eq_attr.wait_obj = FI_WAIT_UNSPEC;
    conf->eq_attr.signaling_vector = 0;
    conf->eq_attr.wait_set = NULL;

    conf->wait_attr.wait_obj = FI_WAIT_NONE;
    conf->wait_attr.flags = 0;

    conf->poll_attr.flags = 0;

    if (attr->mempool_size != 0) {
        conf->pool_attr.count = attr->mempool_size;
    } else {
        conf->pool_attr.count = 16384;
    }

    if (attr->chunk_size != 0) {
        conf->pool_attr.chunk_size = attr->chunk_size;
    } else {
        conf->pool_attr.chunk_size = 65536;
    }

    if (attr->mem_align != 0) {
        conf->pool_attr.mem_align = attr->mem_align;
        conf->gane_pool_attr.mem_align = attr->mem_align;
    } else {
        conf->pool_attr.mem_align = 4096;
        conf->gane_pool_attr.mem_align = 4096;
    }

    if (attr->gane_pool_size != 0) {
        conf->gane_pool_attr.count = attr->gane_pool_size;
    } else {
        conf->gane_pool_attr.count = 2048;
    }

    if (attr->gane_chunk_size != 0) {
        conf->gane_pool_attr.chunk_size = attr->gane_chunk_size;
    } else {
        conf->gane_pool_attr.chunk_size = 1048576;
    }

    conf->pool_attr.use_hugepage = attr->use_hugepage;
    return conf;
}
