/*
 * Copyright (c) 2012-2014 CEA
 * contributeur : <PERSON> <<EMAIL>>
 * contributeur : <PERSON> <<EMAIL>>
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 * - Redistributions of source code must retain the above copyright notice,
 *   this list of conditions and the following disclaimer.
 * - Redistributions in binary form must reproduce the above copyright notice,
 *   this list of conditions and the following disclaimer in the documentation
 *   and/or other materials provided with the distribution.
 * - Neither the name of Sun Microsystems, Inc. nor the names of its
 *   contributors may be used to endorse or promote products derived
 *   from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

/**
 * \file    rpc_rdma.c
 * \brief   rdma helper
 *
 * This was (very) loosely based on the Mooshika library, which in turn
 * was a mix of diod, rping (librdmacm/examples), and Linux kernel's
 * net/9p/trans_rdma.c (dual BSD/GPL license). No vestiges remain.
 */

#if HAVE_CONFIG_H
#  include "config.h"
#endif

#include <stdio.h>	//printf
#include <limits.h>	//INT_MAX
#include <sys/socket.h> //sockaddr
#include <sys/un.h>     //sockaddr_un
#include <pthread.h>	//pthread_* (think it's included by another one)
#include <semaphore.h>  //sem_* (is it a good idea to mix sem and pthread_cond/mutex?)
#include <arpa/inet.h>  //inet_ntop
#include <netinet/in.h> //sock_addr_in
#include <unistd.h>	//fcntl
#include <fcntl.h>	//fcntl
#include <sys/epoll.h>
#include <urcu-bp.h>

#define EPOLL_SIZE (10)
/*^ expected number of fd, must be > 0 */
#define EPOLL_EVENTS (16)
/*^ maximum number of events per poll */
#define EPOLL_WAIT_MS (1000)
/*^ ms check for rpc_rdma_state.run_count (was 100) */
#define IBV_POLL_EVENTS (16)
/*^ maximum number of events per poll */
#define NSEC_IN_SEC (1000*1000*1000)

#include "misc/portable.h"
#include <rdma/rdma_cma.h>
#include <rpc/types.h>
#include <rpc/xdr.h>
#include <rpc/xdr_ioq.h>
#include <rpc/rpc.h>

#include "misc/abstract_atomic.h"
#include "../rpc_rdma.h"
#include "../svc_internal.h"
#include "../rpc_com.h"
#include "misc/city.h"
#include "../svc_internal.h"
#include "../svc_xprt.h"
#include <rpc/svc_rqst.h>
#include <rpc/svc_auth.h>

#include <rdma/fi_errno.h>
#include <rdma/fi_cm.h>
#include <rdma/fi_rma.h>
#include <assert.h>
#include "rdma.h"
#include "../fabric/fabric.h"

uint64_t FABRIC_IN_PROCESS_COUNT = 0;
#if 0
static void
rpcrdma_fabric_dump_msg(struct xdr_ioq_uv *data, char *comment, uint32_t xid)
{
	char *buffer;
	uint8_t *datum = data->v.vio_head;
	int sized = ioquv_length(data);
	int buffered = (((sized / DUMP_BYTES_PER_LINE) + 1 /*partial line*/)
			* (12 /* heading */
			   + (((DUMP_BYTES_PER_GROUP * 2 /*%02X*/) + 1 /*' '*/)
			      * DUMP_GROUPS_PER_LINE)))
			+ 1 /*'\0'*/;
	int i = 0;
	int m = 0;

	xid = ntohl(xid);
	if (sized == 0) {
		__warnx(TIRPC_DEBUG_FLAG_XDR,
			"NFS/FABRIC rpcrdma 0x%" PRIx32 "(%" PRIu32 ") %s?",
			xid, xid, comment);
		return;
	}
	buffer = (char *)mem_alloc(buffered);

	while (sized > i) {
		int j = sized - i;
		int k = j < DUMP_BYTES_PER_LINE ? j : DUMP_BYTES_PER_LINE;
		int l = 0;
		int r = sprintf(&buffer[m], "\n%10d:", i);	/* heading */

		if (r < 0)
			goto quit;
		m += r;

		for (; l < k; l++) {
			if (l % DUMP_BYTES_PER_GROUP == 0)
				buffer[m++] = ' ';

			r = sprintf(&buffer[m], "%02X", datum[i++]);
			if (r < 0)
				goto quit;
			m += r;
		}
	}
quit:
	buffer[m] = '\0';	/* in case of error */
	__warnx(TIRPC_DEBUG_FLAG_XDR,
		"NFS/RDMA rpcrdma 0x%" PRIx32 "(%" PRIu32 ") %s:%s\n",
		xid, xid, comment, buffer);
	mem_free(buffer, buffered);
}
#endif

struct _rpc_rdma_callback_args {
	void *ch;
	int state;
	void *callback_arg;
	int cnt;
	int reslut;
	struct iovec iov[];
};

void 
rpc_addrlist_hton(struct rpc_rdma_cbc *cbc) {

	struct xdr_write_list *write_chunk = (struct xdr_write_list *)cbc->write_chunk;
	struct xdr_write_list *reply_chunk = (struct xdr_write_list *)cbc->reply_chunk;
	if (write_chunk != NULL) {
		while (write_chunk->present != 0) {
			int i = 0;
			int elements = write_chunk->elements;
			for(; i < elements; i++) {
				write_chunk->entry[i].target.length = htonl(write_chunk->entry[i].target.length);
				write_chunk->entry[i].target.handle = htonl(write_chunk->entry[i].target.handle);
				xdr_encode_hyper((uint32_t *)&write_chunk->entry[i].target.offset, write_chunk->entry[i].target.offset);
				__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
						"NFS/FABRIC %s: write_chunk %p, position:%d, elements:%d,entry[%d]:{ handle:0x%x,length:%d,offset:%d }", __func__,
						write_chunk,
						write_chunk->present,
						write_chunk->elements,
						i,
						htonl(write_chunk->entry[i].target.handle),
						htonl(write_chunk->entry[i].target.length),
						xdr_decode_hyper(&(write_chunk->entry[i].target.offset)));
			}
			write_chunk->present = htonl(write_chunk->present);
			write_chunk->elements = htonl(write_chunk->elements);
			write_chunk = (struct xdr_write_list *)((char *)write_chunk + sizeof(struct xdr_write_list) + sizeof(struct xdr_write_chunk)* elements);
		}
	}
	if (reply_chunk != NULL) {
		if (reply_chunk->present != 0) {
			int i = 0;
			int elements = reply_chunk->elements;
			for(; i < elements; i++) {
				reply_chunk->entry[i].target.length = htonl(reply_chunk->entry[i].target.length);
				reply_chunk->entry[i].target.handle = htonl(reply_chunk->entry[i].target.handle);
				xdr_encode_hyper((uint32_t *)&reply_chunk->entry[i].target.offset, reply_chunk->entry[i].target.offset);
				__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
						"NFS/FABRIC %s: reply_chunk %p, position:%d, elements:%d,entry[%d]:{ handle:0x%x,length:%d,offset:%d }", __func__,
						reply_chunk,
						reply_chunk->present,
						reply_chunk->elements,
						i,
						htonl(reply_chunk->entry[i].target.handle),
						htonl(reply_chunk->entry[i].target.length),
						xdr_decode_hyper(&(reply_chunk->entry[i].target.offset)));
			}
			reply_chunk->present = htonl(reply_chunk->present);
			reply_chunk->elements = htonl(reply_chunk->elements);
			reply_chunk = (struct xdr_write_list *)((char *)reply_chunk + sizeof(struct xdr_write_list) + sizeof(struct xdr_write_chunk) * elements);
		}
	}

	return ;
}

void 
rpc_addrlist_ntoh(struct rpc_rdma_cbc *cbc) {
	struct xdr_write_list *write_chunk = (struct xdr_write_list *)cbc->write_chunk;
	struct xdr_write_list *reply_chunk = (struct xdr_write_list *)cbc->reply_chunk;
	int write_chunk_cnt = 0;;
	
	if (write_chunk != NULL) {
		write_chunk->present = ntohl(write_chunk->present);
		while (write_chunk->present != 0) {
			int i = 0;
			write_chunk->elements = ntohl(write_chunk->elements);
			for(; i < write_chunk->elements; i++) {
				write_chunk->entry[i].target.length = ntohl(write_chunk->entry[i].target.length);
				write_chunk->entry[i].target.handle = ntohl(write_chunk->entry[i].target.handle);
				write_chunk->entry[i].target.offset = xdr_decode_hyper(&write_chunk->entry[i].target.offset);
#if 0
				__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
						"%s() NFS/FABRIC [data] [write-chunk] %p, position:%d, elements:%d,entry[%d]:{ handle:0x%x,length:%d,offset:%d }", __func__,
						write_chunk,
						write_chunk->present,
						write_chunk->elements,
						i,
						write_chunk->entry[i].target.handle,
						write_chunk->entry[i].target.length,
						write_chunk->entry[i].target.offset);
#endif
			}
			write_chunk = (struct xdr_write_list *)((char *)write_chunk +
										sizeof(struct xdr_write_list) + 
										sizeof(struct xdr_write_chunk)*write_chunk->elements);
			write_chunk->present = ntohl(write_chunk->present);
			write_chunk_cnt ++;
		}
	}
	cbc->write_chunk_cnt = write_chunk_cnt;
	if (reply_chunk != NULL) {
		reply_chunk->present = ntohl(reply_chunk->present);
		if (reply_chunk->present != 0) {
			int i = 0;
			reply_chunk->elements = ntohl(reply_chunk->elements);
			for(; i < reply_chunk->elements; i++) {
				reply_chunk->entry[i].target.length = ntohl(reply_chunk->entry[i].target.length);
				reply_chunk->entry[i].target.handle = ntohl(reply_chunk->entry[i].target.handle);
				reply_chunk->entry[i].target.offset = xdr_decode_hyper(&reply_chunk->entry[i].target.offset);
#if 0
				__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
					"%s() NFS/FABRIC [add] [reply-chunk] %p, position:%d, elements:%d,entry[%d]:{ handle:0x%x,length:%d,offset:%d }", __func__,
					reply_chunk,
					reply_chunk->present,
					reply_chunk->elements,
					i,
					reply_chunk->entry[i].target.handle,
					reply_chunk->entry[i].target.length,
					reply_chunk->entry[i].target.offset);
#endif
			}
			reply_chunk = (struct xdr_write_list *)((char *)reply_chunk +
								sizeof(struct xdr_write_list) + 
								sizeof(struct xdr_write_chunk)*reply_chunk->elements);
			reply_chunk->present = ntohl(reply_chunk->present);
		}
	}
	return;
}

void 
rpc_addrlist_readlist_ntoh(struct rpc_rdma_cbc *cbc) {
	struct xdr_read_list *read_chunk = (struct xdr_read_list *)cbc->read_chunk;
	
	if (read_chunk != NULL) {
		read_chunk->present = ntohl(read_chunk->present);
		while (read_chunk->present != 0) {

			read_chunk->position = ntohl(read_chunk->present);
			read_chunk->target.handle = ntohl(read_chunk->target.handle);
			read_chunk->target.length = ntohl(read_chunk->target.length);
			read_chunk->target.offset = xdr_decode_hyper(&read_chunk->target.offset);
#if 0
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
					"%s() NFS/FABRIC [data] [read-chunk] position:%d, present:%d,target:{ handle:0x%x,length:%d,offset:%d }", __func__,
					read_chunk->position,
					read_chunk->present,
					read_chunk->target.handle,
					read_chunk->target.length,
					read_chunk->target.offset);
#endif
			read_chunk = (struct xdr_read_list *)((char *)read_chunk + sizeof(struct xdr_read_list));
			read_chunk->present = ntohl(read_chunk->present);
		}
	}
	return;
}


typedef enum rpc_fabric_stat_enum {
	RDMA_START = 0,
	RDMA_WAIT_READV = 1,
	RDMA_WAIT_WRITEV = 2,
	RDMA_WAIT_SENDDOEN = 3,
	RDMA_MAX,
}rpc_fabric_stat;

char *fabric_stat_str[RDMA_MAX]  = {
	[RDMA_START] = "start",
	[RDMA_WAIT_READV] = "readv callback",
	[RDMA_WAIT_WRITEV] = "waitv callback",
	[RDMA_WAIT_SENDDOEN] = "send success callback",
};



int
rpc_fabric_get_reply_memory(struct rpc_rdma_cbc *cbc)
{
	RDMAXPRT *xprt;
	struct xdr_write_list *reply_array;
	struct xdr_write_list *write_array;
	struct poolq_entry *have;


	write_array = (struct xdr_write_list *)cbc->write_chunk;
	reply_array = (struct xdr_write_list *)cbc->reply_chunk;
	xprt = x_xprt(cbc->recvbufq.xdrs);
	//xdr_ioq_release(&cbc->sendbufq.ioq_uv.uvqh);

	assert(cbc->sendbufq.ioq_uv.uvqh.qcount == 0);

	/* mem-rdma-ref: get-outbufs */
	have = rdma_ioq_get_sendbufq(&cbc->sendbufq, &rdma_xprt_class.rdmaxprt->outbufs.uvqh,
				"rpc-rdma-head buffer", 1, IOQ_FLAG_NONE);
	if(have == NULL){
		__warnx(TIRPC_DEBUG_FLAG_ERROR,"NFS/FABRIC %s: rpc-rdma-head buffer error, cbc %p outbufs.uvqh %p.", __func__, cbc, rdma_xprt_class.rdmaxprt->outbufs.uvqh);
		return -1;
	}
	struct poolq_entry *hold_have = TAILQ_FIRST(&cbc->sendbufq.ioq_uv.uvqh.qh);
	TAILQ_REMOVE(&cbc->sendbufq.ioq_uv.uvqh.qh, hold_have, q);
	(cbc->sendbufq.ioq_uv.uvqh.qcount)--;
	cbc->reply_rpc_head = (void *)hold_have;

	__warnx(TIRPC_DEBUG_FLAG_XDR,
			"NFS/FABRIC %s: cbc %p xdrs %p, get reply rpchead memory addr %p, get outbufs cnt %d.", __func__, cbc, &(cbc->sendbufq.xdrs[0]), IOQ_(have)->v.vio_base, 1);
	if (ntohl(reply_array->present) == 0)
	{
		/* no reply array to write, replying inline and hope it works
			* (OK on RPC/RDMA Read)
			*/
		/* mem-rdma-ref: get-out*/
		have = rdma_ioq_get_sendbufq(&cbc->sendbufq, &rdma_xprt_class.rdmaxprt->outbufs.uvqh,
					"reply-nfshead buffer", 1, IOQ_FLAG_NONE);
		if(have == NULL){
			__warnx(TIRPC_DEBUG_FLAG_ERROR,"NFS/FABRIC %s: reply-nfshead buffer error, cbc %p outbufs.uvqh %p.", __func__, cbc, rdma_xprt_class.rdmaxprt->outbufs.uvqh);
			return -1;
		}
		// todo-jerry get mem
		IOQ_(have)->v.vio_head =
		IOQ_(have)->v.vio_tail = IOQ_(have)->v.vio_base;
		IOQ_(have)->v.vio_wrap = (char *)IOQ_(have)->v.vio_base
					//+ RFC5666_BUFFER_SIZE;
					+ 1024;

		/* make room at head for RDMA header */
		//call_data rpc-rdma tail 
		//
		//xdr_ioq_reset(&cbc->sendbufq, (uintptr_t)cbc->call_data
		//			- (uintptr_t)cbc->write_chunk
		//			+ offsetof(struct rdma_msg, rdma_body));
		xdr_ioq_reset(&cbc->sendbufq, 0);
		__warnx(TIRPC_DEBUG_FLAG_XDR,
			"NFS/FABRIC %s: cbc %p  xdrs %p, xdrs.x_data %p, get reply nfshead memory addr %p, get outbufs cnt %d.", __func__, cbc, 
			&(cbc->sendbufq.xdrs[0]), cbc->sendbufq.xdrs[0].x_data, IOQ_(have)->v.vio_base, 1);
	}

#if 0
	if (ntohl(reply_array->present) != 0)  {
		uint32_t i;
		uint32_t l;
		uint32_t n = ntohl(reply_array->elements);

		if (!n) {
			__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"%s() NFS/FABRIC missing reply chunks",
				__func__);
			return ;
		}

		/* fetch all reply chunks in advance to avoid deadlock
		 * (there may be more than one)
		 */
		for (i = 0; i < n; i++) {
			l = ntohl(reply_array->entry[i].target.length);
			rdma_ioq_get_sendbufq_chunk(&cbc->sendbufq, &xprt->outbufs.uvqh,
					     "sreply chunk", l,
					 //    xprt->sm_dr.sendsz,
						 ((l < xprt->sm_dr.sendsz) ? l : xprt->sm_dr.sendsz),
					     xprt->xa->max_send_sge,
					     xdr_rdma_chunk_out);
			__warnx(TIRPC_DEBUG_FLAG_XDR,
				"NFS/FABRIC %s: cbc %p get reply memory addr %p, get outbufs cnt %d.", __func__, cbc, IOQ_(have)->v.vio_base, 1);
		}

		xdr_ioq_reset(&cbc->sendbufq, 0);
	}
#endif
	int status = 0;
	if (ntohl(write_array->present) != 0) {
		uint32_t i = 0; uint32_t tatol_len = 0; uint32_t n = ntohl(write_array->elements);
		for (i = 0; i < n; i++) {
			tatol_len += ntohl(write_array->entry[i].target.length);
		}
		__warnx(TIRPC_DEBUG_FLAG_XDR, "NFS/FABRIC  %s, cbc %p, write_array->present :%d, tatol_len :%d  xprt->sm_dr.sendsz :%d\n", __func__, cbc, ntohl(write_array->present), tatol_len, xprt->sm_dr.sendsz);
		status = rdma_ioq_get_sendbufq_chunk(&cbc->sendbufq, &rdma_xprt_class.rdmaxprt->outbufs.uvqh,
					 "sreply chunk", tatol_len,
					 ((tatol_len < xprt->sm_dr.sendsz) ? tatol_len : xprt->sm_dr.sendsz),
					 xprt->xa->max_send_sge,
					 xdr_rdma_chunk_out);
		if(status < 0)
			return status;
	}else if (ntohl(reply_array->present) != 0) {
		uint32_t i = 0; uint32_t tatol_len = 0; uint32_t n = ntohl(reply_array->elements);
		for (i = 0; i < n; i++) {
			tatol_len += ntohl(reply_array->entry[i].target.length);
		}
		__warnx(TIRPC_DEBUG_FLAG_XDR, "NFS/FABRIC  %s, cbc %p, reply_array-->present :%d, tatol_len :%d  xprt->sm_dr.sendsz :%d \n", __func__, cbc, ntohl(reply_array->present), tatol_len, xprt->sm_dr.sendsz);
		status = rdma_ioq_get_sendbufq_chunk(&cbc->sendbufq, &rdma_xprt_class.rdmaxprt->outbufs.uvqh,
					 "sreply chunk", tatol_len,
					 ((tatol_len < xprt->sm_dr.sendsz) ? tatol_len : xprt->sm_dr.sendsz),
					 xprt->xa->max_send_sge,
					 xdr_rdma_chunk_out);
		xdr_ioq_reset(&cbc->sendbufq, 0);
		if(status < 0)
			return status;
	}
	__warnx(TIRPC_DEBUG_FLAG_XDR, "NFS/FABRIC  %s, cbc %p, total get memory,  qcount %d. write_array->present :%d,  reply_array-->present :%d  \n", __func__, cbc, cbc->sendbufq.ioq_uv.uvqh.qcount,write_array->present, reply_array->present);

	//have = TAILQ_FIRST(&cbc->sendbufq.ioq_uv.uvqh.qh);
	//while (have) {
	//	have = TAILQ_NEXT(have, q);
	//	probe_count_inc(PROBE_COUNT_RDMA_MEM_OUT_USED);
	//}	
	return 0;
}

static int
rpc_fabric_start(RDMAXPRT *xd, void *callback_arg, struct rpc_rdma_cbc **out_cbc)
{
	struct rpc_rdma_cbc *cbc;
	struct poolq_entry *have =
		rdma_ioq_get_cbc(&xd->sm_dr.ioq, &rdma_xprt_class.rdmaxprt->cbqh,
				 "callq context", 1, IOQ_FLAG_NONE);
	if (have == NULL) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"func %s,() get cbc error!, reslut %d.\n", __func__);
		return -1;
	}
	cbc = (opr_containerof((_IOQ(have)), struct rpc_rdma_cbc, recvbufq));
	cbc->sendbufq.xdrs[0].x_lib[1] = cbc->recvbufq.xdrs[0].x_lib[1] = xd;
	cbc->state = RDMA_START;
	have = (struct poolq_entry *)callback_arg;
	struct xdr_ioq *xioq = &cbc->recvbufq;
	RDMAXPRT *xprt = x_xprt(cbc->recvbufq.xdrs);
	fabric_endpoint *fab_endp = (struct fabric_endpoint *)xprt->fd;
	//pthread_mutex_lock(&xioq->ioq_uv.uvqh.qmutex);
	(xioq->ioq_uv.uvqh.qcount)++;
	TAILQ_INSERT_TAIL(&xioq->ioq_uv.uvqh.qh, have, q);
	//pthread_mutex_unlock(&xioq->ioq_uv.uvqh.qmutex);
	fab_endp->in_process_count ++;
	*out_cbc = cbc;
 	//__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC  xprt:%p, cbc %p, recv count %d.", __func__, xd, cbc, cbc->recvbufq.ioq_uv.uvqh.qcount);	
	return 0;
}


static int
rpc_fabric_decode(RDMAXPRT *xd,  struct rpc_rdma_cbc *cbc)
{
	struct rdma_msg *cmsg;

	probe_time_begin(xd, cbc, RPC_fabric_decode); /* rpc_fabric_decode 3*/ 

	XDR *xdrs = &cbc->recvbufq.xdrs[0];
	now(&xdrs->recv_start);


	cbc->call_uv = IOQ_(TAILQ_FIRST(&cbc->recvbufq.ioq_uv.uvqh.qh));
	//(cbc->call_uv->u.uio_references)++;
	cbc->call_head = cbc->call_uv->v.vio_head;

	__warnx(TIRPC_DEBUG_FLAG_XDR,
			"NFS/FABRIC %s: decode  xdr_svc_recv cbc %p, head %p.", __func__, cbc, cbc->call_head);

	cmsg = (struct rdma_msg *)(cbc->call_head);
	//rpcrdma_dump_msg(cbc->call_uv, "call", cmsg->rdma_xid);

	switch (ntohl(cmsg->rdma_vers)) {
	case 1:
//		__warnx(TIRPC_DEBUG_FLAG_XDR,
//				"NFS/FABRIC %s: rdma_vers %.d.", __func__, ntohl(cmsg->rdma_vers));
		break;
	default:
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"NFS/FABRIC %s: xd:%p, rdma_vers faild %" PRIu32 "?", __func__, xd, ntohl(cmsg->rdma_vers));
		probe_time_end(xd, cbc, RPC_fabric_decode); /* rpc_fabric_decode 3*/
		//SVC_RELEASE(&xd->sm_dr.xprt, SVC_REF_FLAG_NONE);
		return (-1);
	}
	/* locate NFS/RDMA (RFC-5666) chunk positions */
	cbc->read_chunk = xdr_rdma_get_read_list(cmsg);
	cbc->write_chunk = (wl_t *)cbc->read_chunk;
	xdr_rdma_skip_read_list((uint32_t **)&cbc->write_chunk);
	cbc->reply_chunk = cbc->write_chunk;
	xdr_rdma_skip_write_list((uint32_t **)&cbc->reply_chunk);
	cbc->call_data = cbc->reply_chunk;
	xdr_rdma_skip_reply_array((uint32_t **)&cbc->call_data);


	/* skip past the header for the calling buffer */
	xdr_ioq_reset(&cbc->recvbufq, ((uintptr_t)cbc->call_data
				  - (uintptr_t)cmsg));
#if 1
	cbc->invalidate_rkey = 0;
	struct xdr_read_list *read_chunk = (struct xdr_read_list *)cbc->read_chunk;
	struct xdr_write_list *write_chunk = (struct xdr_write_list *)cbc->write_chunk;
	struct xdr_write_list *reply_chunk = (struct xdr_write_list *)cbc->reply_chunk;
	while (ntohl(read_chunk->present) != 0) {
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		//__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"NFS/FABRIC %s: read_chunk present:%d, position:%d, target:{ handle:0x%x,length:%d,offset:0x%x }", __func__,
				ntohl(read_chunk->present),
				ntohl(read_chunk->position),
				ntohl(read_chunk->target.handle),
				ntohl(read_chunk->target.length),
				ntohl64(read_chunk->target.offset));
		if (cbc->invalidate_rkey == 0)
			cbc->invalidate_rkey = ntohl(read_chunk->target.handle);
		read_chunk = (struct xdr_read_list *)((char *)read_chunk + sizeof(struct xdr_read_list));
		
	}


	while (ntohl(write_chunk->present) != 0) {
		int i = 0, elements= ntohl(write_chunk->elements);
		cbc->writelist = (struct xdr_rdma_segment *)malloc(sizeof(struct xdr_rdma_segment) * elements);
		cbc->writelist_cnt = elements;
		for(; i < elements; i++) {
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		//__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"NFS/FABRIC %s: write_chunk present:%d, elements:%d, entry[%d]:{ handle:0x%x,length:%d,offset:0x%x }", __func__,
				ntohl(write_chunk->present),
				ntohl(write_chunk->elements),
				i,
				ntohl(write_chunk->entry[i].target.handle),
				ntohl(write_chunk->entry[i].target.length),
				ntohl64(write_chunk->entry[i].target.offset));
			cbc->writelist[i].handle = ntohl(write_chunk->entry[i].target.handle);
			cbc->writelist[i].length = ntohl(write_chunk->entry[i].target.length);
			cbc->writelist[i].offset = ntohl64(write_chunk->entry[i].target.offset);
			if (cbc->invalidate_rkey == 0)
				cbc->invalidate_rkey = ntohl(write_chunk->entry[i].target.handle);
			if (cbc->writelist[i].length == 0)
				return -1;
		}
		write_chunk = (struct xdr_write_list *)((char *)write_chunk +
						sizeof(struct xdr_write_list) + 
						sizeof(struct xdr_write_chunk) * elements);
	}
	if (ntohl(reply_chunk->present) != 0) {
		int i = 0, elements= ntohl(reply_chunk->elements);
		cbc->replylist = (struct xdr_rdma_segment *)malloc(sizeof(struct xdr_rdma_segment) * elements);
		cbc->replylist_cnt = elements;
		for(; i < elements; i++) {
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
		//__warnx(TIRPC_DEBUG_FLAG_ERROR,
				"NFS/FABRIC %s: reply_chunk present:%d, elements:%d, entry[%d]:{ handle:0x%x,length:%d,offset:0x%x }", __func__,
				ntohl(reply_chunk->present),
				ntohl(reply_chunk->elements),
				i,
				ntohl(reply_chunk->entry[i].target.handle),
				ntohl(reply_chunk->entry[i].target.length),
				ntohl64(reply_chunk->entry[i].target.offset));
			cbc->replylist[i].handle = ntohl(reply_chunk->entry[i].target.handle);
			cbc->replylist[i].length = ntohl(reply_chunk->entry[i].target.length);
			cbc->replylist[i].offset = ntohl64(reply_chunk->entry[i].target.offset);
			if (cbc->invalidate_rkey == 0)
				cbc->invalidate_rkey = ntohl(reply_chunk->entry[i].target.handle);
		}
		//reply_chunk = (struct xdr_write_list *)((char *)reply_chunk + sizeof(struct xdr_write_list));
	}
#endif
	/* alloc reply memory*/
	int status = 0;
	status = rpc_fabric_get_reply_memory(cbc);
	probe_time_end(xd, cbc, RPC_fabric_decode); /* rpc_fabric_decode 3*/ 
	if (status != 0)
		return status;
	return 0;
}

static int
get_rdma_credit(int work_thread_num, int queues_num)
{
	int ret = (work_thread_num * queues_num) - FABRIC_IN_PROCESS_COUNT;
	if (ret < 0){
		return 0;
	}else{
		return ret;
	}
}

extern struct rdma_parameter_t rdma_param;

static 
bool
rpc_fabric_rpcheader_encode(struct rpc_rdma_cbc *cbc)
{
	RDMAXPRT *xprt;
	//struct rpc_msg *msg;
	struct rdma_msg *cmsg;
	struct rdma_msg *rmsg;
	struct xdr_write_list *w_array;
	struct xdr_write_list *re_array;

	struct xdr_write_list *reply_array;
	struct xdr_write_list *write_array;
	struct xdr_ioq_uv *head_uv = NULL;
	int rdma_credit = 0;
	//struct xdr_ioq_uv *work_uv = NULL;

	if (!cbc) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s() NFS/FABRIC no context?",
			__func__);
		return (false);
	}
	xprt = x_xprt(cbc->recvbufq.xdrs);
	if (cbc->reply_nfs_head != NULL) {
		/*nfs head add sendbufq*/
		TAILQ_INSERT_TAIL(&cbc->sendbufq.ioq_uv.uvqh.qh, cbc->reply_nfs_head, q);
		(cbc->sendbufq.ioq_uv.uvqh.qcount)++;
		cbc->reply_nfs_head = NULL;
	}

	(void)rpc_addrlist_hton(cbc);

	int valid_cnt = 0;
	{
		struct poolq_entry *have = TAILQ_FIRST(&cbc->sendbufq.ioq_uv.uvqh.qh);
		//work_uv = IOQ_(have);
		uint32_t totalsize = 0;
		while (have) {
			uint32_t length = ioquv_length(IOQ_(have));
			void *addr = (void *)(IOQ_(have)->v.vio_head);
			totalsize += length;
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
				"%s NFS/FABRIC [IOV]  cbc->workq iov: addr %p, len %d.", __func__, addr, length);
			if (length > 0)	
				valid_cnt ++;
			assert(length <= xprt->sm_dr.sendsz);
			have = TAILQ_NEXT(have, q);
			if(!have)
				__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
					"%s NFS/FABRIC [IOV] cbc->workq iov: totalsize %d. qcount %d. valid_cnt %d.", __func__, totalsize, cbc->sendbufq.ioq_uv.uvqh.qcount, valid_cnt);
		}	
	}

	cmsg = m_(cbc->call_head);

	head_uv = IOQ_(cbc->reply_rpc_head);

	TAILQ_INSERT_HEAD(&cbc->sendbufq.ioq_uv.uvqh.qh, cbc->reply_rpc_head, q);
	(cbc->sendbufq.ioq_uv.uvqh.qcount)++;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, " %s NFS/FABRIC, cbc %p, sendbufq qcount %d. \n", __func__, cbc, cbc->sendbufq.ioq_uv.uvqh.qcount);
	cbc->reply_rpc_head = NULL;

	/* entry was already added directly to the queue */
	head_uv->v.vio_head = head_uv->v.vio_base;
	/* tail adjusted below */
	head_uv->v.vio_wrap = (char *)head_uv->v.vio_base + xprt->sm_dr.sendsz;

	/* build the header that goes with the data */
	rmsg = m_(head_uv->v.vio_head);
	//memset(rmsg, 0 , xprt->sm_dr.sendsz);
	//memset(rmsg, 0 , 1024);
	rmsg->rdma_xid = cmsg->rdma_xid;
	rmsg->rdma_vers = cmsg->rdma_vers;
	if(xprt->xa->enable_credicts_countable){
		rdma_credit = get_rdma_credit(xprt->xa->thread_work_num, xprt->xa->cred_queues_num);
		rmsg->rdma_credit = htonl(rdma_credit);
	}else{
		rmsg->rdma_credit = htonl(xprt->xa->credits);
	}
	__warnx_1hz(TIRPC_DEBUG_FLAG_XDR, " %s NFS/FABRIC, rmsg->rdma_credit:%lu, xprt->xa->credits:%lu, enable_credicts_countable:%d, rdma_credit:%ld, thread_work_num:%d, cred_queues_num:%ld, FABRIC_IN_PROCESS_COUNT:%lu\n",
		__func__, rmsg->rdma_credit, xprt->xa->credits, xprt->xa->enable_credicts_countable, rdma_credit, xprt->xa->thread_work_num, xprt->xa->cred_queues_num, FABRIC_IN_PROCESS_COUNT);

	/* no read, write chunks. */
	rmsg->rdma_body.rdma_msg.rdma_reads = htonl(0); /* htonl(0); */
	rmsg->rdma_body.rdma_msg.rdma_writes =  htonl(0); /* htonl(0); */
	rmsg->rdma_type = htonl(RDMA_MSG);
	write_array = (struct xdr_write_list *)cbc->write_chunk;
	reply_array = (struct xdr_write_list *)cbc->reply_chunk;


	if ((write_array != NULL) && (write_array->present != 0)) {
		uint32_t i = 0;
		uint32_t n = ntohl(write_array->elements);
		
		rmsg->rdma_type = htonl(RDMA_MSG);
		w_array = (struct xdr_write_list *)&rmsg->rdma_body.rdma_msg.rdma_writes;
		while (i < n) {
			struct xdr_rdma_segment *w_seg =
				&w_array->entry[i].target;
			struct xdr_rdma_segment *c_seg =
				&write_array->entry[i].target;
			i++;
			*w_seg = *c_seg;
#if 1
		//__warnx(TIRPC_DEBUG_FLAG_ERROR,
		__warnx(TIRPC_DEBUG_FLAG_XDR,
				"NFS/FABRIC %s: write_chunk  entry[%d]:{ handle:0x%x,length:%d,offset:0x%x }", __func__,
				i,
				ntohl(w_seg->handle),
				ntohl(w_seg->length),
				ntohl64(w_seg->offset));
#endif


		}
		w_array->present = htonl(1);
		w_array->elements = htonl(i);
		
		w_array = (struct xdr_write_list *) ((uint8_t *)w_array + (sizeof(struct xdr_write_list) + sizeof(struct xdr_write_chunk) * n ));
		((struct xdr_write_list *)w_array)->present = htonl(0);

	}else {
		w_array = (struct xdr_write_list *)&rmsg->rdma_body.rdma_msg.rdma_writes;
		w_array->present = htonl(0);
	}
	
	if ((reply_array != NULL) && (reply_array->present != 0)) {
			uint32_t i = 0;
			int n = ntohl(reply_array->elements);
			rmsg->rdma_type = htonl(RDMA_NOMSG);
			/* reply chunk */
			re_array = (struct xdr_write_list *)((uint8_t *)w_array + sizeof(uint32_t));

			while (i < n) {
				struct xdr_rdma_segment *w_seg =
				   &re_array->entry[i].target;
				struct xdr_rdma_segment *c_seg =
				   &reply_array->entry[i].target;
				i ++;
				*w_seg = *c_seg;
			}
			re_array->present = htonl(1);
			re_array->elements = htonl(i);
	}else {
		re_array = (struct xdr_write_list *)((uint8_t *)w_array + sizeof(uint32_t));
		re_array->present = htonl(0);
	}
	head_uv->v.vio_tail = head_uv->v.vio_head
					+ xdr_rdma_header_length(rmsg);

	int head_len =  head_uv->v.vio_tail - head_uv->v.vio_head;
	assert(head_len < xprt->sm_dr.sendsz);
#if 0
	rpcrdma_fabric_dump_msg(head_uv, "sreply head", rmsg->rdma_xid);
	if (work_uv != NULL)
		rpcrdma_fabric_dump_msg(work_uv, "sreply body", rmsg->rdma_xid);
#endif
	return (true);
}

int 
rpc_fabric_send(struct rpc_rdma_cbc *cbc) {
	RDMAXPRT *xprt = x_xprt(cbc->recvbufq.xdrs);
	uint32_t totalsize = 0;
	struct poolq_entry *have = NULL;
	int ret = -1;
	//struct iovec send_iov[10] = {0};
	cbc->send_iov = (struct iovec*)malloc(sizeof(struct iovec) * 10);
	int send_iov_cnt = 0;
	probe_time_begin(xprt, cbc, RPC_fabric_send);
	probe_timespec_begin(xprt, &(cbc->start_send_time), RPC_fabric_s_waitcb);
	if (!rpc_fabric_rpcheader_encode(cbc)) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR,
			"%s():  NFS/FABRIC reply encode failed (will set dead)",
			__func__);
		return ret;
	}

	have = TAILQ_FIRST(&cbc->sendbufq.ioq_uv.uvqh.qh);
	if (have) {
		totalsize = 0;
		while (have) {
			struct poolq_entry *next = TAILQ_NEXT(have, q);
			uint32_t length = ioquv_length(IOQ_(have));
			void *addr = (void *)(IOQ_(have)->v.vio_head);
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
				"%s NFS/FABRIC [data] [send-iov] [%d]: addr %p, len %d.", __func__, send_iov_cnt, addr, length);
			cbc->send_iov[send_iov_cnt].iov_base = addr;
			cbc->send_iov[send_iov_cnt].iov_len = length;
			send_iov_cnt ++;
			totalsize += length;
			have = next;
			if(!have)
				__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
					"%s NFS/FABRIC [data] [send-iov] cbc->holdq iov: totalsize %d. write_iov cnt %d.", __func__, totalsize, send_iov_cnt);
		}	
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
				"%s NFS/FABRIC [data] [send-iov] send cnt %d, totalsize %d.", __func__, send_iov_cnt, totalsize);
		cbc->state = RDMA_WAIT_SENDDOEN;
		if (cbc->send_iov[0].iov_base  == 0)
			assert(0);
		ret = fabric_sendv(cbc->ch, send_iov_cnt, cbc->send_iov, cbc, rpc_rdma_fabric_send_callback);
		probe_time_end(xprt, cbc, RPC_fabric_send);
		if(ret != 0)
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,"%s():  NFS/FABRIC assert(ret == 0) ret %d",__func__, ret);

	}
	else {
		assert(0);	
	}

	return ret;
}

static int 
rpc_fabric_done(struct rpc_rdma_cbc *cbc) {
	int ret = 0;
	RDMAXPRT *xprt = x_xprt(cbc->recvbufq.xdrs);
	struct fabric_endpoint *fe = (struct fabric_endpoint* )cbc->ch;
	//probe_end(xprt, 2);  /* rpc_fabric_reply 2*/ 
	struct xdr_ioq *xioq = &cbc->sendbufq; 
	//__warnx(TIRPC_DEBUG_FLAG_ERROR,
	//	"%s() xd %p  xp_refcnt %" PRId32,
	//	__func__, xprt,  xprt->sm_dr.xprt.xp_refcnt);

	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s NFS/FABRIC recvbufq qcount:%d.", __func__, cbc->recvbufq.ioq_uv.uvqh.qcount);
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s NFS/FABRIC sendbufq qcount:%d.", __func__, cbc->sendbufq.ioq_uv.uvqh.qcount);

	TAILQ_CONCAT(&cbc->sendbufq.ioq_uv.uvqh.qh,
			 &cbc->writebufuvq.uvqh.qh, q);
	cbc->sendbufq.ioq_uv.uvqh.qcount += cbc->writebufuvq.uvqh.qcount;
	cbc->writebufuvq.uvqh.qcount = 0;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s cbc %p NFS/FABRIC sendbufq qcount:%d, recvbufq qcount:%d., cbc->recvbufq.ioq_uv.uvqh:%p, fabric %p, fabric: %p", __func__, 
		cbc, cbc->sendbufq.ioq_uv.uvqh.qcount, cbc->recvbufq.ioq_uv.uvqh.qcount, cbc->recvbufq.ioq_uv.uvqh, fe, cbc->ch);
	if (cbc->reply_rpc_head != NULL ) {
		struct xdr_ioq_uv *head_uv = NULL;
		head_uv = IOQ_(cbc->reply_rpc_head);

		TAILQ_INSERT_HEAD(&cbc->sendbufq.ioq_uv.uvqh.qh, cbc->reply_rpc_head, q);
		(cbc->sendbufq.ioq_uv.uvqh.qcount)++;
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, " %s  NFS/FABRIC, (reply_rpc_head != NULL) cbc %p, sendbufq qcount %d, head_uv %p. \n", __func__, 
				cbc, cbc->sendbufq.ioq_uv.uvqh.qcount, head_uv);
		cbc->reply_rpc_head = NULL;

	}

	rdma_ioq_put_recvbufq(&cbc->recvbufq.ioq_uv.uvqh);
	rdma_ioq_put_sendbufq(&cbc->sendbufq.ioq_uv.uvqh);


	if ((xioq->xdrs[0].reply_start.tv_nsec > 0 || xioq->xdrs[0].reply_start.tv_sec >0)
		&& get_enable_RPCSTATS(xioq->xdrs)) {
		struct timespec reply_end;
		now(&reply_end);
		nsecs_elapsed_t reply_time = timespec_diff(&reply_end, &xioq->xdrs[0].reply_start);
		nsecs_elapsed_t request_time = timespec_diff(&reply_end, &xioq->xdrs[0].recv_start);
		server_stats_rpc_done(reply_time, request_time,xioq->xdrs);
	}

	if (cbc->send_iov != NULL) {
		free(cbc->send_iov);
		cbc->send_iov = NULL;
	}

	if (cbc->write_iov != NULL) {
		free(cbc->write_iov);
		cbc->write_iov = NULL;
	}
	if (cbc->writelist != NULL) {
		free(cbc->writelist);
		cbc->writelist = NULL;
	}
	if (cbc->replylist != NULL) {
		free(cbc->replylist);
		cbc->replylist = NULL;
	}
	cbc->write_iov_cnt = 0;
	cbc->writelist_cnt = 0;
	cbc->replylist_cnt = 0;


	mutex_lock(&xprt->sm_dr.ioq.ioq_uv.uvqh.qmutex);
	TAILQ_REMOVE(&xprt->sm_dr.ioq.ioq_uv.uvqh.qh, &cbc->recvbufq.ioq_s, q);
	(xprt->sm_dr.ioq.ioq_uv.uvqh.qcount)--;
	mutex_unlock(&xprt->sm_dr.ioq.ioq_uv.uvqh.qmutex);
	rdma_ioq_put_cbc(&cbc->recvbufq, sizeof(*cbc));
	fabric_endpoint *fab_endp = (struct fabric_endpoint *)xprt->fd;
	(void)atomic_dec_uint64_t(&(fab_endp->ref_count));
	fab_endp->in_process_count --;
	//FABRIC_IN_PROCESS_COUNT --;
	(void)atomic_dec_uint64_t(&FABRIC_IN_PROCESS_COUNT);
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s(): NFS/FABRIC  cbc :%p, inbuf qcount %d, outbuf qcount %d. fabric %p, recv ref_count %d.",
		__func__, cbc, xprt->inbufs.uvqh.qcount, xprt->outbufs.uvqh.qcount, fe, fe->ref_count);
	return ret;
}

int 
rpc_fabric_reply(struct rpc_rdma_cbc *cbc) {
	RDMAXPRT *xprt = x_xprt(cbc->recvbufq.xdrs);
	struct poolq_entry *have = NULL;
	int ret = 0;
	struct xdr_write_list *remote_array = NULL;
	rpc_addrlist_ntoh(cbc);

	probe_time_end(xprt, cbc, NFS_process);
	struct fabric_endpoint *fe = (struct fabric_endpoint* )cbc->ch;
        __warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s(): NFS/FABRIC  cbc :%p , inbuf qcount %d, outbuf qcount %d. fabric %p, recv ref_count %d. cbc->recvbufq.ioq_uv.uvqh:%p",
		__func__, cbc, xprt->inbufs.uvqh.qcount, xprt->outbufs.uvqh.qcount, fe, fe->ref_count, cbc->recvbufq.ioq_uv.uvqh);
	if (cbc->replylist_cnt != 0) {
		cbc->write_iov_cnt = cbc->replylist_cnt;		
		cbc->write_iov = (struct iovec *)malloc(sizeof(struct iovec) * cbc->replylist_cnt);
		remote_array = (struct xdr_write_list *)cbc->reply_chunk;
		remote_array->elements = cbc->write_iov_cnt;

		TAILQ_CONCAT(&cbc->writebufuvq.uvqh.qh,
				 &cbc->sendbufq.ioq_uv.uvqh.qh, q);
		cbc->writebufuvq.uvqh.qcount += cbc->sendbufq.ioq_uv.uvqh.qcount;
		cbc->sendbufq.ioq_uv.uvqh.qcount = 0;
	}
	else if (cbc->writelist_cnt != 0) {
		 cbc->write_iov_cnt = cbc->writelist_cnt;		
		cbc->write_iov = (struct iovec *)malloc(sizeof(struct iovec) * cbc->writelist_cnt);
		remote_array = (struct xdr_write_list *)cbc->write_chunk;
		remote_array->elements = cbc->write_iov_cnt;

		/*write() not include nfs-head*/
		struct poolq_entry *hold_have = TAILQ_FIRST(&cbc->sendbufq.ioq_uv.uvqh.qh);
		TAILQ_REMOVE(&cbc->sendbufq.ioq_uv.uvqh.qh, hold_have, q);
		(cbc->sendbufq.ioq_uv.uvqh.qcount)--;
		cbc->reply_nfs_head = (void *)hold_have;

		TAILQ_CONCAT(&cbc->writebufuvq.uvqh.qh,
				 &cbc->sendbufq.ioq_uv.uvqh.qh, q);
		cbc->writebufuvq.uvqh.qcount += cbc->sendbufq.ioq_uv.uvqh.qcount;
		cbc->sendbufq.ioq_uv.uvqh.qcount = 0;
	}
	
	if (cbc->write_iov != 0) {

		have = TAILQ_FIRST(&cbc->writebufuvq.uvqh.qh);
		void *addr = (void *)(IOQ_(have)->v.vio_head);
		//uint32_t length = ioquv_size(IOQ_(have));
		uint32_t length = ioquv_length(IOQ_(have));
		int i = 0;
		
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s NFS/FABRIC fabric  ioquv size %d, replylist cnt %d, writelist cnt %d.", __func__, length, cbc->replylist_cnt, cbc->writelist_cnt);
		for (i=0; i < cbc->write_iov_cnt; i++) {
			cbc->write_iov[i].iov_base = addr;
			cbc->write_iov[i].iov_len = (remote_array->entry[i].target.length < length)? remote_array->entry[i].target.length : length;
			remote_array->entry[i].target.length = cbc->write_iov[i].iov_len;
			addr = (char *)addr + cbc->write_iov[i].iov_len;
			length -= cbc->write_iov[i].iov_len;
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s NFS/FABRIC fabric writev_iov[%d].iov_base %p.", __func__, i, cbc->write_iov[i].iov_base);
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s NFS/FABRIC fabric writev_iov[%d].iov_len %d.", __func__, i, cbc->write_iov[i].iov_len);
		}

		if (cbc->write_iov_cnt > 0) {
			cbc->state = RDMA_WAIT_WRITEV;
			probe_time_begin(xprt, cbc, RPC_fabric_writev);
			probe_timespec_begin(xprt, &(cbc->start_writev_time), RPC_fabric_w_waitcb);
			ret = fabric_writev(cbc->ch, remote_array, cbc->write_iov_cnt,  cbc->write_iov, cbc, rpc_rdma_fabric_write_callback, 1);
			probe_time_end(xprt, cbc, RPC_fabric_writev);
			if (ret != 0) {
				__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s NFS/FABRIC fabric writev error. ret: %d. cbc %p sendbufq qcount %d, recvbufq qcount %d, recvbufq uvqh:%p, fe %p, cbc->ch:%p.",
					__func__, ret, cbc, cbc->sendbufq.ioq_uv.uvqh.qcount, cbc->recvbufq.ioq_uv.uvqh.qcount, cbc->recvbufq.ioq_uv.uvqh, fe, cbc->ch);
				//struct fabric_endpoint *fab_endp = (struct fabric_endpoint*)cbc->ch;
				if((ret == -1) && (fe->state != EP_STATE_CONNECTED)){
					ret = rpc_fabric_send(cbc);
				}

			}
		}else {
			((struct xdr_write_list *)cbc->write_chunk)->present = 0;
			((struct xdr_write_list *)cbc->reply_chunk)->present = 0;
			ret = rpc_fabric_send(cbc);
		}
	} else {
		((struct xdr_write_list *)cbc->write_chunk)->present = 0;
		((struct xdr_write_list *)cbc->reply_chunk)->present = 0;
		ret = rpc_fabric_send(cbc);
	}

	return ret;
}
static int 
rpc_fabric_readv(struct rpc_rdma_cbc *cbc) {
	int ret = 0;
	RDMAXPRT *xd = x_xprt(cbc->recvbufq.xdrs);

	cbc->state = RDMA_WAIT_READV;
	rpc_addrlist_readlist_ntoh(cbc);
	probe_time_begin(xd, cbc, RPC_fabric_readv); 
	probe_timespec_begin(xd, &(cbc->start_readv_time), RPC_fabric_r_waitcb);
	probe_timespec_begin(xd, &(cbc->start_readv_cq_time), RPC_fabric_r_cq);
	ret = fabric_readv(cbc->ch, cbc->read_chunk, cbc, rpc_rdma_fabric_read_callback);
	probe_time_end(xd, cbc, RPC_fabric_readv); 
	return ret;
}
static void 
async_fabric_readv(void *cbc) {
	(void)rpc_fabric_readv(cbc);
	return;
}
static int 
rpc_fabric_decode_readchunk(struct rpc_rdma_cbc *cbc, struct iovec *iov, int count) {
	int ret = 0;
	RDMAXPRT *xprt = x_xprt(cbc->recvbufq.xdrs);
	
	if (count != 0) {
		uint32_t totalsize = 0;
		int iov_cnt  = 0;
		int align_len  = 0;
		struct xdr_ioq_uv *head_uv = NULL;
		for (; iov_cnt < count; iov_cnt ++ ){
			struct poolq_entry *have = NULL;
			void * iov_base = iov[iov_cnt].iov_base;
			size_t iov_len = iov[iov_cnt].iov_len;
			/* usurp the holdq for the head, move to workq later */
			have = rdma_ioq_get_recvbufq(&cbc->recvbufq, &rdma_xprt_class.rdmaxprt->inbufs.uvqh,
					                                                        "recv", 1, IOQ_FLAG_NONE);
			if (have == NULL) {
				__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"%s NFS/FABRIC get recv bufq faile, cnt %d, total %d.", __func__, iov_cnt, count);
				return -1;
			}
			head_uv = IOQ_(have);
#if 1	
			head_uv->u.uio_p3 = iov_base;
			head_uv->v.vio_base = ((chunk*)iov_base)->buffer;
#else
			//memcpy(head_uv->v.vio_base, iov_base, iov_len);
#endif
			/* entry was already added directly to the queue */
			head_uv->v.vio_head = head_uv->v.vio_base;
			/* tail adjusted below */
			head_uv->v.vio_wrap = (char *)head_uv->v.vio_base + xprt->sm_dr.sendsz;

			assert(iov_len < xprt->sm_dr.sendsz );
			head_uv->v.vio_tail = head_uv->v.vio_base + iov_len;
			totalsize += iov_len;
			//__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
			//	"%s NFS/FABRIC [IOV] iovcnt %d, addr %p, len %ld.", __func__, iov_cnt, iov_base, iov_len);
		}
		if(totalsize % BYTES_PER_XDR_UNIT != 0){
			align_len = BYTES_PER_XDR_UNIT - (totalsize % BYTES_PER_XDR_UNIT);
			head_uv->v.vio_tail = head_uv->v.vio_tail + align_len;
		}
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
			"%s NFS/FABRIC [IOV] count %d, total_size %d, align_len %d., yushu:%d, head_uv:%p ", __func__, iov_cnt, totalsize, align_len, (totalsize % BYTES_PER_XDR_UNIT), head_uv);
	}

	return ret;
}

void printf_callback_error(fabric_endpoint *fab_endp, int reslut) {
	char str_error[256] = {0};
	switch(reslut) {
	case CALLBACK_SUCEESS:
		break;
	case CALLBACK_FAILED:
		sprintf(str_error, "%s", "failed");
		break;
	case CALLBACK_ERROR_WAITDESTORY_LIMIT:
		sprintf(str_error, "%s", "session num limit");
		break;
	case CALLBACK_ERROR_NOCONNECTED:
		sprintf(str_error, "%s", "ep no connected");
		break;
	case CALLBACK_ERROR_FI_SENDV_FAILED:
		sprintf(str_error, "%s", "fi sendv failed");
		break;
	case CALLBACK_ERROR_FI_READV_NOMEM:
		sprintf(str_error, "%s", "fi readv no memery");
		break;
	case CALLBACK_ERROR_FI_READV_FAILED:
		sprintf(str_error, "%s", "fi readv failed");
		break;
	case CALLBACK_ERROR_FI_READ_FAILED:
		sprintf(str_error, "%s", "fi read failed");
		break;
	case CALLBACK_ERROR_FI_WRITE_FAILED:
		sprintf(str_error, "%s", "fi write failed");
		break;
	case CALLBACK_ERROR_FI_WRITEV_FAILED:
		sprintf(str_error, "%s", "fi writev failed");
		break;
	default:break;
	}
	if (reslut != CALLBACK_FAILED) {
		__warnx(TIRPC_DEBUG_FLAG_ERROR, "func %s() fabric_endpoint:%p, ref:%ld, [%s].\n", __func__, fab_endp, (fab_endp != NULL) ? fab_endp->ref_count : -1, str_error);
	}else {
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "func %s() fabric_endpoint:%p, ref:%ld, [%s].\n", __func__, fab_endp, (fab_endp != NULL) ? fab_endp->ref_count : -1, str_error);
	}
	return;
}
/*
* arg
* void *rdma_xprt:            链接句柄
* void *callback_arg： 回调参数，会话句柄
* result success:0  error:-1
*/
int
rpc_rdma_process(void *ch, int state, void *callback_arg, struct iovec *iov, int cnt, int reslut)
{
	int ret = -1;
	struct fabric_endpoint *fe = (struct fabric_endpoint* )ch;
	struct rpc_rdma_cbc *cbc  = NULL;
	RDMAXPRT *xd = fe->xd;
	if (state != RDMA_START)
		cbc  = (struct rpc_rdma_cbc *)callback_arg;

//	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA,
//		"func %s,() state %s, reslut %d.\n", __func__, fabric_stat_str[state], reslut);

	if (reslut != 0) {
		printf_callback_error(fe, reslut);
	}
	if (reslut != 0) {
		if (cbc != NULL)
			ret = rpc_fabric_done(cbc);
		return ret;
	}
	if ((state != RDMA_START) && (fe->state != EP_STATE_CONNECTED)) {
		if (cbc != NULL)
			ret = rpc_fabric_done(cbc);
		return ret;
	}
	switch(state) {
		case RDMA_START:
			assert(cbc == NULL);
			ret = rpc_fabric_start(xd, callback_arg, &cbc);
			if (ret != 0) {
				__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"%s() NFS/FABRIC  xprt:%p fabric start error ret %d.", __func__, xd, ret);
				return ret;
			}
			ret = rpc_fabric_decode(xd, cbc);
			if (ret == 0){
				cbc->ch = ch;
				if (xrl(cbc->read_chunk)->present != 0) {
					ret = fabric_cqpool_submit(rdma_xprt_class.fab_clas, async_fabric_readv, cbc, fe->thrid);
					if (ret != 0) {
						__warnx(TIRPC_DEBUG_FLAG_ERROR,
							"%s() NFS/FABRIC  xprt:%p rpc_fabric_readv  ret %d.", __func__, xd, ret);
					}
				}
				else {
					ret = rpc_worksubmit(cbc);
					if (ret != 0) {
						__warnx(TIRPC_DEBUG_FLAG_ERROR,
							"%s() NFS/FABRIC  xprt:%p fabric read done worksubmit error ret %d.", __func__, xd, ret);
					}
				}
			}
			else {
				__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"%s() NFS/FABRIC  xprt:%p fabric decode error ret %d.", __func__, xd, ret);
				if (cbc != NULL) {
					ret = rpc_fabric_done(cbc);
				}
			}
			break;
		case RDMA_WAIT_READV:
			ret = rpc_worksubmit(cbc);
			if (ret != 0) {
				__warnx(TIRPC_DEBUG_FLAG_ERROR,
					"%s() NFS/FABRIC  xprt:%p fabric readv done worksubmit error ret %d.", __func__, xd, ret);
			}
			break;
		case RDMA_WAIT_WRITEV: {
			//probe_timespec_end(xd, &(cbc->start_writev_time), RPC_fabric_w_waitcb);
			//ret = rpc_fabric_send(cbc);
			break;
		}
		case RDMA_WAIT_SENDDOEN:
			probe_timespec_end(xd, &(cbc->start_send_time), RPC_fabric_s_waitcb);
			ret = rpc_fabric_done(cbc);
			break;
		default: break;
	}
	return ret;
}

static void
rpc_fabric_process_callback(void *arg) {
	struct _rpc_rdma_callback_args *callback_args  = (struct _rpc_rdma_callback_args *)arg;

	rpc_rdma_process(callback_args->ch, 
			callback_args->state,
			callback_args->callback_arg,
			callback_args->iov,
			callback_args->cnt, 
			callback_args->reslut);
	free(callback_args);
}

int
rpc_rdma_fabric_recv_callback(void *ch, void *callback_arg, struct iovec *iov, int cnt, int reslut){
	int ret = 0;
	//struct rpc_rdma_cbc *cbc  = (struct rpc_rdma_cbc *)callback_arg;
	struct fabric_endpoint *fe = (struct fabric_endpoint* )ch;
	RDMAXPRT *xd = fe->xd;
	assert(callback_arg == NULL);

	//if (xd == NULL) {
	//	ret = -1;
	//}
	if (fe->active == false) {
		ret = -1;
	}
	if ((reslut == 0) && (ret == 0)){
		struct poolq_head *ioqh = &rdma_xprt_class.rdmaxprt->inbufs.uvqh;
#if __LOCK_IN_MEM
		pthread_mutex_lock(&ioqh->qmutex);
#endif
		if (likely(0 < ioqh->qcount)) {
			struct poolq_entry *have;
			char *buf = iov[0].iov_base;
			ioqh->qcount --;
			/* positive for buffer(s) */
			have = TAILQ_FIRST(&ioqh->qh);
			TAILQ_REMOVE(&ioqh->qh, have, q);
#if __LOCK_IN_MEM
			pthread_mutex_unlock(&ioqh->qmutex);
#endif
			IOQ_(have)->u.uio_p3 = buf;
			IOQ_(have)->v.vio_base = ((chunk*)buf)->buffer;
			IOQ_(have)->v.vio_head = IOQ_(have)->v.vio_base;
			IOQ_(have)->v.vio_tail = IOQ_(have)->v.vio_wrap;
			IOQ_(have)->v.vio_wrap = (char *)IOQ_(have)->v.vio_base + xd->sm_dr.recvsz;
			IOQ_(have)->v.vio_tail = IOQ_(have)->v.vio_base + iov[0].iov_len;
			callback_arg = have;
			probe_count_inc(PROBE_COUNT_RDMA_MEM_IN_USED);
		} else {
#if __LOCK_IN_MEM
			pthread_mutex_unlock(&ioqh->qmutex);
#endif
			ret = -1;
			__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC  xprt:%p, get inbufs faild, qcount %d, reslut:%d, ret %d.", __func__, xd, ioqh->qcount ,reslut, ret);
		}
	}
	if ((reslut != 0) || (ret != 0)){
		if (iov != NULL) {
			free_chunk(fe->mpool, iov->iov_base);
			(void)atomic_dec_uint64_t(&fe->ref_count);
			__warnx_rdma(TIRPC_DEBUG_FLAG_XDR_RDMA, "func %s,() fabric %p, recv ref_count %d reslut:%d, ret %d..\n", __func__, fe, fe->ref_count, reslut, ret);
		}
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRIC  xprt:%p , reslut:%d, ret %d.", __func__, xd, reslut, ret);
		/*memery alloc error  dont need return*/
		return -1;
	}

	struct _rpc_rdma_callback_args *callback_args = malloc(sizeof(struct _rpc_rdma_callback_args) + sizeof(struct iovec)*cnt);
	callback_args->ch = ch;
	callback_args->callback_arg = callback_arg;
	callback_args->cnt = cnt;
	callback_args->state = RDMA_START;
	callback_args->reslut = reslut;	
	memcpy(callback_args->iov, iov, sizeof(struct iovec)*cnt);
	//FABRIC_IN_PROCESS_COUNT ++;
	(void)atomic_inc_uint64_t(&FABRIC_IN_PROCESS_COUNT);
	//probe_time_begin(xd, cbc, RPC_fabric_worksubmit_wait);
	if (xd->xa->thr_nolock ==  true) {
		if (xd->xa->enable_rpc_thread == true) {
			ret = fabric_rpcpool_submit(rdma_xprt_class.fab_clas, rpc_fabric_process_callback, callback_args);
		}
		else {
			rpc_fabric_process_callback(callback_args);
			ret = 0;
		}
	} else { 
		assert(0);
	}
	return ret;
}
int
rpc_rdma_fabric_read_callback(void *ch, void *callback_arg, struct iovec *iov, int cnt, int reslut){
	int ret = -1;
	//struct rpc_rdma_cbc *cbc  = (struct rpc_rdma_cbc *)callback_arg;
	struct fabric_endpoint *fe = (struct fabric_endpoint* )ch;
	RDMAXPRT *xd = fe->xd;
	assert(callback_arg != NULL);
	struct rpc_rdma_cbc *cbc = (struct rpc_rdma_cbc *)callback_arg;

	probe_timespec_end(xd, &(cbc->start_readv_time), RPC_fabric_r_waitcb);
	if (reslut == 0) {
		probe_time_begin(xd, cbc, RPC_fabric_decode_readchunk);
		ret = rpc_fabric_decode_readchunk(cbc,  iov, cnt);
		probe_time_end(xd, cbc, RPC_fabric_decode_readchunk);
		if ( ret != 0) {
			__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s cbc %p NFS/FABRIC sendbufq qcount:%d, recvbufq qcount:%d., cbc->recvbufq.ioq_uv.uvqh:%p", __func__,
                			cbc, cbc->sendbufq.ioq_uv.uvqh.qcount, cbc->recvbufq.ioq_uv.uvqh.qcount, cbc->recvbufq.ioq_uv.uvqh);

			rdma_ioq_put_recvbufq(&cbc->recvbufq.ioq_uv.uvqh);
		//	(void)atomic_dec_uint64_t(&fe->ref_count);
		}
	} 
	if ((reslut != 0) || (ret != 0)){
		if (iov != NULL) {
			int iov_cnt = 0; 
			for (iov_cnt = 0; iov_cnt < cnt; iov_cnt++) {
				free_chunk(fe->mpool, iov[iov_cnt].iov_base);
			}
		}
		__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s() NFS/FABRIC  xprt:%p, call back reslut:%d, reslut:%d, iov=%p, cnt=%d.", __func__, xd, reslut, ret, iov, cnt);
		/* do not return, should call fabric_done *///return -1; 
	}
 	//__warnx(TIRPC_DEBUG_FLAG_ERROR, "%s() NFS/FABRIC  xprt:%p, cbc %p, recv count %d.", __func__, xd, cbc, cbc->recvbufq.ioq_uv.uvqh.qcount);	

	struct _rpc_rdma_callback_args *callback_args = malloc(sizeof(struct _rpc_rdma_callback_args) + sizeof(struct iovec)*cnt);
	callback_args->ch = ch;
	callback_args->callback_arg = callback_arg;
	callback_args->cnt = cnt;
	callback_args->state = RDMA_WAIT_READV;
	callback_args->reslut = reslut;	
	memcpy(callback_args->iov, iov, sizeof(struct iovec)*cnt);

	//probe_time_begin(xd, cbc, RPC_fabric_worksubmit_wait);
	if (xd->xa->thr_nolock ==  true) {
		if (xd->xa->enable_rpc_thread == true) {
			ret = fabric_rpcpool_submit(rdma_xprt_class.fab_clas, rpc_fabric_process_callback, callback_args);
		}
		else {
			rpc_fabric_process_callback(callback_args);
			ret = 0;
		}
	} else { 
		assert(0);
	}
	return ret;
}
int
rpc_rdma_fabric_send_callback(void *ch, void *callback_arg, struct iovec *iov, int cnt, int reslut){
	int ret = -1;
	//struct rpc_rdma_cbc *cbc  = (struct rpc_rdma_cbc *)callback_arg;
	struct fabric_endpoint *fe = (struct fabric_endpoint* )ch;
	RDMAXPRT *xd = fe->xd;

	assert(callback_arg != NULL);
	struct rpc_rdma_cbc *cbc = (struct rpc_rdma_cbc *)callback_arg;
	__warnx(TIRPC_DEBUG_FLAG_XDR_RDMA, "%s cbc %p NFS/FABRIC sendbufq qcount:%d, recvbufq qcount:%d, cbc->recvbufq.ioq_uv.uvqh:%p.", __func__,
		cbc, cbc->sendbufq.ioq_uv.uvqh.qcount, cbc->recvbufq.ioq_uv.uvqh.qcount, cbc->recvbufq.ioq_uv.uvqh);

	rdma_ioq_put_recvbufq(&cbc->recvbufq.ioq_uv.uvqh);

	struct _rpc_rdma_callback_args *callback_args = malloc(sizeof(struct _rpc_rdma_callback_args) + sizeof(struct iovec)*cnt);
	callback_args->ch = ch;
	callback_args->callback_arg = callback_arg;
	callback_args->cnt = cnt;
	callback_args->state = RDMA_WAIT_SENDDOEN;
	callback_args->reslut = reslut;	
	memcpy(callback_args->iov, iov, sizeof(struct iovec)*cnt);

	//probe_time_begin(xd, cbc, RPC_fabric_worksubmit_wait);
	if (xd->xa->thr_nolock ==  true) {
		if (xd->xa->enable_rpc_thread == true) {
			ret = fabric_rpcpool_submit(rdma_xprt_class.fab_clas, rpc_fabric_process_callback, callback_args);
		}
		else {
			rpc_fabric_process_callback(callback_args);
			ret = 0;
		}
	} else { 
		assert(0);
	}
	return ret;
}

int
rpc_rdma_fabric_write_callback(void *ch, void *callback_arg, struct iovec *iov, int cnt, int reslut){
	int ret = 0;
	struct fabric_endpoint *fe = (struct fabric_endpoint* )ch;
	RDMAXPRT *xd = fe->xd;
	struct rpc_rdma_cbc *cbc = (struct rpc_rdma_cbc *)callback_arg;
	probe_timespec_end(xd, &(cbc->start_writev_time), RPC_fabric_w_waitcb);
	/*do nothing*/
	return ret;
}
