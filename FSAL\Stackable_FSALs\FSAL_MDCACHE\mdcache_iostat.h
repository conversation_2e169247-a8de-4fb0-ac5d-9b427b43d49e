/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright (C) 2012, The Linux Box Corporation
 * Contributor: <PERSON> <<EMAIL>>
 *
 * Some portions Copyright CEA/DAM/DIF  (2008)
 * contributeur: <PERSON>NIEL   <EMAIL>
 *               <PERSON> LEIBOVICI  thomas.leib<PERSON><EMAIL>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

/**
 * @addtogroup FSAL_MDCACHE
 * @{
 */

#ifndef MDCACHE_IOSTAT_H
#define MDCACHE_IOSTAT_H

#include "config.h"
#include "log.h"
#include "mdcache_int.h"

#define IO_BLOCK_SIZE_4K  1024*4
#define IO_BLOCK_SIZE_8K  1024*8
#define IO_BLOCK_SIZE_16K  1024*16
#define IO_BLOCK_SIZE_32K 1024*32
#define IO_BLOCK_SIZE_64K 1024*64
#define IO_BLOCK_SIZE_128K  1024*128

#define STAT_SIZE_1K      1024*1
#define STAT_SIZE_32K     1024*32
#define STAT_SIZE_64K     1024*64
#define STAT_SIZE_1M      1024*1024
#define STAT_SIZE_50M     1024*1024*50
#define STAT_SIZE_100M    1024*1024*100
#define STAT_SIZE_200M    1024*1024*200
#define STAT_SIZE_500M    1024*1024*500
#define STAT_SIZE_1G      1024*1024*1024*1

typedef enum {
	IO_STATA_RANGE_0_4K,
	IO_STATA_RANGE_4K_8K,
	IO_STATA_RANGE_8K_16K,
	IO_STATA_RANGE_16K_32K,
	IO_STATA_RANGE_32K_64K,
	IO_STATA_RANGE_64K_128K,
	IO_STATA_RANGE_128K_MAX,
	IO_STATA_RANGE_MAX,
}io_stat_range;

typedef struct {
	uint64_t io[IO_STATA_RANGE_MAX];
	uint64_t io_total;
}io_stat;

typedef enum {
	FILE_SIZE_RANGE_0_1K,
	FILE_SIZE_RANGE_1K,
	FILE_SIZE_RANGE_1K_32K,
	FILE_SIZE_RANGE_32K,
	FILE_SIZE_RANGE_32K_64K,
	FILE_SIZE_RANGE_64K,
	FILE_SIZE_RANGE_64K_1M,
	FILE_SIZE_RANGE_1M,
	FILE_SIZE_RANGE_1M_50M,
	FILE_SIZE_RANGE_50M,
	FILE_SIZE_RANGE_50M_100M,
	FILE_SIZE_RANGE_100M,
	FILE_SIZE_RANGE_100M_200M,
	FILE_SIZE_RANGE_200M,
	FILE_SIZE_RANGE_200M_500M,
	FILE_SIZE_RANGE_500M,
	FILE_SIZE_RANGE_500M_1G,
	FILE_SIZE_RANGE_1G,
	FILE_SIZE_RANGE_1G_MAX,
	FILE_SIZE_RANGE_MAX,
}file_size_range;

typedef struct {
	uint64_t io_count;
	uint64_t file_count;
}files_count;

typedef struct {
	files_count file_range_counts[FILE_SIZE_RANGE_MAX];
	uint64_t file_total;
}files_stat;

typedef struct {

	files_stat write_files_stat;
	io_stat  write_io_stat;
	files_stat read_files_stat;
	io_stat  read_io_stat;
	struct timespec first_record;
	pthread_spinlock_t spin;
}file_io_stat;

//file_io_stat g_fileio_stat;


#endif				/* MDCACHE_LRU_H */
/** @} */
