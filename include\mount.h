/*
 * Please do not edit this file.
 * It was generated using rpcgen.
 */

#ifndef _MOUNT_H_RPCGEN
#define	_MOUNT_H_RPCGEN

#include "gsh_refstr.h"

#define	MNTPATHLEN 1024
#define	MNTNAMLEN 255

#define NB_AUTH_FLAVOR 10

/* RPCSEC_GSS flavor, for MOUNT */
#define MNT_RPC_GSS_NONE      390003
#define MNT_RPC_GSS_INTEGRITY 390004
#define MNT_RPC_GSS_PRIVACY   390005

enum mountstat3 {
	MNT3_OK = 0,
	MNT3ERR_PERM = 1,
	MNT3ERR_NOENT = 2,
	MNT3ERR_IO = 5,
	MNT3ERR_ACCES = 13,
	MNT3ERR_NOTDIR = 20,
	MNT3ERR_INVAL = 22,
	MNT3ERR_NAMETOOLONG = 63,
	MNT3ERR_NOTSUPP = 10004,
	MNT3ERR_SERVERFAULT = 10006
};
typedef enum mountstat3 mountstat3;

/**
 * @todo
 * Danger Will Robinson!!
 * this struct is overlayed with nfs_fh3 in nfs23.h!!
 * This needs to be fixed
 */
typedef struct {
	u_int fhandle3_len;
	char *fhandle3_val;
} fhandle3;

typedef char *mnt3_dirpath;

typedef char *mnt3_name;

typedef struct groupnode *mnt3_groups;

struct groupnode {
	mnt3_name gr_name;
	mnt3_groups gr_next;
};
typedef struct groupnode groupnode;

typedef struct exportnode *mnt3_exports;

struct exportnode {
	struct gsh_refstr *ex_refdir;
	mnt3_dirpath ex_dir;
	mnt3_groups ex_groups;
	mnt3_exports ex_next;
};
typedef struct exportnode exportnode;

typedef struct mountbody *mountlist;

struct mountbody {
	mnt3_name ml_hostname;
	mnt3_dirpath ml_directory;
	mountlist ml_next;
};
typedef struct mountbody mountbody;

struct mountres3_ok {
	fhandle3 fhandle;
	struct {
		u_int auth_flavors_len;
		int *auth_flavors_val;
	} auth_flavors;
};
typedef struct mountres3_ok mountres3_ok;

struct mountres3 {
	mountstat3 fhs_status;
	union {
		mountres3_ok mountinfo;
	} mountres3_u;
};
typedef struct mountres3 mountres3;

#define	MOUNTPROG	100005
#define	MOUNT_V1	1
#define	MOUNTPROC2_NULL	0
#define	MOUNTPROC2_MNT	1
#define	MOUNTPROC2_DUMP	2
#define	MOUNTPROC2_UMNT	3
#define	MOUNTPROC2_UMNTALL	4
#define	MOUNTPROC2_EXPORT	5
#define	MOUNT_V3	3
#define	MOUNTPROC3_NULL	0
#define	MOUNTPROC3_MNT	1
#define	MOUNTPROC3_DUMP	2
#define	MOUNTPROC3_UMNT	3
#define	MOUNTPROC3_UMNTALL	4
#define	MOUNTPROC3_EXPORT	5

/* the xdr functions */
extern bool xdr_mountstat3(XDR *, mountstat3 *);
extern bool xdr_fhandle3(XDR *, fhandle3 *);
extern bool xdr_dirpath(XDR *, mnt3_dirpath *);
extern bool xdr_name(XDR *, mnt3_name *);
extern bool xdr_groups(XDR *, mnt3_groups *);
extern bool xdr_exports(XDR *, mnt3_exports *);
extern bool xdr_mountlist(XDR *, mountlist *);
extern bool xdr_mountres3_ok(XDR *, mountres3_ok *);
extern bool xdr_mountres3(XDR *, mountres3 *);

#endif				/* !_MOUNT_H_RPCGEN */
