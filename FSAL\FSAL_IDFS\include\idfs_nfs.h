/*                                                                              */
/* Copyright (C) 2001 International Business Machines                           */
/* All rights reserved.                                                         */
/*                                                                              */
/* This file is part of the GPFS user library.                                  */
/*                                                                              */
/* Redistribution and use in source and binary forms, with or without           */
/* modification, are permitted provided that the following conditions           */
/* are met:                                                                     */
/*                                                                              */
/*  1. Redistributions of source code must retain the above copyright notice,   */
/*     this list of conditions and the following disclaimer.                    */
/*  2. Redistributions in binary form must reproduce the above copyright        */
/*     notice, this list of conditions and the following disclaimer in the      */
/*     documentation and/or other materials provided with the distribution.     */
/*  3. The name of the author may not be used to endorse or promote products    */
/*     derived from this software without specific prior written                */
/*     permission.                                                              */
/*                                                                              */
/* THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR         */
/* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES    */
/* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.      */
/* IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, */
/* SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, */
/* PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;  */
/* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,     */
/* WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR      */
/* OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF       */
/* ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.                                   */
/*                                                                              */
/* %Z%%M%       %I%  %W% %G% %U% */
/*
 *  Library calls for GPFS interfaces
 */
#ifndef H_GPFS_NFS
#define H_GPFS_NFS

#ifdef __cplusplus
extern "C" {
#endif


	//add by wangyingjie for stat tool
#define IDFS_NULL     0
#define IDFS_LOOKUP   1
#define IDFS_CREATE   2
#define IDFS_MKDIR    3
#define IDFS_PUT      4
#define IDFS_OPENDIR  5
#define IDFS_READDIRPLUS_R 6 //add by zanglinjie
#define IDFS_RELEASEDIR   7
#define IDFS_SYMLINK  8
#define IDFS_READLINK 9
#define IDFS_GETATTR  10
#define IDFS_SETATTR  11
#define IDFS_LINK     12
#define IDFS_RENAME   13
#define IDFS_UNLINK   14
#define IDFS_RMDIR    15
#define IDFS_OPEN     16
#define IDFS_READ     17
#define IDFS_WRITE    18
#define IDFS_CLOSE    19
#define IDFS_GET_INODE 20
#define IDFS_LOOKUP_INODE 21
#define IDFS_STATE_FS 22
#define IDFS_FSYNC    23
#define IDFS_MKNOD    24
#define IDFS_SETLK 25
#define IDFS_GETLK 26
#define IDFS_SETXATTR 27
#define IDFS_GETXATTR 28
#define IDFS_REMOVEXATTR 29
#define IDFS_FSYNC_INODE 30
#define IDFS_DELEGATION 31
#define IDFS_FALLOCATE 32
#define IDFS_READV     33
#define IDFS_WRITEV    34
#define IDFS_TRANS_USERMAP    35
#define IDFS_CHECK_PERMISSIONS 36
#define IDFS_NTFS_ACL_TO_UNIX_ACL 37
#define IDFS_UID_2_GIDS 38
#define IDFS_FREE_GIDS 39
#define IDFS_TRUNCATE 40



#ifdef __cplusplus
}
#endif

extern struct fsal_stats gpfs_stats;

#endif /* H_GPFS_NFS */

