/*
 * Copyright © 2012-2014, CohortFS, LLC.
 * Author: <PERSON> <<EMAIL>>
 *
 * contributeur : <PERSON> <<EMAIL>>
 *		  <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

/**
 * @file   internal.h
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @date Wed Oct 22 13:24:33 2014
 *
 * @brief Internal declarations for the Idfs FSAL
 *
 * This file includes declarations of data types, functions,
 * variables, and constants for the Idfs FSAL.
 */

#ifndef FSAL_IDFS_INTERNAL_INTERNAL__
#define FSAL_IDFS_INTERNAL_INTERNAL__

#include <idfsfs/libidfsfs.h>
#include "fsal.h"
#include "fsal_types.h"
#include "fsal_api.h"
#include "fsal_convert.h"
#include <stdbool.h>
#include <uuid/uuid.h>
#include "statx_compat.h"
#include "FSAL/fsal_commonlib.h"

/* Max length of a user_id string that we pass to idfs_mount */
#define MAXUIDLEN	(64)

/* Max length of a secret key for this user */
#define MAXSECRETLEN	(88)

/**
 * Idfs Main (global) module object
 */

struct idfs_fsal_module {
	struct fsal_module fsal;
	struct fsal_obj_ops handle_ops;
	char *conf_path;
};
extern struct idfs_fsal_module IdfsFSM;

/**
 * Idfs private export object
 */

struct idfs_export {
	struct fsal_export export;	/*< The public export object */
	struct idfs_mount_info *cmount;	/*< The mount object used to
					   access all Idfs methods on
					   this export. */
	struct idfs_handle *root;	/*< The root handle */
	char *user_id;			/* idfsx user_id for this mount */
	char *secret_key;
	char *sec_label_xattr;		/* name of xattr for security label */
	char *fs_name;			/* filesystem name */
	int64_t fscid;			/* Cluster fsid for named fs' */
	bool is_root_export;    /*add by zhangshuai 20210421*/
};

struct idfs_fd {
	/** The open and share mode etc. */
	fsal_openflags_t openflags;
	/* rw lock to protect the file descriptor */
	pthread_rwlock_t fdlock;
	/** The idfsfs file descriptor. */
	Fh *fd;
};

struct idfs_state_fd {
	struct state_t state;
	struct idfs_fd idfs_fd;
};

/**
 * The 'private' Idfs FSAL handle
 */

struct idfs_host_handle {
	uint64_t	chk_ino;
	uint64_t	chk_snap;
	int64_t		chk_fscid;
	char chk_fsid[5];
	int8_t         chk_vision;
} __attribute__ ((__packed__));

struct idfs_handle_key {
	/* NOTE: The idfs_host_handle MUST be first in this structure */
	struct idfs_host_handle hhdl;
	uint16_t export_id;
};

struct idfs_handle {
	struct fsal_obj_handle handle;	/*< The public handle */
	struct idfs_fd fd;
	struct IdfsInode *i;	/*< The Idfs inode */
	const struct fsal_up_vector *up_ops;	/*< Upcall operations */
	struct idfs_handle_key key;	/*< The handle-key that includes the
					    idfs_host_handle. */
	struct fsal_share share;
#ifdef IDFS_PNFS
	uint64_t rd_issued;
	uint64_t rd_serial;
	uint64_t rw_issued;
	uint64_t rw_serial;
	uint64_t rw_max_len;
#endif				/* IDFS_PNFS */
};

#ifdef IDFS_PNFS

/**
 * The wire content of a DS (data server) handle
 */

struct ds_wire {
	struct wire_handle wire; /*< All the information of a regualr handle */
	struct idfs_file_layout layout;	/*< Layout information */
	uint64_t snapseq; /*< And a single entry giving a degernate
			      snaprealm. */
};

/**
 * The full, 'private' DS (data server) handle
 */

struct ds {
	struct fsal_ds_handle ds;	/*< Public DS handle */
	struct ds_wire wire;	/*< Wire data */
	bool connected;		/*< True if the handle has been connected
				   (in Idfs) */
};

#endif				/* IDFS_PNFS */

#ifdef IDFSFS_POSIX_ACL
#  define POSIX_ACL_ATTR	ATTR_ACL
#else /* IDFSFS_POSIX_ACL */
#  define POSIX_ACL_ATTR	0
#endif /* IDFSFS_POSIX_ACL */

#define IDFS_SUPPORTED_ATTRS ((const attrmask_t) (ATTRS_POSIX |		\
						  ATTR4_SEC_LABEL |	\
						  ATTR4_XATTR |		\
						  POSIX_ACL_ATTR	\
						  ))

#define IDFS_SETTABLE_ATTRIBUTES ((const attrmask_t) (          \
	ATTR_MODE  | ATTR_OWNER | ATTR_GROUP | ATTR_ATIME    |  \
	ATTR_CTIME | ATTR_MTIME | ATTR_SIZE  | ATTR_MTIME_SERVER |  \
	ATTR_ATIME_SERVER | ATTR4_SEC_LABEL | POSIX_ACL_ATTR))

/* Prototypes */

void construct_handle(const struct idfs_statx *stx,
					struct IdfsInode *i,
					struct idfs_export *export,
					struct idfs_handle **obj);
void deconstruct_handle(struct idfs_handle *obj);

/**
 * @brief FSAL status from Idfs error
 *
 * This function returns a fsal_status_t with the FSAL error as the
 * major, and the posix error as minor. (Idfs's error codes are just
 * negative signed versions of POSIX error codes.)
 *
 * @param[in] idfs_errorcode Idfs error (negative Posix)
 *
 * @return FSAL status.
 */
static inline fsal_status_t idfs2fsal_error(const int idfs_errorcode)
{
	return fsalstat(posix2fsal_error(-idfs_errorcode), -idfs_errorcode);
}

unsigned int attrmask2idfs_want(attrmask_t mask);
void idfs2fsal_attributes(const struct idfs_statx *stx,
			  struct fsal_attrlist *fsalattr);

void export_ops_init(struct export_ops *ops);
void handle_ops_init(struct fsal_obj_ops *ops);
#ifdef IDFS_PNFS
void pnfs_ds_ops_init(struct fsal_pnfs_ds_ops *ops);
void export_ops_pnfs(struct export_ops *ops);
void handle_ops_pnfs(struct fsal_obj_ops *ops);
#endif				/* IDFS_PNFS */

struct state_t *idfs_alloc_state(struct fsal_export *exp_hdl,
				 enum state_type state_type,
				 struct state_t *related_state);

void idfs_free_state(struct fsal_export *exp_hdl, struct state_t *state);

#ifdef IDFSFS_POSIX_ACL
/*
fsal_status_t idfs_set_acl(struct idfs_export *export,
			   struct idfs_handle *objhandle, bool is_dir,
			   struct fsal_attrlist *attrs);

int idfs_get_acl(struct idfs_export *export, struct idfs_handle *objhandle,
	bool is_dir, struct fsal_attrlist *attrs);
*/
int idfs_get_acl_v3(struct idfs_export *export, struct idfs_handle *objhandle,
	bool is_dir, struct fsal_attrlist *attrs);
fsal_status_t idfs_set_acl_v3(struct idfs_export *export,
	struct idfs_handle *objhandle, bool is_dir, struct fsal_attrlist *attrs, bool is_default);

int idfs_get_acl_v4(struct idfs_export *export, struct idfs_handle *objhandle,
	bool is_dir, struct fsal_attrlist *attrs);
fsal_status_t idfs_set_acl_v4(struct idfs_export *export,
	struct idfs_handle *objhandle, bool is_dir, struct fsal_attrlist *attrs, bool is_default);

int mode_flash_acl(struct idfs_export *export, struct idfs_handle *objhandle,
	uint32_t mode,bool is_dir);

void copy_entry1_2_entry2(idfs_acl_entry_t *entry1, idfs_acl_entry_t *entry2, uint32_t set_perm);
int idfs_acl_printf(idfs_acl_t *idfs_acl);
uint32_t posix_perm_to_idfs_perm( uint16_t posix_perm, bool use_dv, bool directory);
int merge_idfs_acl_V4(idfs_acl_t *idfs_acl, idfs_acl_t **pp_idfs_acl);


#endif				/* IDFSFS_POSIX_ACL */
#ifdef USE_DBUS
void fsal_idfs_extract_stats(struct fsal_module *fsal_hdl, void *iter);
#endif
void prepare_for_stats(struct fsal_module *fsal_hdl);
void fsal_idfs_reset_stats(struct fsal_module *fsal_hdl);
void stat_idfs_begin(uint32_t idfs_ops);
uint64_t  stat_idfs_end(uint32_t idfs_ops);
#endif				/* !FSAL_IDFS_INTERNAL_INTERNAL__ */
