/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright (C) Panasas Inc., 2013
 * Author: <PERSON>@panasas.com
 *
 * contributeur : <PERSON>   <EMAIL>
 *                Thomas LEIBOVICI  <EMAIL>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

#include "config.h"

#include <time.h>
#include <unistd.h>
#include <sys/socket.h>
#ifdef RPC_VSOCK
#include <linux/vm_sockets.h>
#endif /* VSOCK */
#include <sys/types.h>
#include <sys/param.h>
#include <pthread.h>
#include <assert.h>
#include <arpa/inet.h>
#include "gsh_list.h"
#include "fsal.h"
#include "nfs_core.h"
#include "log.h"
#include "avltree.h"
#include "gsh_types.h"
#ifdef USE_DBUS
#include "gsh_dbus.h"
#endif
#include "namespace_mgr.h"
#include "export_mgr.h"
#include "server_stats_private.h"
#include "abstract_atomic.h"
#include "gsh_intrinsic.h"
#include "server_stats.h"
#include "sal_functions.h"
#include "client_mgr.h"

static struct ns_audit_by_tn g_ns_audit_by_tn;

static uint64_t hash_tenant_namespace(const char *tenant, const char *namespace)
{
	uint64_t h = 0;
	const char *p = tenant;
	while (*p) {
		h = (h << 5) - h + (unsigned char)*p++;
	}
	p = namespace;
	while (*p) {
		h = (h << 5) - h + (unsigned char)*p++;
	}
	return h;
}

static inline int ns_cache_offsetof(struct ns_audit_by_tn *ns, uint64_t hashval)
{
	return hashval % ns->cache_sz;
}

static int ns_audit_cmpf(const struct avltree_node *lhs,
			const struct avltree_node *rhs)
{
	struct ns_audit *lk, *rk;

	lk = avltree_container_of(lhs, struct ns_audit, node_k);
	rk = avltree_container_of(rhs, struct ns_audit, node_k);
	
	int ret = strcmp(lk->tenant_name, rk->tenant_name);
	if (ret != 0)
		return ret;
	
	return strcmp(lk->namespace_name, rk->namespace_name);
}

void ns_audit_pkginit(void)
{
	pthread_rwlockattr_t rwlock_attr;
	
	pthread_rwlockattr_init(&rwlock_attr);
#ifdef GLIBC
	pthread_rwlockattr_setkind_np(
		&rwlock_attr,
		PTHREAD_RWLOCK_PREFER_WRITER_NONRECURSIVE_NP);
#endif
	PTHREAD_RWLOCK_init(&g_ns_audit_by_tn.lock, &rwlock_attr);
	avltree_init(&g_ns_audit_by_tn.t, ns_audit_cmpf, 0);
	g_ns_audit_by_tn.cache_sz = 32767;
	g_ns_audit_by_tn.cache =
		gsh_calloc(g_ns_audit_by_tn.cache_sz, sizeof(struct avltree_node *));
	pthread_rwlockattr_destroy(&rwlock_attr);
}

struct ns_audit *get_ns_audit(const char *tenant, const char *namespace,
                              bool lookup_only)
{
	struct avltree_node *node = NULL;
	struct ns_audit *ret_au = NULL;
	struct ns_audit temp_key;  /* 用于查询AVL的临时 key */
	void **cache_slot = NULL;
	uint64_t hashval = 0;

	if (strlen(tenant) == 0 || strlen(namespace) == 0) {
		LogCrit(COMPONENT_DISPATCH, "the tenant or namespace is NULL");
		return NULL;
	}

	/* 1) 先构造一个 key 用于AVL查找 */
	memset(&temp_key, 0, sizeof(temp_key));
	strncpy(temp_key.tenant_name, tenant, sizeof(temp_key.tenant_name) - 1);
	strncpy(temp_key.namespace_name, namespace, sizeof(temp_key.namespace_name) - 1);


	/* 2) 计算哈希并去 cache 查找 */
	hashval = hash_tenant_namespace(tenant, namespace);
	uint32_t slot_idx = ns_cache_offsetof(&g_ns_audit_by_tn, hashval);

	pthread_rwlock_rdlock(&g_ns_audit_by_tn.lock);

	cache_slot = (void **)&(g_ns_audit_by_tn.cache[slot_idx]);
	node = (struct avltree_node *)atomic_fetch_voidptr(cache_slot);
	if (node) {
		/* 命中 cache，看是否真的匹配 */
		if (ns_audit_cmpf(&temp_key.node_k, node) == 0) {
			ret_au = avltree_container_of(node, struct ns_audit, node_k);
			goto out_found;
		}
	}


	/* 3) 在 AVL 树中查找 */
	node = avltree_lookup(&temp_key.node_k, &g_ns_audit_by_tn.t);
	if (node) {
		/* 找到，更新 cache */
		atomic_store_voidptr(cache_slot, node);
		ret_au = avltree_container_of(node, struct ns_audit, node_k);
		goto out_found;
	} else if (lookup_only) {
		/* 只查询不创建 */
		pthread_rwlock_unlock(&g_ns_audit_by_tn.lock);
		return NULL;
	}


	/* 4) 若查不到且允许创建(lookup_only==false)，则先解锁再加写锁 */
	pthread_rwlock_unlock(&g_ns_audit_by_tn.lock);

	/* 分配结构。注意可以使用 Ganesha 的gsh_calloc 或其他。 */
	struct ns_audit *new_au = gsh_calloc(1, sizeof(struct ns_audit));
	if (!new_au)
		return NULL;
	strncpy(new_au->tenant_name, tenant, sizeof(new_au->tenant_name) - 1);
	strncpy(new_au->namespace_name, namespace, sizeof(new_au->namespace_name) - 1);
	pthread_rwlock_init(&new_au->lock, NULL);
	new_au->refcnt = 1;

	/* 写锁再做一次插入检查，避免并发重复创建 */
	pthread_rwlock_wrlock(&g_ns_audit_by_tn.lock);
	node = avltree_insert(&new_au->node_k, &g_ns_audit_by_tn.t);
	if (node) {
		/* 有并发创建，使用已有的 */
		gsh_free(new_au); 
		ret_au = avltree_container_of(node, struct ns_audit, node_k);
	} else {
		/* 插入成功，更新 cache */
		atomic_store_voidptr(cache_slot, &new_au->node_k);
		ret_au = new_au;
	}

 out_found:
 	atomic_inc_int64_t(&ret_au->refcnt);
	pthread_rwlock_unlock(&g_ns_audit_by_tn.lock);

	return ret_au;
}

void put_ns_audit(struct ns_audit *au)
{
	int64_t new_refcnt;
	
	new_refcnt = atomic_dec_int64_t(&au->refcnt);
	
	if(new_refcnt == 0){
		size_t len_tent = strlen(au->tenant_name) + 1;
		size_t len_ns = strlen(au->namespace_name) + 1;
		
		char *tenant_name = NULL;
		char *namespace_name = NULL;
		tenant_name = alloca(len_tent);
		namespace_name = alloca(len_ns);

		if(tenant_name == NULL || namespace_name == NULL){
			LogEvent(COMPONENT_REPORT_ISM, "tenant_name or namespace_name malloc error, tenant_name:%s namespace_name:%s", 
				au->tenant_name, au->namespace_name);
			return;
		}

		memcpy(tenant_name, au->tenant_name, len_tent);
		memcpy(namespace_name, au->namespace_name, len_ns);
		
		int ret = remove_ns_audit(tenant_name, namespace_name);
		LogEvent(COMPONENT_FSAL, "remove_ns_audit, i:%d, tenant_name:%s namespace_name:%s", ret, tenant_name, namespace_name);
	}
}


int remove_ns_audit(const char *tenant, const char *namespace)
{
	struct avltree_node *node = NULL;
	struct avltree_node *cnode = NULL;
	struct ns_audit *au = NULL;
	struct ns_audit temp_key;
	int removed = 0;
	void **cache_slot;
	uint64_t hashval = 0;

	memset(&temp_key, 0, sizeof(temp_key));
	strncpy(temp_key.tenant_name, tenant, sizeof(temp_key.tenant_name) - 1);
	strncpy(temp_key.namespace_name, namespace, sizeof(temp_key.namespace_name) - 1);

	hashval = hash_tenant_namespace(tenant, namespace);
	uint32_t slot_idx = ns_cache_offsetof(&g_ns_audit_by_tn, hashval);

	pthread_rwlock_wrlock(&g_ns_audit_by_tn.lock);

	/* 1) 在 AVL 树里找到对应节点 */
	node = avltree_lookup(&temp_key.node_k, &g_ns_audit_by_tn.t);
	if (node) {
		au = avltree_container_of(node, struct ns_audit, node_k);
		/* refcnt > 0 说明还有人在用，返回 EBUSY */
		if (atomic_fetch_int64_t(&au->refcnt) > 0) {
			removed = EBUSY;
			goto out;
		}
		
		/* 若 cache 命中了该节点，也要清理掉 */
		cache_slot = (void **)&(g_ns_audit_by_tn.cache[slot_idx]);
		cnode = (struct avltree_node *)atomic_fetch_voidptr(cache_slot);
		if (node == cnode) {
			atomic_store_voidptr(cache_slot, NULL);
		}

		/* 从 AVL 中删除 */
		avltree_remove(node, &g_ns_audit_by_tn.t);
	} else {
		removed = ENOENT;  /* 没找到 */
	}

out:
	pthread_rwlock_unlock(&g_ns_audit_by_tn.lock);

	if (removed == 0 && au) {
		/* 真正释放前，如果需要，还有其它资源释放可在这里做 */
		pthread_rwlock_destroy(&au->lock);
		gsh_free(au);
	}
	return removed;
}


