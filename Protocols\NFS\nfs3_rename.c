/*
 * vim:noexpandtab:shiftwidth=8:tabstop=8:
 *
 * Copyright CEA/DAM/DIF  (2008)
 * contributeur : <PERSON>   <EMAIL>
 *                Thomas LEIBOVICI  <EMAIL>
 *
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * ---------------------------------------
 */

/**
 * @file  nfs3_rename.c
 * @brief Everything you need for NFSv3 RENAME
 */
#include "config.h"
#include <stdio.h>
#include <string.h>
#include <pthread.h>
#include <fcntl.h>
#include <sys/file.h>
#include "hashtable.h"
#include "log.h"
#include "fsal.h"
#include "nfs_core.h"
#include "nfs_exports.h"
#include "nfs_proto_functions.h"
#include "nfs_convert.h"
#include "nfs_proto_tools.h"
#include "client_mgr.h"

/**
 *
 * @brief The NFSPROC3_RENAME
 *
 * Implements the NFSPROC3_RENAME function.
 *
 * @param[in]  arg     NFS argument union
 * @param[in]  req     SVC request related to this call
 * @param[out] res     Structure to contain the result of the call
 *
 * @retval NFS_REQ_OK if successful
 * @retval NFS_REQ_DROP if failed but retryable
 * @retval NFS_REQ_FAILED if failed and not retryable
 *
 */

int nfs3_rename(nfs_arg_t *arg, struct svc_req *req, nfs_res_t *res)
{
	const char *entry_name = arg->arg_rename3.from.name;
	const char *new_entry_name = arg->arg_rename3.to.name;
	struct fsal_obj_handle *parent_obj = NULL;
	struct fsal_obj_handle *new_parent_obj = NULL;
	fsal_status_t fsal_status;
	int to_exportid = 0;
	int from_exportid = 0;
	int rc = NFS_REQ_OK;
	RENAME3resfail *resfail = &res->res_rename3.RENAME3res_u.resfail;
	RENAME3resok *resok = &res->res_rename3.RENAME3res_u.resok;

	//add by bijingqiang for disable rm and rename 
	//date:2016/04/13
	struct export_perms *p_perms  = &op_ctx->export_perms;
	if ((p_perms->options & EXPORT_OPTION_DISABLE_RM ) != 0){ 
		res->res_remove3.status = NFS3ERR_PERM;
		rc = NFS_REQ_OK;
		LogAudit_NFS(AUDIT_OP_RENAME, op_ctx->audit_flags,
			" operation_type: rename | client_ip: %s | server_ip: %s | user: %d | dir_inodeno: 0 | new_dir_inodeno:0 | object_name: %s | new_object_name: %s | operation_result: 1 | err_code: %d | details: disabled rm and rename | information: nfs3_rename",
			op_ctx->srcstr, op_ctx->deststr, op_ctx->creds.caller_uid, entry_name ? entry_name : "", new_entry_name ? new_entry_name : "", res->res_remove3.status);
		goto ro_out ;	 
	}

	pre_op_attr pre_parent = {
		.attributes_follow = false
	};
	pre_op_attr pre_new_parent = {
		.attributes_follow = false
	};

	LogNFS3_Operation2(COMPONENT_NFSPROTO, req,
			   &arg->arg_rename3.from.dir, entry_name,
			   &arg->arg_rename3.to.dir, new_entry_name);

	/* to avoid setting it on each error case */
	resfail->fromdir_wcc.before.attributes_follow = FALSE;
	resfail->fromdir_wcc.after.attributes_follow = FALSE;
	resfail->todir_wcc.before.attributes_follow = FALSE;
	resfail->todir_wcc.after.attributes_follow = FALSE;

	/* Get the exportids for the two handles. */
	to_exportid = nfs3_FhandleToExportId(&(arg->arg_rename3.to.dir));
	from_exportid = nfs3_FhandleToExportId(&(arg->arg_rename3.from.dir));

	/* Validate the to_exportid */
	if (to_exportid < 0 || from_exportid < 0) {
		LogInfo(COMPONENT_DISPATCH,
			"NFS%d RENAME Request from client %s has badly formed handle for to dir",
			req->rq_msg.cb_vers,
			op_ctx->client
				? op_ctx->client->hostaddr_str
				: "unknown client");

		/* Bad handle, report to client */
		res->res_rename3.status = NFS3ERR_BADHANDLE;
		LogAudit_NFS(AUDIT_OP_RENAME, op_ctx->audit_flags,
			" operation_type: rename | client_ip: %s | server_ip: %s | user: %d | dir_inodeno: 0 | new_dir_inodeno:0 | object_name: %s | new_object_name: %s | operation_result: 1 | err_code: %d | details: bad handel | information: nfs3_rename",
			op_ctx->srcstr, op_ctx->deststr, op_ctx->creds.caller_uid, entry_name ? entry_name : "", new_entry_name ? new_entry_name : "", res->res_remove3.status);
		goto out;
	}

	/* Both objects have to be in the same filesystem */
	if (to_exportid != from_exportid) {
		res->res_rename3.status = NFS3ERR_XDEV;
		LogAudit_NFS(AUDIT_OP_RENAME, op_ctx->audit_flags,
			" operation_type: rename | client_ip: %s | server_ip: %s | user: %d | dir_inodeno: 0 | new_dir_inodeno:0 | object_name: %s | new_object_name: %s | operation_result: 1 | err_code: %d | details: confilct | information: nfs3_rename",
			op_ctx->srcstr, op_ctx->deststr, op_ctx->creds.caller_uid, entry_name ? entry_name : "", new_entry_name ? new_entry_name : "", res->res_remove3.status);
		goto out;
	}

	/* Convert fromdir file handle into a FSAL obj */
	parent_obj = nfs3_FhandleToCache(&arg->arg_rename3.from.dir,
					   &res->res_rename3.status,
					   &rc);

	if (parent_obj == NULL) {
		/* Status and rc have been set by nfs3_FhandleToCache */
		LogAudit_NFS(AUDIT_OP_RENAME, op_ctx->audit_flags,
			" operation_type: rename | client_ip: %s | server_ip: %s | user: %d | dir_inodeno: 0 | new_dir_inodeno:0 | object_name: %s | new_object_name: %s | operation_result: 1 | err_code: %d | details: parent_obj null | information: nfs3_rename",
			op_ctx->srcstr, op_ctx->deststr, op_ctx->creds.caller_uid, entry_name ? entry_name : "", new_entry_name ? new_entry_name : "", nfs3_Errno_status(fsal_status));
		goto out;
	}

	nfs_SetPreOpAttr(parent_obj, &pre_parent);

	/* Convert todir file handle into a FSAL obj */
	new_parent_obj = nfs3_FhandleToCache(&arg->arg_rename3.to.dir,
					       &res->res_rename3.status,
					       &rc);

	if (new_parent_obj == NULL) {
		/* Status and rc have been set by nfs3_FhandleToCache */
		LogAudit_NFS(AUDIT_OP_RENAME, op_ctx->audit_flags,
			" operation_type: rename | client_ip: %s | server_ip: %s | user: %d | dir_inodeno: %lu | new_dir_inodeno:0 | object_name: %s | new_object_name: %s | operation_result: 1 | err_code: %d | details: new_parent_obj null | information: nfs3_rename",
			op_ctx->srcstr, op_ctx->deststr, op_ctx->creds.caller_uid, parent_obj->fileid, entry_name ? entry_name : "", new_entry_name ? new_entry_name : "", res->res_rename3.status);
		goto out;
	}

	nfs_SetPreOpAttr(new_parent_obj, &pre_new_parent);

	if (entry_name == NULL || *entry_name == '\0' || new_entry_name == NULL
	    || *new_entry_name == '\0') {
		fsal_status = fsalstat(ERR_FSAL_INVAL, 0);
		LogAudit_NFS(AUDIT_OP_RENAME, op_ctx->audit_flags,
			" operation_type: rename | client_ip: %s | server_ip: %s | user: %d | dir_inodeno: %lu | new_dir_inodeno: %lu | object_name: %s | new_object_name: %s | operation_result: 1 | err_code: %d | details: invalid name | information: nfs3_rename",
			op_ctx->srcstr, op_ctx->deststr, op_ctx->creds.caller_uid, parent_obj->fileid, new_parent_obj->fileid, entry_name ? entry_name : "", new_entry_name ? new_entry_name : "", nfs3_Errno_status(fsal_status));
		goto out_fail;
	}

	fsal_status = fsal_rename(parent_obj, entry_name, new_parent_obj,
				  new_entry_name);

	if (FSAL_IS_ERROR(fsal_status)){
		LogAudit_NFS(AUDIT_OP_RENAME, op_ctx->audit_flags,
			" operation_type: rename | client_ip: %s | server_ip: %s | user: %d | dir_inodeno: %lu | new_dir_inodeno: %lu | object_name: %s | new_object_name: %s | operation_result: 1 | err_code: %d | details: fsal_rename failed | information: nfs3_rename",
			op_ctx->srcstr, op_ctx->deststr, op_ctx->creds.caller_uid, parent_obj->fileid, new_parent_obj->fileid, entry_name ? entry_name : "", new_entry_name ? new_entry_name : "", nfs3_Errno_status(fsal_status));
		goto out_fail;
	}

	res->res_rename3.status = NFS3_OK;

	nfs_SetWccData(&pre_parent, parent_obj, &resok->fromdir_wcc);

	nfs_SetWccData(&pre_new_parent, new_parent_obj, &resok->todir_wcc);

	rc = NFS_REQ_OK;
	LogAudit_NFS(AUDIT_OP_RENAME, op_ctx->audit_flags,
		" operation_type: rename | client_ip: %s | server_ip: %s | user: %d | dir_inodeno: %lu | new_dir_inodeno: %lu | object_name: %s | new_object_name: %s | operation_result: 0 | information: nfs3_rename",
		op_ctx->srcstr, op_ctx->deststr, op_ctx->creds.caller_uid, parent_obj->fileid, new_parent_obj->fileid, entry_name ? entry_name : "", new_entry_name ? new_entry_name : "");
	goto out;

 out_fail:
	res->res_rename3.status = nfs3_Errno_status(fsal_status);

	nfs_SetWccData(&pre_parent, parent_obj, &resfail->fromdir_wcc);

	nfs_SetWccData(&pre_new_parent, new_parent_obj, &resfail->todir_wcc);

	/* If we are here, there was an error */
	if (nfs_RetryableError(fsal_status.major))
		rc = NFS_REQ_DROP;

 out:
	if (parent_obj)
		parent_obj->obj_ops->put_ref(parent_obj);

	if (new_parent_obj)
		new_parent_obj->obj_ops->put_ref(new_parent_obj);

	return rc;

 ro_out:
	
	LogFullDebug(COMPONENT_NFSPROTO,"client_ip: %s | rename | name: %s | inode: 0 | result: not_ok(NFS3ERR_PERM)",
				   op_ctx->client->hostaddr_str,
				   entry_name);
	return rc ;

}

/**
 * @brief Free the result structure allocated for nfs3_rename.
 *
 * This function frees the result structure allocated for nfs3_rename.
 *
 * @param[in,out] res Result structure
 *
 */
void nfs3_rename_free(nfs_res_t *res)
{
	/* Nothing to do here */
}
